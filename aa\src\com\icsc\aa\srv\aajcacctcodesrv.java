package com.icsc.aa.srv;

import com.icsc.dpms.de.*;
import com.icsc.dpms.ds.*;
import com.icsc.aa.api.*;
//import com.icsc.aa.dei.aajcCommondei;

public class aajcacctcodesrv {

    private dsjccom dsCom;

    private dejc318 de318;

    //private String AppId;
    
    public aajcacctcodesrv(dsjccom dsCom) throws Exception {
        this.dsCom = dsCom;
        this.de318 = new dejc318(dsCom, "AAJCACCTCODESRV");
    }

    public aajcAcctcodeapi getAAClass() throws Exception{
        aajcAcctcodeapi aajcacctapi = null;
	    try {
	        Class[] aaAry = new Class[1];
	        aaAry[0] = this.dsCom.getClass();
	        Object[] aaObject = new Object[1];
	        aaObject[0] = this.dsCom;
	        //Class aa = null;
	        //aajcCommondei aaComdei = new aajcCommondei(dsCom);
	        //String apType = aaComdei.getDE323Desc("AA","AASYSAP",2);
	        Class aaapi = Class.forName("com.icsc.aa.dei.aajcAcctcodedei");
	        aajcacctapi = (aajcAcctcodeapi)aaapi.getConstructor(aaAry).newInstance(aaObject);
        } catch (Exception e) {
            de318.logs("AAJCACCTCODESRV","getAAClass Exception:" + e.getMessage(),e);
            throw e;
        } 
        return aajcacctapi;
    }

}