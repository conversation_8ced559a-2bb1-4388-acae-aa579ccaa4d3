package com.icsc.aa.api;

import java.math.BigDecimal;
import java.util.Map;

import com.icsc.aa.dei.aajcCommondei;
import com.icsc.dpms.de.dejcQueryDAO;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.gp.validate.gpjiBaseInvalidCheck;

public class aajcApiCheckGP implements gpjiBaseInvalidCheck {
	public final static String CLASS_VERSION = "$Id: aajcApiCheckGP.java,v 1.1 2018/04/24 03:10:55 I26345 Exp $";

	public boolean check(dsjccom dsCom, String id) throws Exception {
		dejcQueryDAO deQuery = new dejcQueryDAO(dsCom);
	    		
		if(new aajcCommondei(dsCom).getAaParameter("", "isCheckGP").trim().equals("Y")) {
			StringBuffer sqlStr = new StringBuffer();
			sqlStr.append("select count(*) as NUM from db.tbaat1 where");
			sqlStr.append(" idCode='" + id + "' or refNo='" + id + "' or aid03Code='" + id + "' or aid04Code='" + id + "'");
			
			Map aaMap = deQuery.getData(sqlStr.toString());
			if(new BigDecimal(aaMap.get("NUM").toString()).compareTo(new BigDecimal("0")) != 0) {
				return false;
			}
		}
		return true;
	}

}

