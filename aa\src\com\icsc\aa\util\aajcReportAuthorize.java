/*
 * Created on 2009-9-27
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package com.icsc.aa.util;

import com.icsc.dpms.ds.dsjcagc;
import com.icsc.dpms.ds.dsjccom;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public class aajcReportAuthorize {

    String pageId = null;

    dsjccom dsCom = null;

    public aajcReportAuthorize(String pageId, dsjccom dsCom) {
        this.pageId = pageId;
        this.dsCom = dsCom;
    }

    public boolean checkAruhorize(String reportId) {
        dsjcagc dsagc = new dsjcagc(dsCom);
        boolean isAuthorize = false;
        try {
            if (dsCom.user.ID.equals("ICSCAA")) {
                isAuthorize = true;
            } else {
                isAuthorize = dsagc.check(dsCom, pageId, reportId, dsCom.user.ID);
            }
            return isAuthorize;
        } catch (Exception e) {
            return false;
        }
    }
}
