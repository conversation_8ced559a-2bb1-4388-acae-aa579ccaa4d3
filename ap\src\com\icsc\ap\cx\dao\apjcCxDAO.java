package com.icsc.ap.cx.dao;

import java.sql.Connection;

import com.icsc.dpms.de.dejcCommonDAO;
import com.icsc.dpms.ds.dsjccom;

/**
 * Common DAO usage for cx
 * <AUTHOR> I27640 $
 * @since $Date: 2016/04/26 09:10:15 $
 * @version $Revision: 1.1 $
 */
public class apjcCxDAO extends dejcCommonDAO {
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2016/04/26 09:10:15 $ $Author: I27640 $";

	public apjcCxDAO(dsjccom paramdsjccom, Connection paramConnection) {
		super(paramdsjccom, paramConnection);
	}
	
	public int executeUpdate(String sql) throws Exception {
		return super.executeUpdate(sql);
	}
}
