<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import="com.icsc.dpms.de.*" %>
<%@ page import="com.icsc.dpms.de.web.*" %>
<%@ page import="com.icsc.dpms.de.structs.*" %>
<%@ page import="com.icsc.dpms.de.structs.web.*" %>
<%@ page import="com.icsc.mf.util.*" %> 
<%! public static final String _AppId = "MRJJLAYERID"; %>
<%@ taglib uri="/WEB-INF/tld/deTagLib.tld" prefix="de" %>
<%@ include file="../../jsp/dzjjmain.jsp" %>

<%  
	StringBuffer sql = new StringBuffer() ;

	String psrno = null == request.getParameter("psrno")? "": request.getParameter("psrno");
	String compId = null == request.getParameter("compId")? "": request.getParameter("compId");
	//out.println("compId="+compId);
	compId = null==compId ||"".equals(compId)? mfjcds01.getCompId(_dsCom):compId;
			  
	sql.append("select a.matrlNo,c.cNmDesc, a.locNo,a.lotNo,a.endQty, a.matrlGrade from db.TBMFVI12 a join db.tbmfV006 b on (a.compId=b.compId and a.matrlNo=b.matrlNo) join db.tbmfv002 c on (a.matrlno=c.matrlNo) where b.compId = '" + compId +"'  and b.psrno = '" + psrno +"'and a.endQty >0 ");
	 
	sql.append(" order by a.matrlNo,a.locNo,a.lotNo,a.matrlGrade "); 

    dejcQryGrid g = new dejcQryGrid(_de300, sql.toString(), 15) ;
    g.setImgBarType() ;
    
%>
<form name="form1" method="post" action="/erp/mr/jsp/mrjjLayerIdList.jsp"  >
<table>
    <tr>
    	<td>
    		<%= g.printNavigator() %>
    	</td>
    </tr>
</table>

<table width=100% class=function-bar id="t1">
	<%=g.makeHeader("class='subsys-title' ")%>
    <td class="subsys-title" width="4%">序号</td>
    <td >料号</td> 
    <td >名称</td> 
    <td >储位</td> 
    <td >批号</td> 
    <td >库存量</td> 
    </tr>
<%
    dejcQryGridList list = g.getList() ;
    for(int i=0; i<list.size(); i++) {
    	String style=(i%2==0)?"light-bg-left":"deep-bg-left" ;
    	dejcQryRow r = list.row(i) ;
%>
    <tr class="<%=style%>" style="cursor:hand" onclick="query('<%=r.getString("matrlNo")%>','<%=r.getString("locNo")%>','<%=r.getString("lotNo")%>','<%=r.get("endQty", "#,##0.####")%>')">
	   
		<td width="4%"><%=r.getSequenceNo()%></td> 
	    <td ><%=r.get("matrlNo", "String")%></td>
	    <td ><%=r.get("cNmDesc", "String")%></td>
	    <td ><%=r.get("locNo", "String")%></td>
	    <td ><%=r.get("lotNo", "String")%></td> 
	    <td  align='right'><%=r.get("endQty", "#,##0.####")%></td>
    </tr>
<%}%>
</table>
<input type="hidden" class="msg" id="msg">
</form>

</body>
</html>
<de:script src="dejtab06a"/>
<de:script src="dejtTableCursor"/>
<script>
<%-- submit master 的 frame  --%>
var obj = new deScrollTable("t1"); 
obj.setNeedSort(false);
obj.setEvent(false);
obj.setHeight(78);
obj.draw();

function query(matrlNo,locNo,lotNo,endQty) {
    form1.submit() ;
    parent.resetOpener(matrlNo,locNo,lotNo,endQty) ;
}

var t = new tableRoll("t1") ;

// 按下 navigator bar 的 button 时会先触发此 function
function deGridButtonClick() {
//    parent.document.all("column").src="" ;
}
window.name="mrjjLayerIdListID";
</script>