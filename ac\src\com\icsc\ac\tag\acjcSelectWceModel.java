package com.icsc.ac.tag;

import java.util.Map;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.dpms.ds.dsjcst0;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.util.zafctool;

public class acjcSelectWceModel {
	public static String getSelectOptions(dsjccom dsCom, String target, String dateym) throws Exception {

		StringBuffer sb = new StringBuffer();
		try {
			StringBuffer sqlstr = new StringBuffer();
			sqlstr.append(" select yyyy,srlNo,srlNoName ");
			sqlstr.append(" from db.tbacwcecname where yyyy='" + dateym.substring(0, 4) + "' and compid='"
					+ dsjcst0.getCompanyId() + "' ");
			zafcCommonDAO dao = new zafcCommonDAO(dsCom);
			Map[] selectData = dao.queryVOs(sqlstr.toString());

			for (int i = 0; i < selectData.length; i++) {
				Map mp = selectData[i];
				String value0 = zafctool.trim(mp.get("YYYY"));
				String value1 = zafctool.trim(mp.get("SRLNO"));
				String value2 = zafctool.trim(mp.get("SRLNONAME"));
				String chk = target.equals(value1) ? "selected" : "";
				sb.append("<option value='" + value1 + "' " + chk + " >");
				sb.append(value0 + "-" + value1 + " " + value2);
				sb.append("</option>").append(" \r\n");
			}
		} catch (Exception e) {
			sb.append("<option>" + e.getMessage() + "</option>");
		}
		return sb.toString();
	}
}