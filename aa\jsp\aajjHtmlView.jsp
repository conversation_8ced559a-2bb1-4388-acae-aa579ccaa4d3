<%@ page contentType = "text/html;charset=GBK"%>

<%@ page import="com.icsc.dpms.de.*" %>
<%@ page import="com.icsc.dpms.de.structs.*" %>
<%@ page import="net.sf.jasperreports.engine.JasperPrint" %>
<%@ page import="net.sf.jasperreports.engine.JRExporterParameter" %>
<%@ page import="net.sf.jasperreports.engine.JRPrintPage" %>
<%@ page import="net.sf.jasperreports.engine.JRPrintElement" %>
<%@ page import="net.sf.jasperreports.engine.export.*" %>
<%@ page import="net.sf.jasperreports.engine.util.JRLoader" %>
<%@ page import="net.sf.jasperreports.j2ee.servlets.ImageServlet" %>


<%
String htmlUrl = "";

String REPORT_URL = request.getParameter("REPORT_URL");
if(REPORT_URL==null){
	dejcWebInfoOut infoOut = (dejcWebInfoOut)request.getAttribute("infoOut") ;
	htmlUrl = infoOut.getParameter("htmlUrl");
%>
<%! public static final String _AppId = "AAJJPRINT"; %>

<%@ include file="../../jsp/dzjjmain.jsp" %>
<script>
window.location.href = "<%=htmlUrl%>";
</script>
</body>
</html>

<% }else{
  REPORT_URL =   REPORT_URL.substring(5,REPORT_URL.length());
  JasperPrint jasperPrint = (JasperPrint) JRLoader.loadObject(REPORT_URL);
  
  String isPrintBackgroundImg = request.getParameter("isPrintBackgroundImg");
  if(isPrintBackgroundImg!=null && isPrintBackgroundImg.equals("N")){
      System.out.println("eeeeeeeeeeeeeeeeeeeeee");
      List<JRPrintPage> jrPrintPageList = jasperPrint.getPages();
      for (JRPrintPage jrPrintPage : jrPrintPageList) {
          List elements = jrPrintPage.getElements();

          List<JRPrintElement> list = new Vector<JRPrintElement>();
          for (Object obj : elements) {
              JRPrintElement element = (JRPrintElement) obj;

              String key = element.getKey();
              if (!key.startsWith("imageNotPrint")) {
                  list.add(element);
              }
          }
          jrPrintPage.setElements(list);
      }
  }

  JRHtmlExporter jrHtmlExporter=new JRHtmlExporter();

  Map imageMap = new  HashMap();
  request.getSession().setAttribute("IMAGES_MAP" , imageMap);

  StringBuffer sb = new StringBuffer();
  request.getSession().setAttribute(ImageServlet.DEFAULT_JASPER_PRINT_SESSION_ATTRIBUTE,jasperPrint); 
  jrHtmlExporter.setParameter(JRExporterParameter.JASPER_PRINT,jasperPrint);
  jrHtmlExporter.setParameter(JRExporterParameter.OUTPUT_STRING_BUFFER, sb);  
  jrHtmlExporter.setParameter(JRHtmlExporterParameter.IMAGES_MAP,imageMap);
  //jrHtmlExporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME,htmlUrl);
  //jrHtmlExporter.setParameter(JRHtmlExporterParameter.BETWEEN_PAGES_HTML, "<h1>"); 
  jrHtmlExporter.setParameter(JRHtmlExporterParameter.IMAGES_URI,"../../jrImg?image="); 
  jrHtmlExporter.setParameter(JRHtmlExporterParameter.HTML_HEADER,"");
  jrHtmlExporter.setParameter(JRHtmlExporterParameter.HTML_FOOTER, "");  
  
  jrHtmlExporter.exportReport();  
%>
<html>
<head>
  <title></title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE">
  <style type="text/css">
    a {
      text-decoration: none
    }
    table{
       background-image:url(/erp/images/aa/aawzKPJ101S.jpg);
       background-size:100% 50%;
       background-repeat:repeat;
       margin-left:auto;
       margin-right:auto;
       width:1000px;
    }
  </style>
</head>
<body>
<%=sb.toString()%>
</body>
</html>
<%}%>



