package com.icsc.ad.bp;

import java.math.BigDecimal;

public class adjcDistRptVO {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:35:38 $";

	private String srcCC;

	private String srcCCDesc;

	private String wce;

	private String wceDesc;

	private String pd;

	private String pdDesc;

	private String logic;

	private String logicName;

	private String formId;

	private BigDecimal sharePercent;

	private BigDecimal shareWeight;

	private BigDecimal inputAmt;

	public String getLogic() {
		return logic;
	}

	public void setLogic(String logic) {
		this.logic = logic;
	}

	public String getFormId() {
		return formId;
	}

	public void setFormId(String formId) {
		this.formId = formId;
	}

	public String getLogicName() {
		return logicName;
	}

	public void setLogicName(String logicName) {
		this.logicName = logicName;
	}

	public BigDecimal getInputAmt() {
		return inputAmt;
	}

	public void setInputAmt(BigDecimal inputAmt) {
		this.inputAmt = inputAmt;
	}

	public String getPd() {
		return pd;
	}

	public void setPd(String pd) {
		this.pd = pd;
	}

	public String getPdDesc() {
		return pdDesc;
	}

	public void setPdDesc(String pdDesc) {
		this.pdDesc = pdDesc;
	}

	public BigDecimal getSharePercent() {
		return sharePercent;
	}

	public void setSharePercent(BigDecimal sharePercent) {
		this.sharePercent = sharePercent;
	}

	public BigDecimal getShareWeight() {
		return shareWeight;
	}

	public void setShareWeight(BigDecimal shareWeight) {
		this.shareWeight = shareWeight;
	}

	public String getSrcCC() {
		return srcCC;
	}

	public void setSrcCC(String srcCC) {
		this.srcCC = srcCC;
	}

	public String getSrcCCDesc() {
		return srcCCDesc;
	}

	public void setSrcCCDesc(String srcCCDesc) {
		this.srcCCDesc = srcCCDesc;
	}

	public String getWce() {
		return wce;
	}

	public void setWce(String wce) {
		this.wce = wce;
	}

	public String getWceDesc() {
		return wceDesc;
	}

	public void setWceDesc(String wceDesc) {
		this.wceDesc = wceDesc;
	}

	// for ACP6 use============================

	private String dpdIn;

	public String getDpdIn() {
		return dpdIn == null ? "" : dpdIn;
	}

	public void setDpdIn(String dpdIn) {
		this.dpdIn = dpdIn;
	}

	private String fromCC;

	public String getFromCC() {
		return fromCC == null ? "" : fromCC;
	}

	public void setFromCC(String fromCC) {
		this.fromCC = fromCC;
	}
}
