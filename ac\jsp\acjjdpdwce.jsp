<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ACJJDPDWCE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = zafctool.trim(request.getParameter("compId"));
	String WA = zafctool.trim(request.getParameter("WA"));
	//String CC = zafctool.trim(request.getParameter("CC"));
	String PD = zafctool.trim(request.getParameter("PD"));
	String CC_F = zafctool.trim(request.getParameter("CC_F"));
	String PD_F = zafctool.trim(request.getParameter("PD_F"));

%>
<%--
<textarea name="paraa" style="display:none">
	<select name="<%=CC_F%>" >
	<%=ipjcWorkArea.getCCOptions(_dsCom,compId,WA,CC)%>
	</select>
</textarea> 
--%>
<textarea name="parab" style="display:none">
	<select name="<%=PD_F %>" >
	<%=ipjcWorkArea.getPDOptions(_dsCom,compId,WA,PD)%>
	</select>
</textarea> 

<script>
	if(parent){
		parent.document.getElementById("<%=CC_F%>").innerHTML = document.all("paraa").value;
		parent.document.getElementById("<%=PD_F%>").innerHTML = document.all("parab").value;
	}
	  

</script>