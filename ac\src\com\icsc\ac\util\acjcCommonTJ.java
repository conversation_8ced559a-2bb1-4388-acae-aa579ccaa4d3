package com.icsc.ac.util;

import java.math.*;
import java.util.*;

import com.icsc.ac.dao.acjcb2TJDAO;
import com.icsc.ac.dao.acjcb2TJVO;
import com.icsc.ac.dao.acjcb5TJDAO;
import com.icsc.ac.dao.acjcb5TJVO;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.bp.zafcINI;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.dao.zafcrdDAO;
import com.icsc.facc.zaf.dao.zafcrdVO;
import com.icsc.facc.zaf.util.zafctool;

public class acjcCommonTJ {
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2024/05/28 03:16:36 $";

	private String acctItems;
	
	private Map wceBuff;
	private Map dpdData;
	
	private dsjccom dsCom;
	private acjcb2TJDAO aeb2DAO;
	private zafcCommonDAO comDAO;
	
	public acjcCommonTJ(dsjccom dsCom) {
		this.dsCom = dsCom;
		aeb2DAO = new acjcb2TJDAO(dsCom);
		comDAO = new zafcCommonDAO(dsCom);
		acctItems = zafcINI.getSetting(dsCom, "ipjcQualifyProd.acctItems");
	}
	
	public boolean chkWce(String compId, String wce) throws Exception {
		if (wceBuff == null)
			wceBuff = new HashMap();
		
		String key = compId + wce;
		if (wceBuff.containsKey(key))
			return true;
		
		acjcb2TJVO vo = aeb2DAO.findByPK(compId, wce);
		if (vo != null) 
			wceBuff.put(key, vo);
		return true;
	}
	
	public acjcb2TJVO getWceData(String compId, String wce) throws Exception {
		if (wceBuff == null)
			wceBuff = new HashMap();
		
		String key = compId + wce;
		if (!wceBuff.containsKey(key)) {
			acjcb2TJVO vo = aeb2DAO.findByPK(compId, wce);
			if (vo != null) {
				wceBuff.put(key, vo);
			} 
		}
		return (acjcb2TJVO) wceBuff.get(key);
	}
	
	public String getWceDesc(String compId, String wce) throws Exception {
		if (wceBuff == null)
			wceBuff = new HashMap();
		
		String key = compId + wce;
		if (wceBuff.containsKey(key))
			return ((acjcb2TJVO) wceBuff.get(key)).getWceDesc();
		
		acjcb2TJVO vo = aeb2DAO.findByPK(compId, wce);
		if (vo != null) {
			wceBuff.put(key, vo);
			return vo.getWceDesc();
		}
		return "N/A";
	}
	
	public String[] getDpdData(String compId, String yyyy, String dpd) {
		if (dpdData == null) {
			try {
				dpdData = new HashMap();
				
				String sql = "Select dpd, dpdDesc, pdUnit From DB" + ".TBAEPDBASE Where compId = '" + compId + "' and fiscalYear = '" + yyyy + "' ";
				Map[] data = comDAO.queryVOs(sql);
				
				for (int i = 0; i < data.length; i++) {
					String cDPD = (String) data[i].get("DPD");
					dpdData.put(compId+yyyy+cDPD, new String[]{(String) data[i].get("DPDDESC"), (String) data[i].get("PDUNIT")}); 
				}				
			} catch (Exception e) {
				return new String[] { "", "" };
			}	
		}
		return (String[]) dpdData.get(compId+yyyy+dpd);
	}
	
	public String[] getWAData(String compId, String ps,String wa) {
		try {
			acjcb5TJVO b5VO = new acjcb5TJDAO(dsCom).findByPK(compId, zafctool.getWYear(), ps,wa);
			if (b5VO==null)
				return new String[] { "", "" };
			return new String[] {b5VO.getWorkAreaDesc(), b5VO.getUnit()};
		} catch (Exception e) {
			return new String[] { "", "" };
		}
	}
	
	public String[] getFacData(String compId, String fac) {
		try {
			zafcrdVO zafVO = new zafcrdDAO(dsCom).findByIA(compId, "12011202", fac);
			if (zafVO == null)
				return new String[] { "", "" };
			return new String[] { zafVO.getDataA(), zafVO.getDataC() };
		} catch (Exception e) {
			return new String[] { "", "" };
		}
	}
	
	public BigDecimal[] qryProdSumQty(String verId, String compId, String fromYM, String toYM, String dpd, String workAreas, String pds, String alloyNos, String specs) throws Exception {
		BigDecimal[] rslt = new BigDecimal[]{zafctool.ZERO, zafctool.ZERO};
		
		String sql = "";
		if (dpd.length() > 0) {
			sql = this.qryPdQtyByDpd(verId, compId, fromYM, toYM, dpd, zafctool.chgDotToInSql(acctItems, ","));
		} else {
			sql = this.qryPdQtyByCond(verId, compId, fromYM, toYM, workAreas, pds, alloyNos, specs, zafctool.chgDotToInSql(acctItems, ","));
		}
		Map[] mps = comDAO.queryVOs(sql);	
		
		for (int i = 0; i < mps.length; i++) {
			String acctItem = zafctool.trim(mps[i].get("ACCTITEM"));
			BigDecimal qty = zafctool.parseBD(mps[i].get("QTY"));

			if (acctItem.equals("11")) {
				rslt[0] = rslt[0].add(qty);
			} else {
				if (acctItem.compareTo("40") < 0) {
					rslt[1] = rslt[1].add(qty);
				} else {
					rslt[1] = rslt[1].subtract(qty);
				}
			}
		}
		return rslt;
	}
	
	private String qryPdQtyByCond(String verId, String compId, String fromYM, String toYM, String workAreas, String pds, String alloyNos, String specs, String acctItems) {
		String sql = "Select acctItem, sum(QTY) QTY From (" + 
			" Select acctItem, dpd, sum(invWgt) QTY From db"+".tbaemcc " +
			" Where verId = '" + verId + "' and compid = '" + compId + "' and dataDate between '" + fromYM + "' and '" + toYM + "' and acctItem in (" + acctItems + ") " + 
			" Group By acctItem, dpd) a " + 
			" Where a.dpd in (Select dpd From DB"+".TBAEPDBASE Where compId = '" + compId + "' and fiscalYear = '" + fromYM.substring(0, 4) + "' "; 
			if (pds.length() > 0) {
				sql += " and prodCode in (" + pds + ") ";
			} else if (workAreas.length() > 0) { 
				sql += " and workArea in (" + workAreas + ") ";
			} 
			if (alloyNos.length() > 0){
				sql += " and steelGrade in (" + alloyNos + ") ";
			}	
			if (specs.length() > 0){
				sql += " and DIMENSIONS in (" + specs + ") ";
			}
			sql += ") ";
			sql += " Group By acctItem ";
		return sql;
	}
	
	private String qryPdQtyByDpd(String verId, String compId, String fromYM, String toYM, String dpd, String acctItems) {
		String sql = "Select acctItem, sum(QTY) QTY From (" + 
		" Select acctItem, dpd, sum(invWgt) QTY From db"+".tbaemcc " +
		" Where verId = '" + verId + "' and compid = '" + compId + "' and dataDate between '" + fromYM + "' and '" + toYM + "' and acctItem in (" + acctItems + ") " + 
		" Group By acctItem, dpd) a " + 
		" Where a.dpd = '" + dpd + "' ";
		sql += " Group By acctItem ";
	return sql;
	}
}
