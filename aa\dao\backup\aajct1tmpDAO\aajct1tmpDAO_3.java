/*----------------------------------------------------------------------------*/
/* aajct1tmpDAO		DAOTool Ver 10.0112 (INPUT FILE VERSION:1.0)
/*----------------------------------------------------------------------------*/
/* author : I27368
/* system : 籵頗數奪燴炵苀(AA)
/* target : 痐隴牉煦翹賜醱紫
/* create : 104/08/20
/* update : 104/08/20
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao;

import java.sql.*;
import java.text.*;
import java.util.*;
import java.math.* ;
import com.icsc.dpms.de.*;
import com.icsc.dpms.de.sql.*;
import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;
import com.icsc.aa.util.*  ;

/**
 * 痐隴牉煦翹賜醱紫 DAO.
 * <pre>
 * Table Name        : DB.TBAAT1TMP
 * Table Description : 痐隴牉煦翹賜醱紫
 * Value Object Name : aajct1tmpVO
 * </pre>
 * @version $Id: aajct1tmpDAO_3.java,v 1.1 2015/09/30 05:13:14 I27368 Exp $
 * @since aajct1tmpVO - 104/08/20
 * <AUTHOR>
 */
public class aajct1tmpDAO extends dejcCommonDAO {
	public final static String AppId = "AAJCT1TMPDAO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:14 $";

/*----------------------------------------------------------------------------*/
/* Creates new aajct1tmpDAO
/*----------------------------------------------------------------------------*/

	/**
	 * 建構子
	 * @param dsCom - 共用元件
	 * @since 104/08/20
	 */
	public aajct1tmpDAO(dsjccom dsCom) {
		super(dsCom, AppId);
	}
	/**
	 * 建構子
	 * @param dsCom - 共用元件
	 * @param con - 交易連接緒
	 * @since 104/08/20
	 */
	public aajct1tmpDAO(dsjccom dsCom, Connection con) {
		super(dsCom, con) ;
		super.appId = this.AppId;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/
	/**
	 * 組合以主鍵查詢的 sql
	 * @since 104/08/20
	 */
	private String getFindByPKSql(String compId,String vchrDate,String vchrNo,BigDecimal srlNo) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAAT1TMP");
		sqlStr.append(" WHERE  compId='"+compId+"' and vchrDate='"+vchrDate+"' and vchrNo='"+vchrNo+"' and srlNo="+srlNo+" ");
		return sqlStr.toString();
	}
	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @return aat1tmpVO - 單筆資料
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public aajct1tmpVO findByPK(String compId,String vchrDate,String vchrNo,BigDecimal srlNo)
			throws SQLException, Exception {
		this.sql = getFindByPKSql(compId,vchrDate,vchrNo,srlNo) ;
		return (aajct1tmpVO) this.eQuery(this.sql) ;
	}

	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @return aat1tmpVO - 單筆資料
	 * @exception dejcNotFoundException - 若查不到資料時
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public aajct1tmpVO findByPKExp(String compId,String vchrDate,String vchrNo,BigDecimal srlNo)
			throws dejcNotFoundException, SQLException, Exception {
		aajct1tmpVO aat1tmpVO = findByPK(compId,vchrDate,vchrNo,srlNo) ;
		if (aat1tmpVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aat1tmpVO ;
	}

	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @param aat1tmpVO - 由使用者所提供的 object, DAO 不另行 new
	 * @return aat1tmpVO - 單筆資料
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public aajct1tmpVO findByPK(aajct1tmpVO aat1tmpVO, String compId,String vchrDate,String vchrNo,BigDecimal srlNo)
			throws SQLException,Exception {
		this.sql = getFindByPKSql(compId,vchrDate,vchrNo,srlNo) ;
		return (aajct1tmpVO) this.eQuery(aat1tmpVO, this.sql) ;
	}

	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @param aat1tmpVO - 由使用者所提供的 object, DAO 不另行 new
	 * @return aat1tmpVO - 單筆資料
	 * @exception dejcNotFoundException - 若查不到資料時
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public aajct1tmpVO findByPKExp(aajct1tmpVO aat1tmpVO, String compId,String vchrDate,String vchrNo,BigDecimal srlNo)
			throws dejcNotFoundException, SQLException, Exception {
		aat1tmpVO = findByPK(aat1tmpVO, compId,vchrDate,vchrNo,srlNo) ;
		if (aat1tmpVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aat1tmpVO ;
	}

	/**
	 * 複合查詢資料
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public Vector findByMasterKey(String compId,String vchrDate,String vchrNo)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAAT1TMP");
		sqlStr.append(" WHERE compId='"+compId+"' and vchrDate='"+vchrDate+"' and vchrNo='"+vchrNo+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}

	/**
	 * 複合查詢資料
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public Vector findByMasterKey(String compId,String vchrDate)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAAT1TMP");
		sqlStr.append(" WHERE compId='"+compId+"' and vchrDate='"+vchrDate+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}

	/**
	 * 複合查詢資料
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public Vector findByMasterKey(String compId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAAT1TMP");
		sqlStr.append(" WHERE compId='"+compId+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}
	/**
	 * 新增一筆資料 throws
	 * <p>
	 * @param aat1tmpVO - Value Object
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料新增
	 * @exception dejcDupException - 重複鍵值
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int create(aajct1tmpVO aat1tmpVO)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		if (aat1tmpVO.verify() == false) {
			throw new dejcEditException(aat1tmpVO.getMessage()) ;
		}
		if (aat1tmpVO.isKeyOk() == false) {
			throw new dejcEditException("Value of key["+AppId+"].["+aat1tmpVO.getMessage()+"] is null or empty!") ;
		}

		
		this.sql = getCreateSql(aat1tmpVO) ;

		try {
			int rslt = this.executeUpdate(this.sql) ;

			if (rslt == 0) {
				throw new dejcNoUpdateException(this.sql) ;
			}
			return rslt;
		} catch (SQLException sqle) {
            handleDupException(sqle);
            return -1;
		}
	}
	
	public String getCreateSql(aajct1tmpVO aat1tmpVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAAT1TMP (");
		sqlStr.append("compId,vchrDate,vchrNo,srlNo,tradeNo,")
           .append("drCr,apId,pgrmId,acctCode,idCode,")
           .append("idDesc,refNo,refDesc,crcyUnit,qtyFrnAm,")
           .append("ntAmt,dueDate,srlDesc,statusCo,postDate,")
           .append("vchrType,exRate,transTradeNo,transVchrNo");
		sqlStr.append(") VALUES (");
		sqlStr.append("'").append(aat1tmpVO.getCompIdS()).append("','").append(aat1tmpVO.getVchrDateS()).append("','").append(aat1tmpVO.getVchrNoS()).append("',").append(aat1tmpVO.getSrlNo());
           sqlStr.append(",'").append(aat1tmpVO.getTradeNoS()).append("','").append(aat1tmpVO.getDrCrS()).append("','").append(aat1tmpVO.getApIdS());
           sqlStr.append("','").append(aat1tmpVO.getPgrmIdS()).append("','").append(aat1tmpVO.getAcctCodeS()).append("','").append(aat1tmpVO.getIdCodeS());
           sqlStr.append("','").append(aat1tmpVO.getIdDescS()).append("','").append(aat1tmpVO.getRefNoS()).append("','").append(aat1tmpVO.getRefDescS());
           sqlStr.append("','").append(aat1tmpVO.getCrcyUnitS()).append("',").append(aat1tmpVO.getQtyFrnAm()).append(",").append(aat1tmpVO.getNtAmt());
           sqlStr.append(",'").append(aat1tmpVO.getDueDateS()).append("','").append(aat1tmpVO.getSrlDescS()).append("','").append(aat1tmpVO.getStatusCoS());
           sqlStr.append("','").append(aat1tmpVO.getPostDateS()).append("','").append(aat1tmpVO.getVchrTypeS()).append("',").append(aat1tmpVO.getExRate());
           sqlStr.append(",'").append(aat1tmpVO.getTransTradeNoS()).append("','").append(aat1tmpVO.getTransVchrNoS()).append("'");
		sqlStr.append(")");	
		this.sql = sqlStr.toString() ;
		return this.sql;
	}

	/**
	 * 新增多筆資料 throws
	 * <p>
	 * @param aat1tmpVOList - Value Object
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料新增
	 * @exception dejcDupException - 重複鍵值
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int createList(List aat1tmpVOList)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aat1tmpVOList.size(); i++) {
			aajct1tmpVO aat1tmpVO = (aajct1tmpVO)aat1tmpVOList.get(i) ;
			count = count+this.create(aat1tmpVO) ;
		}
		return count ;
	}

  	/**
	 * 刪除資料
	 * <p>
	 * @return int - 交易筆數
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int remove(String compId,String vchrDate,String vchrNo,BigDecimal srlNo)
			throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAAT1TMP");
		sqlStr.append(" WHERE  compId='"+compId+"' and vchrDate='"+vchrDate+"' and vchrNo='"+vchrNo+"' and srlNo="+srlNo+" " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	

	/**
	 * 刪除資料 throws
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int removeExp(String compId,String vchrDate,String vchrNo,BigDecimal srlNo)
			throws dejcNoUpdateException, SQLException, Exception {
		int rslt = remove(compId,vchrDate,vchrNo,srlNo) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(this.sql) ;
		}
		return rslt ;
	}

	/**
	 * 刪除資料 throws
	 * <p>
	 * @param aat1tmpVO 欲刪除的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int removeExp(aajct1tmpVO aat1tmpVO)
			throws dejcNoUpdateException, SQLException, Exception {
		return removeExp(aat1tmpVO.getCompId(), aat1tmpVO.getVchrDate(), aat1tmpVO.getVchrNo(), aat1tmpVO.getSrlNo()) ;
	}

	/**
	 * 刪除多筆資料 throws
	 * <p>
	 * @param aat1tmpVOList 欲刪除的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int removeList(List aat1tmpVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aat1tmpVOList.size(); i++) {
			aajct1tmpVO aat1tmpVO = (aajct1tmpVO)aat1tmpVOList.get(i) ;
			count = count+this.remove(aat1tmpVO.getCompId(), aat1tmpVO.getVchrDate(), aat1tmpVO.getVchrNo(), aat1tmpVO.getSrlNo()) ;
		}
		return count ;
	}

	/**
	 * 刪除資料 throws
	 * <p>
	 * @param aat1tmpVO 欲刪除的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int remove(aajct1tmpVO aat1tmpVO)
			throws SQLException, Exception {
		return remove(aat1tmpVO.getCompId(), aat1tmpVO.getVchrDate(), aat1tmpVO.getVchrNo(), aat1tmpVO.getSrlNo()) ;
	}

	/**
	 * 修改資料
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int update(aajct1tmpVO aat1tmpVO)
			throws dejcEditException, SQLException, Exception {
		if (aat1tmpVO.verify() == false) {
			throw new dejcEditException(aat1tmpVO.getMessage()) ;
		}
		if(aat1tmpVO.hasEditFields()) {
			return updateEditFields(aat1tmpVO);
		} else {
			return this.executeUpdate(getUpdateSql(aat1tmpVO) ) ;
		}
	}

	public String getUpdateSql(aajct1tmpVO aat1tmpVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAAT1TMP");
		sqlStr.append(" SET tradeNo='").append(aat1tmpVO.getTradeNoS()).append("',drCr='").append(aat1tmpVO.getDrCrS()).append("',apId='").append(aat1tmpVO.getApIdS()).append("',pgrmId='").append(aat1tmpVO.getPgrmIdS()).append("',acctCode='").append(aat1tmpVO.getAcctCodeS()).append("',");
           sqlStr.append("idCode='").append(aat1tmpVO.getIdCodeS()).append("',idDesc='").append(aat1tmpVO.getIdDescS()).append("',refNo='").append(aat1tmpVO.getRefNoS()).append("',refDesc='").append(aat1tmpVO.getRefDescS()).append("',crcyUnit='").append(aat1tmpVO.getCrcyUnitS()).append("',");
           sqlStr.append("qtyFrnAm=").append(aat1tmpVO.getQtyFrnAm()).append(",ntAmt=").append(aat1tmpVO.getNtAmt()).append(",dueDate='").append(aat1tmpVO.getDueDateS()).append("',srlDesc='").append(aat1tmpVO.getSrlDescS()).append("',statusCo='").append(aat1tmpVO.getStatusCoS()).append("',");
           sqlStr.append("postDate='").append(aat1tmpVO.getPostDateS()).append("',vchrType='").append(aat1tmpVO.getVchrTypeS()).append("',exRate=").append(aat1tmpVO.getExRate()).append(",transTradeNo='").append(aat1tmpVO.getTransTradeNoS()).append("',transVchrNo='").append(aat1tmpVO.getTransVchrNoS()).append("' ");
		sqlStr.append(" WHERE  compId='"+aat1tmpVO.getCompId()+"' and vchrDate='"+aat1tmpVO.getVchrDate()+"' and vchrNo='"+aat1tmpVO.getVchrNo()+"' and srlNo="+aat1tmpVO.getSrlNo()+" ");
		this.sql = sqlStr.toString();
		return this.sql ;	
	}
	
	/**
	 * 若資料與預設值不同，才要修改該欄位
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int updateFldsNotDef(aajct1tmpVO aat1tmpVO)
			throws dejcEditException, SQLException, Exception {
		if (aat1tmpVO.verify() == false) {
			throw new dejcEditException(aat1tmpVO.getMessage()) ;
		}
		if (aat1tmpVO.isKeyOk() == false) {
			throw new dejcEditException("primary key["+aat1tmpVO.getMessage()+"] of ["+AppId+"] is empty!") ;
		}		
		StringBuffer updateFlds = new StringBuffer();
		if (aat1tmpVO.getTradeNo() !=null  && !aat1tmpVO.getTradeNo().equals("") ) {
			updateFlds.append("tradeNo='").append( aat1tmpVO.getTradeNoS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getDrCr() !=null  && !aat1tmpVO.getDrCr().equals("") ) {
			updateFlds.append("drCr='").append( aat1tmpVO.getDrCrS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getApId() !=null  && !aat1tmpVO.getApId().equals("") ) {
			updateFlds.append("apId='").append( aat1tmpVO.getApIdS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getPgrmId() !=null  && !aat1tmpVO.getPgrmId().equals("") ) {
			updateFlds.append("pgrmId='").append( aat1tmpVO.getPgrmIdS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getAcctCode() !=null  && !aat1tmpVO.getAcctCode().equals("") ) {
			updateFlds.append("acctCode='").append( aat1tmpVO.getAcctCodeS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getIdCode() !=null  && !aat1tmpVO.getIdCode().equals("") ) {
			updateFlds.append("idCode='").append( aat1tmpVO.getIdCodeS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getIdDesc() !=null  && !aat1tmpVO.getIdDesc().equals("") ) {
			updateFlds.append("idDesc='").append( aat1tmpVO.getIdDescS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getRefNo() !=null  && !aat1tmpVO.getRefNo().equals("") ) {
			updateFlds.append("refNo='").append( aat1tmpVO.getRefNoS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getRefDesc() !=null  && !aat1tmpVO.getRefDesc().equals("") ) {
			updateFlds.append("refDesc='").append( aat1tmpVO.getRefDescS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getCrcyUnit() !=null  && !aat1tmpVO.getCrcyUnit().equals("") ) {
			updateFlds.append("crcyUnit='").append( aat1tmpVO.getCrcyUnitS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getQtyFrnAm() !=null  && !aat1tmpVO.getQtyFrnAm().equals(new BigDecimal("0")) ) {
			updateFlds.append("qtyFrnAm=").append( aat1tmpVO.getQtyFrnAm() ).append("").append(',') ;
		} 
		if (aat1tmpVO.getNtAmt() !=null  && !aat1tmpVO.getNtAmt().equals(new BigDecimal("0")) ) {
			updateFlds.append("ntAmt=").append( aat1tmpVO.getNtAmt() ).append("").append(',') ;
		} 
		if (aat1tmpVO.getDueDate() !=null  && !aat1tmpVO.getDueDate().equals("") ) {
			updateFlds.append("dueDate='").append( aat1tmpVO.getDueDateS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getSrlDesc() !=null  && !aat1tmpVO.getSrlDesc().equals("") ) {
			updateFlds.append("srlDesc='").append( aat1tmpVO.getSrlDescS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getStatusCo() !=null  && !aat1tmpVO.getStatusCo().equals("") ) {
			updateFlds.append("statusCo='").append( aat1tmpVO.getStatusCoS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getPostDate() !=null  && !aat1tmpVO.getPostDate().equals("") ) {
			updateFlds.append("postDate='").append( aat1tmpVO.getPostDateS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getVchrType() !=null  && !aat1tmpVO.getVchrType().equals("") ) {
			updateFlds.append("vchrType='").append( aat1tmpVO.getVchrTypeS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getExRate() !=null  && !aat1tmpVO.getExRate().equals(new BigDecimal("1")) ) {
			updateFlds.append("exRate=").append( aat1tmpVO.getExRate() ).append("").append(',') ;
		} 
		if (aat1tmpVO.getTransTradeNo() !=null  && !aat1tmpVO.getTransTradeNo().equals("") ) {
			updateFlds.append("transTradeNo='").append( aat1tmpVO.getTransTradeNoS() ).append("'").append(',') ;
		} 
		if (aat1tmpVO.getTransVchrNo() !=null  && !aat1tmpVO.getTransVchrNo().equals("") ) {
			updateFlds.append("transVchrNo='").append( aat1tmpVO.getTransVchrNoS() ).append("'").append(',') ;
		} 
		if(updateFlds.length()==0) {
			return 0;
		}	
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAAT1TMP");
		sqlStr.append(" SET ").append( updateFlds.substring(0, updateFlds.length()-1) );
		sqlStr.append(" WHERE  compId='"+aat1tmpVO.getCompId()+"' and vchrDate='"+aat1tmpVO.getVchrDate()+"' and vchrNo='"+aat1tmpVO.getVchrNo()+"' and srlNo="+aat1tmpVO.getSrlNo()+" ");
		this.sql = sqlStr.toString();
		return this.executeUpdate(this.sql) ;
	}


	/**
	 * 修改資料 throws
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料修改
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int updateExp(aajct1tmpVO aat1tmpVO)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int rslt = update(aat1tmpVO) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(sql) ;
		}
		return rslt ;
	}

	/**
	 * 修改-新增多筆資料, 如果修改 0 筆的話，即新增該筆資料
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料修改
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int updateCreateList(List aat1tmpVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aat1tmpVOList.size(); i++) {
			aajct1tmpVO aat1tmpVO = (aajct1tmpVO)aat1tmpVOList.get(i) ;
			int updateCount = update(aat1tmpVO) ;
			if ( updateCount==0 ) {
				count += create(aat1tmpVO) ;
			} else {
				count += updateCount ;
			}
		}
		return count ;
	}

	/**
	 * 修改多筆資料 throws
	 * <p>
	 * @param aat1tmpVOList 欲修改的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被修改
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	public int updateList(List aat1tmpVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aat1tmpVOList.size(); i++) {
			aajct1tmpVO aat1tmpVO = (aajct1tmpVO)aat1tmpVOList.get(i) ;
			count = count+this.updateExp(aat1tmpVO) ;
		}
		return count ;
	}

	/**
	 * 執行 addCreateBatch(Object obj) 時需要用到的 sql
	 * 新增資料的 prepareStatement sql<br>
	 * 此方法是覆寫 commonDAO 的方法
	 */
    protected String getCreatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAAT1TMP  (");
		sqlStr.append("compId,vchrDate,vchrNo,srlNo,tradeNo,")
           .append("drCr,apId,pgrmId,acctCode,idCode,")
           .append("idDesc,refNo,refDesc,crcyUnit,qtyFrnAm,")
           .append("ntAmt,dueDate,srlDesc,statusCo,postDate,")
           .append("vchrType,exRate,transTradeNo,transVchrNo");
		sqlStr.append(") VALUES (");
		sqlStr.append("?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,")
           .append("?,?,?,?");
		sqlStr.append(")");		    
		return sqlStr.toString();
    }

	/**
	 * 執行 addupdateBatch(Object obj) 時需要用到的 sql
	 * 修改資料的 prepareStatement sql<br>
	 * 此方法是覆寫 commonDAO 的方法
	 */
    protected String getUpdatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAAT1TMP");
		sqlStr.append(" SET tradeNo=?,drCr=?,apId=?,pgrmId=?,acctCode=?,")
           .append("idCode=?,idDesc=?,refNo=?,refDesc=?,crcyUnit=?,")
           .append("qtyFrnAm=?,ntAmt=?,dueDate=?,srlDesc=?,statusCo=?,")
           .append("postDate=?,vchrType=?,exRate=?,transTradeNo=?,transVchrNo=? ");
		sqlStr.append(" WHERE  compId=? and vchrDate=? and vchrNo=? and srlNo=? ");
		return sqlStr.toString();
    }

	/**
	 * 執行 addDeleteBatch(Object obj) 時需要用到的 sql
	 * 刪除資料的 prepareStatement sql<br>
	 * 此方法是覆寫 commonDAO 的方法
	 */
    protected String getDeletePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAAT1TMP");
		sqlStr.append(" WHERE  compId=? and vchrDate=? and vchrNo=? and srlNo=? " );
		return sqlStr.toString();
    }

	/**
	 * 執行 addCreateBatch(Object obj) 時需要呼叫的方法
	 */
    protected void prepareCreate(Object obj) throws SQLException {
    	aajct1tmpVO aat1tmpVO = (aajct1tmpVO)obj ;
		pstmt.setString(1, aat1tmpVO.getCompId()) ;
		pstmt.setString(2, aat1tmpVO.getVchrDate()) ;
		pstmt.setString(3, aat1tmpVO.getVchrNo()) ;
		pstmt.setBigDecimal(4, aat1tmpVO.getSrlNo()) ;
		pstmt.setString(5, aat1tmpVO.getTradeNo()) ;
		pstmt.setString(6, aat1tmpVO.getDrCr()) ;
		pstmt.setString(7, aat1tmpVO.getApId()) ;
		pstmt.setString(8, aat1tmpVO.getPgrmId()) ;
		pstmt.setString(9, aat1tmpVO.getAcctCode()) ;
		pstmt.setString(10, aat1tmpVO.getIdCode()) ;
		pstmt.setString(11, aat1tmpVO.getIdDesc()) ;
		pstmt.setString(12, aat1tmpVO.getRefNo()) ;
		pstmt.setString(13, aat1tmpVO.getRefDesc()) ;
		pstmt.setString(14, aat1tmpVO.getCrcyUnit()) ;
		pstmt.setBigDecimal(15, aat1tmpVO.getQtyFrnAm()) ;
		pstmt.setBigDecimal(16, aat1tmpVO.getNtAmt()) ;
		pstmt.setString(17, aat1tmpVO.getDueDate()) ;
		pstmt.setString(18, aat1tmpVO.getSrlDesc()) ;
		pstmt.setString(19, aat1tmpVO.getStatusCo()) ;
		pstmt.setString(20, aat1tmpVO.getPostDate()) ;
		pstmt.setString(21, aat1tmpVO.getVchrType()) ;
		pstmt.setBigDecimal(22, aat1tmpVO.getExRate()) ;
		pstmt.setString(23, aat1tmpVO.getTransTradeNo()) ;
		pstmt.setString(24, aat1tmpVO.getTransVchrNo()) ;
    }

	/**
	 * 執行 addUpdateBatch(Object obj) 時需要呼叫的方法
	 */
    protected void prepareUpdate(Object obj) throws SQLException {
    	aajct1tmpVO aat1tmpVO = (aajct1tmpVO)obj ;
		pstmt.setString(1, aat1tmpVO.getTradeNo()) ;
		pstmt.setString(2, aat1tmpVO.getDrCr()) ;
		pstmt.setString(3, aat1tmpVO.getApId()) ;
		pstmt.setString(4, aat1tmpVO.getPgrmId()) ;
		pstmt.setString(5, aat1tmpVO.getAcctCode()) ;
		pstmt.setString(6, aat1tmpVO.getIdCode()) ;
		pstmt.setString(7, aat1tmpVO.getIdDesc()) ;
		pstmt.setString(8, aat1tmpVO.getRefNo()) ;
		pstmt.setString(9, aat1tmpVO.getRefDesc()) ;
		pstmt.setString(10, aat1tmpVO.getCrcyUnit()) ;
		pstmt.setBigDecimal(11, aat1tmpVO.getQtyFrnAm()) ;
		pstmt.setBigDecimal(12, aat1tmpVO.getNtAmt()) ;
		pstmt.setString(13, aat1tmpVO.getDueDate()) ;
		pstmt.setString(14, aat1tmpVO.getSrlDesc()) ;
		pstmt.setString(15, aat1tmpVO.getStatusCo()) ;
		pstmt.setString(16, aat1tmpVO.getPostDate()) ;
		pstmt.setString(17, aat1tmpVO.getVchrType()) ;
		pstmt.setBigDecimal(18, aat1tmpVO.getExRate()) ;
		pstmt.setString(19, aat1tmpVO.getTransTradeNo()) ;
		pstmt.setString(20, aat1tmpVO.getTransVchrNo()) ;
		pstmt.setString(21, aat1tmpVO.getCompId()) ;
		pstmt.setString(22, aat1tmpVO.getVchrDate()) ;
		pstmt.setString(23, aat1tmpVO.getVchrNo()) ;
		pstmt.setBigDecimal(24, aat1tmpVO.getSrlNo()) ;
    }

	/**
	 * 執行 addDeleteBatch(Object obj) 時需要呼叫的方法
	 */
    protected void prepareDelete(Object obj) throws SQLException {
    	aajct1tmpVO aat1tmpVO = (aajct1tmpVO)obj ;
		pstmt.setString(1, aat1tmpVO.getCompId()) ;
		pstmt.setString(2, aat1tmpVO.getVchrDate()) ;
		pstmt.setString(3, aat1tmpVO.getVchrNo()) ;
		pstmt.setBigDecimal(4, aat1tmpVO.getSrlNo()) ;
    }

	/**
	 * 實作父類別
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	protected Object getObjFromRS(ResultSet rs) throws SQLException,Exception {
		return aajct1tmpVO.getInstanceByName(rs) ;
	}

	/**
	 * 實作父類別, 使用 user 傳進來的 object, 不另行 new , 以節省執行時間及記憶體<br>
	 * 適合臨時性的資料運算(通常在運算大量資料時，一些欄位需要暫存在 value object)<br>
	 * 此方法只會在執行 findByPK(Object obj,String compId,String vchrDate,String vchrNo,BigDecimal srlNo) 時才被呼叫。
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/20
	 */
	protected Object getObjFromRS(Object obj, ResultSet rs) throws SQLException,Exception {
		return aajct1tmpVO.getInstanceByName(obj, rs) ;
	}
	
	/**
	  * 備份方法 2009.03.26 新增
	  */
	public int backup(String tableName, aajct1tmpVO aat1tmpVO) throws SQLException, Exception {
		this.sql = getCreateSql(aat1tmpVO).replaceFirst(" DB.TBAAT1TMP "," "+tableName+" "); 
		return this.executeUpdate(this.sql) ;
	} 
	public int backup(String tableName, ResultSet rs) throws SQLException, Exception {
		return backup(tableName, aajct1tmpVO.getInstanceByName(rs) ) ;
	} 

	public String getUpdateEditFieldsPartialSql(aajct1tmpVO aat1tmpVO)  {
		if( aat1tmpVO.getEditFields()==null ) {
			return "";
		}
		return dejcSqlUtils.genUpdateFields(  aat1tmpVO.getEditFields() );		
	}
	/**
	  * 修改有動過的欄位，此方法要有效請確保有先執行  aajct1tmpVO.monitor() 
	  */
	public String getUpdateEditFieldsSql(aajct1tmpVO aat1tmpVO) throws SQLException, Exception {
		String updSql =getUpdateEditFieldsPartialSql(aat1tmpVO) ;
		if(updSql.equals("")){
			return "";
		}
		return "update DB.TBAAT1TMP set "+updSql+" where   compId='"+aat1tmpVO.getCompId()+"' and vchrDate='"+aat1tmpVO.getVchrDate()+"' and vchrNo='"+aat1tmpVO.getVchrNo()+"' and srlNo="+aat1tmpVO.getSrlNo()+" ";
	}
	/**
	  * 修改有動過的欄位，此方法要有效請確保有先執行  aajct1tmpVO.monitor() 
	  */
	public int updateEditFields(aajct1tmpVO aat1tmpVO) throws SQLException, Exception {
		this.sql=getUpdateEditFieldsSql(aat1tmpVO);
		if(this.sql.equals("")){
			// 2010.03.02 從0改為-1，以識別是否有執行
			return -1;
		}
		return this.executeUpdate(getUpdateEditFieldsSql(aat1tmpVO)) ;
	}
	/**
	 * 假update的sql，找最後一個欄位來假update
	 */	
	private String getLockSqlPrefix() {
		return "update DB.TBAAT1TMP set transVchrNo=transVchrNo ";
	}

	/**
	 * 查詢、鎖定、監控資料
	 */	
	public aajct1tmpVO loadByPK4Update(String compId,String vchrDate,String vchrNo,BigDecimal srlNo) throws SQLException, Exception {
		List aList = loadList4Update("WHERE   compId='"+compId+"' and vchrDate='"+vchrDate+"' and vchrNo='"+vchrNo+"' and srlNo="+srlNo+" ");
		if(aList.size()>0) {
			return (aajct1tmpVO)aList.get(0);
		}else {
			throw new dejcNotFoundException(this.sql) ;	
		}	
	}
	
	private String getSelectSqlPrefix() {
		return "select * from DB.TBAAT1TMP ";
	}
	
	private List loadList4Update(String conditionSql) throws SQLException, Exception {
		this.executeUpdate(getLockSqlPrefix()+conditionSql) ;
		this.sql=getSelectSqlPrefix()+conditionSql ;
		return monitor(this.eQueryAll( this.sql ));
	}
	/**
	  *監控資料
	  */
	private List monitor(List aat1tmpVOList){
		for (int i=0; i<aat1tmpVOList.size(); i++) {
			aajct1tmpVO aat1tmpVO = (aajct1tmpVO)aat1tmpVOList.get(i) ;
			aat1tmpVO.monitor();
		}	
		return aat1tmpVOList;
	}
	/**
	 * 以 VO 撈出 DB 資料，撈資料同時會鎖定
	 */
	public aajct1tmpVO loadByVO(aajct1tmpVO aat1tmpVO) throws SQLException, Exception {
		return loadByPK4Update(aat1tmpVO.getCompId(), aat1tmpVO.getVchrDate(), aat1tmpVO.getVchrNo(), aat1tmpVO.getSrlNo());
	}
	/**
	 * 以 VO List 撈出 DB 資料，撈資料同時會鎖定
	 */
	public List loadByList(List aat1tmpVOList) throws SQLException, Exception {
		List rsltList= new ArrayList(aat1tmpVOList.size());
		for (Iterator iterator = aat1tmpVOList.iterator(); iterator.hasNext();) {
			aajct1tmpVO aat1tmpVO = (aajct1tmpVO) iterator.next();
			rsltList.add( loadByVO(aat1tmpVO) ) ;
		}
		return rsltList;
	}
	/**
	 * 以動態查詢條件查詢
	 */
	public Vector findByCriteria(dejcCriteria criteria)throws dejcNotFoundException, SQLException, Exception{
		return findByCriteria(criteria,"");
	}
	/**
	 * 以動態查詢條件查詢
	 */
	public Vector findByCriteria(dejcCriteria criteria,String orderby)throws dejcNotFoundException, SQLException, Exception{
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append(getSelectSqlPrefix());
		if(!dejcUtility.isNull(orderby)) {
			orderby="order by "+orderby;
		}
		sqlStr.append(criteria.toSqlWithWhereIfExists()).append(orderby);  
		this.sql = sqlStr.toString();
		return this.eQueryAll(this.sql) ;
	}
	
//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //



} // end of Class aajct1tmpDAO