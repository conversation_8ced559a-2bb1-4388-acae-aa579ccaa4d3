<%@ page contentType = "text/html;charset=GBK"%>

<%@ page import="com.icsc.dpms.de.*" %>
<%@ page import="com.icsc.dpms.de.structs.*" %>
<%@ page import="net.sf.jasperreports.engine.JasperPrint" %>
<%@ page import="net.sf.jasperreports.engine.JRExporterParameter" %>
<%@ page import="net.sf.jasperreports.engine.JRPrintPage" %>
<%@ page import="net.sf.jasperreports.engine.JRPrintElement" %>
<%@ page import="net.sf.jasperreports.engine.export.JRPdfExporter" %>
<%@ page import="net.sf.jasperreports.engine.util.JRLoader" %>

<%! public static final String _AppId = "AAJJPRINT"; %>

<%@ include file="../../jsp/dzjjmain.jsp" %>
<%
String pdfUrl = "";

String REPORT_URL = request.getParameter("REPORT_URL");
if(REPORT_URL==null){
	dejcWebInfoOut infoOut = (dejcWebInfoOut)request.getAttribute("infoOut") ;
	pdfUrl = infoOut.getParameter("pdfUrl");
}else{
  REPORT_URL =   REPORT_URL.substring(5,REPORT_URL.length());
  JasperPrint jasperPrint = (JasperPrint) JRLoader.loadObject(REPORT_URL);
  
  String isPrintBackgroundImg = request.getParameter("isPrintBackgroundImg");
  if(isPrintBackgroundImg!=null && isPrintBackgroundImg.equals("N")){
      List<JRPrintPage> jrPrintPageList = jasperPrint.getPages();
      for (JRPrintPage jrPrintPage : jrPrintPageList) {
          List elements = jrPrintPage.getElements();

          List<JRPrintElement> list = new Vector<JRPrintElement>();
          for (Object obj : elements) {
              JRPrintElement element = (JRPrintElement) obj;

              String key = element.getKey();
              if (!key.startsWith("imageNotPrint")) {
                  list.add(element);
              }
          }
          jrPrintPage.setElements(list);
      }
  }
  
  JRPdfExporter pdfExporter=new JRPdfExporter();
  
  pdfUrl = dejc332.getPublicDirectory() + "aa/" + _dsCom.user.ID + "/" +  System.currentTimeMillis() + ".pdf";
  pdfExporter.setParameter(JRExporterParameter.JASPER_PRINT,jasperPrint);
  pdfExporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME,pdfUrl);
  pdfExporter.exportReport();
  
  pdfUrl = "/erp/" + pdfUrl;
}
%>

<script>
window.location.href = "<%=pdfUrl%>";
</script>
</body>
</html>

