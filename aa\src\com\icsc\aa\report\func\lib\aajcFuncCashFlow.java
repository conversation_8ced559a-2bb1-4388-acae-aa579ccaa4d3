/**
 * 
 */
package com.icsc.aa.report.func.lib;

import java.math.BigDecimal;
import java.sql.SQLException;

import com.icsc.aa.dao.aajcw6DAO;
import com.icsc.aa.report.aajcReportContext;
import com.icsc.aa.report.func.aajcFuntionCommon;

/**
 *
 */
public class aajcFuncCashFlow extends aajcFuntionCommon {
	aajcw6DAO aaw6DAO;
	/**
	 * @param aaReportContext
	 */
	public aajcFuncCashFlow(aajcReportContext aaReportContext) {
		super(aaReportContext);
		aaw6DAO = new aajcw6DAO(aaReportContext.getDscom());
	}
	
	/**
	 * @return 
	 * @throws Exception 
	 * @throws SQLException 
	 * 
	 */
	public BigDecimal XJLL(String cashFlowCodes) throws SQLException, Exception {
		String[] cashCodeArray = cashFlowCodes.split(",");
		
		BigDecimal amtT= new BigDecimal(0);
		
		for(int i=0;cashCodeArray!=null && i<cashCodeArray.length;i++){
			BigDecimal amt = aaw6DAO.findAmt(aaReportContext.getDscom().companyId, cashCodeArray[i], this.acctPeriod+"01",this.acctPeriod+"31");
			amtT = amtT.add(amt);
		}
		
		return amtT.setScale(2, 5);
	}
	/**
	 * @return 
	 * @throws Exception 
	 * @throws SQLException 
	 * 
	 */
	public BigDecimal LJXJLL(String cashFlowCodes) throws SQLException, Exception {
		String[] cashCodeArray = cashFlowCodes.split(",");
		
		BigDecimal amtT= new BigDecimal(0);
		
		for(int i=0;cashCodeArray!=null && i<cashCodeArray.length;i++){
			BigDecimal amt = aaw6DAO.findAmt(aaReportContext.getDscom().companyId, cashCodeArray[i], this.acctPeriod.substring(0, 4) + "0101",this.acctPeriod+"31");
			amtT = amtT.add(amt);
		}
		
		return amtT.setScale(2, 5);
	}
	

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub

	}

}
