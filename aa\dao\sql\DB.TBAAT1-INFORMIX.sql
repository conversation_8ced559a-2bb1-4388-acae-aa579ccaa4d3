CREATE TABLE DB.TBAAT1 (
compId VARCHAR (10) DEFAULT '' NOT NULL,vchrDate VARCHAR (8) DEFAULT '' NOT NULL,vchrNo VARCHAR (15) DEFAULT '' NOT NULL,srlNo DECIMAL (4,0)  DEFAULT 0  NOT NULL,tradeNo VARCHAR (15) DEFAULT '',drCr VARCHAR (1) DEFAULT '',apId VARCHAR (3) DEFAULT '',pgrmId VARCHAR (20) DEFAULT '',acctCode VARCHAR (20) DEFAULT '',idCode VARCHAR (50) DEFAULT '',idDesc VARCHAR (50) DEFAULT '',refNo VARCHAR (50) DEFAULT '',refDesc VARCHAR (50) DEFAULT '',crcyUnit VARCHAR (10) DEFAULT '',qtyFrnAmt DECIMAL (18,4)  DEFAULT 0 ,ntAmt DECIMAL (18,4)  DEFAULT 0 ,dueDate VARCHAR (8) DEFAULT '',srlDesc LVARCHAR(500) DEFAULT '',statusCode VARCHAR (1) DEFAULT '',postDate VARCHAR (8) DEFAULT '',vchrType VARCHAR (1) DEFAULT '',exRate DECIMAL (18,6)  DEFAULT 1 , PRIMARY KEY(compId,vchrDate,vchrNo,srlNo) 
) ;
GRANT ALL ON DB.TBAAT1 TO JAVAUSER ;
