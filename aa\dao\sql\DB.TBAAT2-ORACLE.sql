CREATE TABLE DB.TBAAT2 (
compId CHAR(10) DEFAULT '' NOT NULL,vchrDate CHAR(8) DEFAULT '' NOT NULL,vchrNo CHAR(15) DEFAULT '' NOT NULL,tradeNo CHAR(15) DEFAULT '',apId CHAR(2) DEFAULT '',pgrmId CHAR(8) DEFAULT '',vchrDesc VARCHAR2(500) DEFAULT '',statusCode CHAR(1) DEFAULT '',bizNo CHAR(10) DEFAULT '',billNo CHAR(15) DEFAULT '',createDateTime CHAR(14) DEFAULT '',createUserId CHAR(10) DEFAULT '',updateDateTime CHAR(14) DEFAULT '',updateDateUserId CHAR(10) DEFAULT '',postDate CHAR(8) DEFAULT '',potstDateUserId CHAR(10) DEFAULT '',printYn CHAR(1) DEFAULT '',printCount NUMBER (3,0)  DEFAULT 0 ,vchrType CHAR(1) DEFAULT '',confirmUserId CHAR(10) DEFAULT '',appendix NUMBER (3,0)  DEFAULT 0 ,auditDate CHAR(8) DEFAULT '',auditor CHAR(10) DEFAULT '',closeUserId CHAR(10) DEFAULT '',commonVchr CHAR(1) DEFAULT '',confirmDate CHAR(8) DEFAULT '', PRIMARY KEY(compId,vchrDate,vchrNo) 
) ;
GRANT ALL ON DB.TBAAT2 TO JAVAUSER ;
