/*
 * Created on 2006/3/1
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package com.icsc.ac.util;

import java.sql.*;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.dao.*;
import com.icsc.facc.zaf.*;
import com.icsc.ip.dao.ipjcrdDAO;
import com.icsc.ip.dao.ipjcrdVO;

import java.util.*;

/**
 * <AUTHOR>
 */
public class acjcZafUtil {
	public static final String TYPE_TEXT = "TEXT";
	public static final String TYPE_SELECT = "SELECT";

	private dsjccom dsCom;

	private zafcrdDAO rdDAO;

	private Connection con;

	public acjcZafUtil(dsjccom dc) {
		dsCom = dc;
		initDAO();
	}

	public acjcZafUtil(dsjccom dc, Connection cn) {
		dsCom = dc;
		con = cn;
		initDAO();
	}

	private void initDAO() {
		if (con == null) {
			rdDAO = new zafcrdDAO(dsCom);
		} else {
			rdDAO = new zafcrdDAO(dsCom, con);
		}
	}

	public String getTEXTType(String columnName, String defaultValue)
			throws Exception {
		return "<input type='text' name='" + columnName + "' value='"
				+ defaultValue + "'>";
	}
	
	public String getSELECTType(String compID, String leafID, String sysId, String columnName, String defaultValue) throws Exception {
		return this.getSELECTType(compID, leafID, sysId, columnName, defaultValue, "");
	}

	public String getSELECTType(String compID, String leafID, String sysId, String columnName, String defaultValue, String eventStr) throws Exception {
		StringBuffer sb = new StringBuffer();

		sb.append("<select name='" + columnName + "' " + eventStr + ">");
		sb.append("<option value=''></option>");
		if (sysId.equals("ZAF")) {
			Vector vt = rdDAO.queryByLeafId(compID, leafID);
			Iterator it = vt.iterator();
			while (it.hasNext()) {
				zafcrdVO rdvo = (zafcrdVO) it.next();
				if (!rdvo.getStatus().equals("Y"))
					continue;
				if (defaultValue.equals(rdvo.getItemA())) {
					sb.append("<option value='" + rdvo.getItemA()
							+ "' selected>" + rdvo.getItemA() + "_"
							+ rdvo.getDataA() + "</option>");
				} else {
					sb.append("<option value='" + rdvo.getItemA() + "'>"
							+ rdvo.getItemA() + "_" + rdvo.getDataA()
							+ "</option>");
				}
			}
		}else if(sysId.equals("IP")){
			Vector vt = new ipjcrdDAO(dsCom).queryByLeafId(compID, leafID);
			Iterator it = vt.iterator();
			while (it.hasNext()) {
				ipjcrdVO rdvo = (ipjcrdVO) it.next();
				if (defaultValue.equals(rdvo.getItemA())) {
					sb.append("<option value='" + rdvo.getItemA()
							+ "' selected>" + rdvo.getItemA() + "_"
							+ rdvo.getDataA() + "</option>");
				} else {
					sb.append("<option value='" + rdvo.getItemA() + "'>"
							+ rdvo.getItemA() + "_" + rdvo.getDataA()
							+ "</option>");
				}
			}
		}
		sb.append("</select>");
		return sb.toString();
	}

	/**
	 * 
	 * @param vo
	 * @param defaultValue
	 * @param specialCase
	 * @return
	 */
	public String getSelectMenu(zafcrdVO vo, String defaultValue,
			String specialCase) {
		String rslt = "<option></option>";

		try {
			rslt += zafcAPI.getAttriSelect(dsCom, vo, defaultValue, specialCase);
		} catch (Exception e) {
			rslt += "<option>ERROR</option>";
		}
		return rslt;
	}

	public String[][] getDataByLeafId(String compID, String leafID)
			throws Exception {

		try {
			Vector vt = rdDAO.queryByLeafId(compID, leafID);

			String result[][] = new String[2][];
			result[0] = new String[vt.size()];
			result[1] = new String[vt.size()];

			Iterator it = vt.iterator();
			int i = 0;
			while (it.hasNext()) {
				zafcrdVO rdvo = (zafcrdVO) it.next();
				result[0][i] = rdvo.getItemA();
				result[1][i] = rdvo.getDataA();
				i++;
			}

			return result;
		} catch (Exception e) {
			return null;
		}
	}

	public Map getDataMapByLeafId(String compID, String leafID)
			throws Exception {
		HashMap rslt = new HashMap();

		Vector vt = rdDAO.queryByLeafId(compID, leafID);
		Iterator it = vt.iterator();
		while (it.hasNext()) {
			zafcrdVO rdvo = (zafcrdVO) it.next();
			rslt.put(rdvo.getItemA(), rdvo.getDataA());
		}

		return rslt;
	}

}
