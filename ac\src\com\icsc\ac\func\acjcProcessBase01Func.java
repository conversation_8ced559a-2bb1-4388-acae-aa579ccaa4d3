package com.icsc.ac.func;

import java.sql.Connection;
import java.util.List;

import com.icsc.ac.dao.acjcb4DAO;
import com.icsc.ac.dao.acjcb4VO;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dejc308;
import com.icsc.dpms.de.structs.dejcFunctionalController;
import com.icsc.facc.zaf.bp.zafcmsg;

public class acjcProcessBase01Func extends dejcFunctionalController {

	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:27:00 $";

	private String compId;

	private String year;

	private String process;

	public acjcProcessBase01Func() {
		this.AppId = "ACJCPROCESSBASE01FUNC";
	}

	public void init() throws Exception {
		compId = dsCom.companyId;
		year = infoIn.getQryField("year");
		process = infoIn.getQryField("process");
	}

	public void query() throws Exception {
		try {
			acjcb4DAO acB4DAO = new acjcb4DAO(dsCom);
			List list = acB4DAO.findByMasterKey(compId, year);
			infoOut.addResultVO("v", list);
			if (list.size() > 0) {
				infoOut.setMessage(zafcmsg.INQ_OK);
			} else {
				infoOut.setMessage(zafcmsg.ERR_NOT_FOUND);
			}
		} catch (Exception e) {
			this.handleExp(e);
		}
	}

	public void create() throws Exception {
		dejc301 de301 = new dejc301();
		dejc308 de308 = new dejc308();
		try {
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			acjcb4DAO acB4DAO = new acjcb4DAO(dsCom, con);
			List list = infoIn.getSequenceVOs("v", "box");
			for (int i = 0; i < list.size(); i++) {
				acjcb4VO acB4VO = new acjcb4VO();
				acB4VO = (acjcb4VO) list.get(i);
				acB4VO.setCompId(compId);
				acB4VO.setFiscalYear(year);
				acB4VO.setCreateId(dsCom.user.ID);
				acB4VO.setLastUpdId(dsCom.user.ID);
				acB4VO.setLastUpdDate(de308.getCrntDateLFmt1());
			}
			acB4DAO.createList(list);
			de301.commit();
			this.query();
			infoOut.setMessage(zafcmsg.IST_OK);
		} catch (Exception e) {
			this.handleExp(e);
			infoOut.setMessage(e.toString());
		} finally {
			de301.close();
		}
	}

	public void update() throws Exception {
		dejc301 de301 = new dejc301();
		dejc308 de308 = new dejc308();
		try {
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			acjcb4DAO acB4DAO = new acjcb4DAO(dsCom, con);
			List list = infoIn.getSequenceVOs("v", "box");
			for (int i = 0; i < list.size(); i++) {
				acjcb4VO acB4VO = new acjcb4VO();
				acB4VO = (acjcb4VO) list.get(i);
				acB4VO.setLastUpdId(dsCom.user.ID);
				acB4VO.setLastUpdDate(de308.getCrntDateLFmt1());
			}
			acB4DAO.updateList(list);
			de301.commit();
			this.query();
			infoOut.setMessage(zafcmsg.UPD_OK);
		} catch (Exception e) {
			this.handleExp(e);
			infoOut.setMessage(e.toString());
		} finally {
			de301.close();
		}
	}

	public void delete() throws Exception {
		dejc301 de301 = new dejc301();
		try {
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			acjcb4DAO acB4DAO = new acjcb4DAO(dsCom, con);
			List list = infoIn.getSequenceVOs("v", "box");
			acB4DAO.removeList(list);
			de301.commit();
			this.query();
			infoOut.setMessage(zafcmsg.DEL_OK);
		} catch (Exception e) {
			this.handleExp(e);
			infoOut.setMessage(e.toString());
		} finally {
			de301.close();
		}
	}
}
