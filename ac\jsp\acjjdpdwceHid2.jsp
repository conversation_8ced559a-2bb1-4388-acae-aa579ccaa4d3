<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%! public static String _AppId = "ACJJDPDWCE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = request.getParameter("compId") == null ? "" :request.getParameter("compId");
	String workArea = request.getParameter("WA") == null ? "" :request.getParameter("WA");
	String yyyymm = request.getParameter("yyyymm") == null ? "" :request.getParameter("yyyymm");
	String yyyy = yyyymm.length() > 4 ? yyyymm.substring(0,4) : "";
%>
<!-- <select name="PRODCODE0" onchange="showTN(this.value)"> -->
<!--<%=ipjcWorkArea.getPDOptions(_dsCom, compId, workArea, "")%> -->
<!--</select> -->

<textarea name="PD" style="display:none">
<%=ipjcWorkArea.getPDChkBoxes(_dsCom, compId, workArea, new String[]{}, "PRODCODE0", " ") %>
</textarea>

<textarea name="EXCPD" style="display:none">
<%=ipjcWorkArea.getCONSPDChkBoxes(_dsCom, compId, yyyymm, workArea, new String[]{}, "EXCONSPD0", " ") %>
</textarea>    

<textarea name="CC" style="display:none">
<select name="CC0" >						
<%=ipjcWorkArea.getCCOptions(_dsCom, compId, yyyy, workArea, "") %>
</select>
</textarea>  
<script>
	if(parent){
	    var obj = parent.document.all("prodCode");
		if(obj!=null)
			obj.innerHTML = document.all("PD").value;

		obj = parent.document.all("EXCONSPD");
		if(obj!=null)
			obj.innerHTML = document.all("EXCPD").value;
			
		obj = parent.document.all("costCenter");
		if(obj!=null)
			obj.innerHTML = document.all("CC").value;
	}
</script>

