package com.icsc.ac.bp;

import java.math.BigDecimal;
import java.util.*;

import com.icsc.ac.dao.*;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.util.zafctool;

public class acjcCostIndicatorBp {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:26:58 $";

	private acjcCostIndicatorDAO ciDAO;
	
	private Map content;
	
	public acjcCostIndicatorBp(dsjccom dsCom) {
		ciDAO = new acjcCostIndicatorDAO(dsCom);
		content = new HashMap();
	}
	
	public void fetchDataByYear(String compId, String yyyy, String rptId) throws Exception {
		content = ciDAO.getYearlyCISetting(compId, yyyy, rptId);		
	}
	
	public void fetchData(String compId, String yyyymm, String rptId) throws Exception {
		content = ciDAO.getMonthlyCISetting(compId, yyyymm, rptId);
	}

	public BigDecimal getUnitQty(String itemId, String wce) {
		acjcCostIndicatorVO ciVO = (acjcCostIndicatorVO) content.get(itemId + "_" + wce);
		return ciVO != null ? ciVO.getUnitQty() : zafctool.ZERO;
	}
	
	public BigDecimal getUnitPrice(String itemId, String wce) {
		acjcCostIndicatorVO ciVO = (acjcCostIndicatorVO) content.get(itemId + "_" + wce);
		return ciVO != null ? ciVO.getUnitPrice() : zafctool.ZERO;
	}
	
	public BigDecimal getUnitCost(String itemId, String wce) {
		acjcCostIndicatorVO ciVO = (acjcCostIndicatorVO) content.get(itemId + "_" + wce);
		return ciVO != null ? ciVO.getUnitCost() : zafctool.ZERO;
	}	
}
