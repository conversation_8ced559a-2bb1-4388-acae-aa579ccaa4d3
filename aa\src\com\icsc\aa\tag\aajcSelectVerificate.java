package com.icsc.aa.tag;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import com.icsc.aa.aajccst;
import com.icsc.aa.dao.aajcC6DAO;
import com.icsc.dpms.de.tag.dejcSelectDataSrc;
import com.icsc.dpms.ds.dsjccom;

public class aajcSelectVerificate extends dejcSelectDataSrc {
	public final static String CLASS_VERSION = "$Id: aajcSelectVerificate.java,v 1.1 2019/01/19 05:25:09 I21312 Exp $";
	
	 public void setDataSrc(dsjccom dsjccom, String s){
	   	try {
	   		aajcC6DAO aaC6DAO =new aajcC6DAO(dsjccom);
			Map[] mp = aaC6DAO.query1(s);
			Map map = new HashMap();
			for(int i=0;i<mp.length;i++){
				map = mp[i];
				String verificate = (String) map.get("VERIFICATE");
				String verificateName = (String) map.get("VERIFICATENAME");
				addOption(verificate, verificateName);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	 }

}
