<%@ page contentType = "text/html;charset=GBK"%>

<%@page import="com.icsc.aa.aajcAPI"%>
<%@ page import="com.icsc.dpms.de.*" %>

<%! String _AppId = "AAJJ01"; %>

<%@ include file="../../jsp/dzjjmain.jsp" %>
<% 
String compId = new aajcAPI(_dsCom).getMainCompId(_dsCom) ;
%>
<link rel="stylesheet" href="<%=_de300.html("aa","/aawz_tree_component.gui")%>" type="text/css"/>
<style>
.tree LI A  {
  padding-left: 23px;
  font-size:12;
}

</style>

<div id="treeBody" style="overflow-x:auto;overflow-y:auto;width:100%;height:100%"></div>

<form name="form1" id="form1" method="post" action="/erp/aa/do?_pageId=aajj01" target="ifrList">	
  <input type="hidden" name="acctCode_qry" value="">
	<input type="hidden" name="_action" value="Q">	  
</form>

<form name="form2" method="post" action="/erp/aa/do?_pageId=aajj01" target="ifrData">
    <input type="hidden" name="acctCode_qry" value="">
    <input type="hidden" name="_action" value="I">	
</form>

</body>
</html>

<script src="<%=_de300.script("aa", "/aajt_css.jss")%>"></script>
<script src="<%=_de300.script("aa", "/aajt_jquery-1.3.2.min.jss")%>"></script>
<script src="<%=_de300.script("aa", "/aajt_tree_component.jss")%>"></script>

<script>
function showList(acctCode){
	form1.acctCode_qry.value = acctCode;
	form1.submit();
}

function showDetail(acctCode){
	form2.acctCode_qry.value = acctCode;
	form2.submit();
}

$().ready(function(e){
	$('#treeBody').tree({
			data:{
				type : "json",
				async : true,
				url : "/erp/aa/do?_pageId=aajjAjax&_action=queryAcctCodeAuthed"+ "&date=" +new Date()
			},
			ui:{
				theme_path : "/erp/html/aa/themes/",
				theme_name : "classic"
			},
			callback : {
				onselect : function(NODE,TREE_OBJ) {
					if($(NODE).attr('VOUCHERYN')=='Y'){
						showDetail($(NODE).attr('id'));
						parent.switchTab("detail") ;	
					}else{
						showList($(NODE).attr('id'));
						parent.switchTab("list") ;				
					}
				}
			}
	 });
});
</script>
