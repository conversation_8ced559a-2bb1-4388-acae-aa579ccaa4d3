package com.icsc.aa.util;

import java.util.Calendar;
import java.util.GregorianCalendar;

public class aajcPriKeyUtil {
	public final static String CLASS_VERSION = "$Id: aajcPriKeyUtil.java,v 1.1 2019/06/21 06:44:32 I21312 Exp $";

    public aajcPriKeyUtil(){}

	public static synchronized String getPriKey(){
		try{
			Thread.sleep(20);
	    }catch(InterruptedException ie){
	        //InterruptedException
	    }

	    Calendar c = new GregorianCalendar();

	    return String.valueOf(c.getTime().getTime());
	}
}
