package com.icsc.ac.func;

import java.sql.Connection;
import java.util.List;

import com.icsc.ac.dao.acjcb5DAO;
import com.icsc.ac.dao.acjcb5VO;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dejc308;
import com.icsc.dpms.de.structs.dejcFunctionalController;
import com.icsc.facc.zaf.bp.zafcmsg;

public class acjcProcessBase02Func extends dejcFunctionalController {
	
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:26:59 $";
	
	private String compId;
	private String year;
	private String process;
    public acjcProcessBase02Func() {
        this.AppId = "ACJCPROCESSBASE02FUNC";
    }
    
	public void init() throws Exception {
		compId=dsCom.companyId;
		year=infoIn.getQryField("year");
		process=infoIn.getQryField("process");
	}
	
	public void end() {
		infoOut.setParameter("year", year);
		infoOut.setParameter("process", process);
	}
	
	public void query() throws Exception {
		try{
			acjcb5DAO acB5DAO=new acjcb5DAO(dsCom);
			List list=acB5DAO.findByProc(compId,year, process);
			de318.logs("query, sql: ", acB5DAO.getSql());
			infoOut.addResultVO("v",list);
			if(list.size()>0){
				infoOut.setMessage(zafcmsg.INQ_OK);
			}else{
				infoOut.setMessage(zafcmsg.ERR_NOT_FOUND);
			}
		}catch(Exception e){
			this.handleExp(e) ;
		}
	}

	public void create() throws Exception {
		dejc301 de301=new dejc301();
		dejc308 de308=new dejc308();
		try{
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			acjcb5DAO acB5DAO=new acjcb5DAO(dsCom,con);
			List list=infoIn.getSequenceVOs("v","box");
			for(int i=0;i<list.size();i++){
				acjcb5VO acB5VO=(acjcb5VO)list.get(i);
				de318.logs("create, vo(" + i + "): ", acB5VO.toString());
				acB5VO.setCompId(compId);
				acB5VO.setProcess(process);
				acB5VO.setFiscalYear(year);
				acB5VO.setCreateId(dsCom.user.ID);
				acB5VO.setLastUpdId(dsCom.user.ID);
				acB5VO.setLastUpdDate(de308.getCrntDateLFmt1());
			}
			acB5DAO.createList(list);
			de301.commit();
			this.query();
			infoOut.setMessage(zafcmsg.IST_OK);
		}catch(Exception e){
			this.handleExp(e) ;
			infoOut.setMessage(e.toString());
		}finally {
			de301.close();
		}
	}
	
	public void update() throws Exception {
		dejc301 de301=new dejc301();
		dejc308 de308=new dejc308();
		try{
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			acjcb5DAO acB5DAO=new acjcb5DAO(dsCom,con);
			List list=infoIn.getSequenceVOs("v","box");
			for(int i=0;i<list.size();i++){
				acjcb5VO acB5VO=(acjcb5VO)list.get(i);
				acB5VO.setLastUpdId(dsCom.user.ID);
				acB5VO.setLastUpdDate(de308.getCrntDateLFmt1());
			}
			acB5DAO.updateList(list);
			de301.commit();
			this.query();
			infoOut.setMessage(zafcmsg.UPD_OK);
		}catch(Exception e){
			this.handleExp(e) ;
			infoOut.setMessage(e.toString());
		}finally {
			de301.close();
		}
	}
	
	public void delete() throws Exception {
		dejc301 de301=new dejc301();
		try{
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			acjcb5DAO acB5DAO=new acjcb5DAO(dsCom,con);
			List list=infoIn.getSequenceVOs("v","box");
			acB5DAO.removeList(list);
			de301.commit();
			this.query();
			infoOut.setMessage(zafcmsg.DEL_OK);
		}catch(Exception e){
			this.handleExp(e) ;
			infoOut.setMessage(e.toString());
		}finally {
			de301.close();
		}
	}
}
