function FloatAdd(arg1, arg2)
{//浮点相加
  var r1, r2, m;
  try { r1 = arg1.toString().split(".")[1].length; } catch (e) { r1 = 0; }
  try { r2 = arg2.toString().split(".")[1].length; } catch (e) { r2 = 0; }
  m = Math.pow(10, Math.max(r1, r2));
  return (FloatMul(arg1, m) + FloatMul(arg2, m)) / m;
}

//浮點數相減
function FloatSub(arg1, arg2)
{
  var r1, r2, m, n;
  try { r1 = arg1.toString().split(".")[1].length } catch (e) { r1 = 0 }
  try { r2 = arg2.toString().split(".")[1].length } catch (e) { r2 = 0 }
  m = Math.pow(10, Math.max(r1, r2));
  n = (r1 >= r2) ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

//浮點數相乘
function FloatMul(arg1, arg2)
{
  var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
  try { m += s1.split(".")[1].length; } catch (e) { }
  try { m += s2.split(".")[1].length; } catch (e) { }
  return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
}
 
//浮點數相除
function FloatDiv(arg1, arg2)
{
  var t1 = 0, t2 = 0, r1, r2;
  try { t1 = arg1.toString().split(".")[1].length } catch (e) { }
  try { t2 = arg2.toString().split(".")[1].length } catch (e) { }
  with (Math)
  {
    r1 = Number(arg1.toString().replace(".", ""))
    r2 = Number(arg2.toString().replace(".", ""))
    return (r1 / r2) * pow(10, t2 - t1);
  }
}

//去掉数字的逗号千分位
function loop(t) {
	return t.toString().replace(/\,/g,"");
}

//去掉开头或结尾的空格
function trim(t) {
	return t.toString().replace(/^\s+|\s+$/g,"");
}

//将数字变成千分位格式
function decimal(t, d) {
	var r = '';
	
	t = trim(loop(t));
	
	if (!checkDec(t)) {	
		return t;
	}
	
	var minus = false;
	
	if (t.indexOf("-")==0) {
		minus = true;
		t = t.toString().substring(1);
	}
	
	var s = t.toString().split(".");
	
	for (var i = s[0].length; i > 0; i--) {
		if (i!=s[0].length && (s[0].length-i)%3==0) {
			r = ',' + r;
		}
		r = s[0].charAt([i-1]) + r;
	}
	
	if (s.length==1) {
		s[1] = '';
	}
	
	r = r + '.';
	
	if (s[1].length > d) {
		r = r + s[1].substring(0, d);
	} else {
		r = r + s[1];
		for (var i = s[1].length; i < d; i++) {
			r = r + '0'; 
		}
	}
	
	if (minus) {
		r = '-' + r;
	}
	
	return r;
}
//检查是否为数字
function checkDec(t) {
	if (trim(t).indexOf(".")==0) {
		t = "0" + t;
	}
	
	var rex = /^[\-]?\d+\.?\d*$/g;
	
	if (!rex.test(t)) {
		return false;
	}
	return true;
}