<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ACJJORDERLIST";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String seqNo = request.getParameter("seqNo")==null ? "" :request.getParameter("seqNo");
	String compId = request.getParameter("compId")==null ? "" :request.getParameter("compId");
	String dataDate = zafctool.parseWYymm(request.getParameter("dataDate"), 1);
	
	String WA = request.getParameter("WA")==null ? "" :request.getParameter("WA");
	String CC = request.getParameter("CC")==null ? "" :request.getParameter("CC");
	String PD = request.getParameter("PD")==null ? "" :request.getParameter("PD");
%>
<textarea name="paraa" style="display:none">
	<select name="costCenter<%=seqNo%>_v1" style="width: 95%">
	<%=ipjcWorkArea.getCCOptions(_dsCom,compId,dataDate,WA,CC)%>
	</select>
</textarea> 
<textarea name="parab" style="display:none">
	<select name="activityCode<%=seqNo%>_v1" style="width: 95%">
	<%=ipjcWorkArea.getPDOptions(_dsCom,compId,WA,PD)%>
	</select>
</textarea> 

<script>
	if(parent){
		parent.document.all("CC<%=seqNo%>").innerHTML = document.all("paraa").value;
		parent.document.all("PD<%=seqNo%>").innerHTML = document.all("parab").value;
		//parent.show2Combo<%=seqNo%>();
	}
	  

</script>