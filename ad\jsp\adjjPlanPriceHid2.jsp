<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ADJJPLANPRICE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = zafctool.trim(request.getParameter("compId"));
	String workArea = zafctool.trim(request.getParameter("workArea"));
	String prodCode = zafctool.trim(request.getParameter("prodCode"));
%>
<textarea name="CHILDSELECT">
	<select name="prodCode_qry" onchange="showTN(this.value)">
		<%=ipjcWorkArea.getAllPDOptions(_dsCom, compId, workArea, prodCode)%>
	</select>
</textarea>  

<script>
	if(parent){
		parent.document.all("PD").innerHTML = document.all("CHILDSELECT").value;
	}
</script>