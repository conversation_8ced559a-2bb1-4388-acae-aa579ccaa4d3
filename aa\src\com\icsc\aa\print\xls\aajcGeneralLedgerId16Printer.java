package com.icsc.aa.print.xls;

import java.util.List;
import java.util.Map;

import com.icsc.aa.dao.aajcAcctData4Id16DAO;
import com.icsc.aa.dao.aajcAcctDataDAO;
import com.icsc.aa.util.aajcUtil;
import com.icsc.dpms.ds.dsjccom;

public class aajcGeneralLedgerId16Printer extends aajcGeneralLedgerIdPrinter{
	public final static String CLASS_VERSION = "$Revision: 1.3 $ $Date: 2018/03/26 05:33:25 $ $Author: I21312 $";

	protected String showGpAttr;
	
	public aajcGeneralLedgerId16Printer(dsjccom dsCom, Map map) throws Exception {
		super(dsCom, map);
		showGpAttr = aajcUtil.getString(map.get("showGpAttr"));
	}

    @Override
    protected List<Map<String, Object>> getData() throws Exception {
    	aajcAcctDataDAO dao = new aajcAcctData4Id16DAO(dsCom, map);
    	return dao.queryData();
    }      
  
}
