<%@ page contentType = "text/html;charset=GBK"%>

<%@ page import="com.icsc.dpms.de.*" %>
<%@ page import="com.icsc.ap.prt.*" %>
<%@ page import="com.icsc.ap.dao.*" %>
<%@ page import="com.icsc.dpms.dr.engine.*" %>

<%! public static final String _AppId = "APJJBILLPRINT"; %>

<%@ include file="apjjmain.jsp" %>

<%
String compId=request.getParameter("compId")==null ? "" : request.getParameter("compId");
String billNo=request.getParameter("billNo")==null ? "" : request.getParameter("billNo");
dejc318 de318 = new dejc318(_dsCom, _dsCom.appId);
apjcBillVO apBillVO = new apjcBillDAO(_dsCom).findByPK(compId, billNo);

if(apBillVO!=null && apBillVO.getBillType().equals("")){
    apjcBillPrint apBillPrint = new apjcBillPrint(_dsCom,compId,billNo);
    Map map = apBillPrint.getBillMapData();

    request.setAttribute("appletMap", map);
%>

<%@ include file="../../aa/jsp/aajjApplet.jsp" %>

<%}else{
	de318.logs("", " in!!!! ");
	String pdfUrl = "";
	try {
		drjcRptUtil drUtil = new drjcRptUtil(_dsCom);

        //boolean isOK = drUtil.genReportByVOList(new ArrayList<HashMap<String, String>>(), "ap/xml/dr/apjrbill.xml", "", new HashMap(), drjcRptUtilPara.PDFfmt);
//         System.out.println("finished=" + isOK);
		//drUtil.genReportByVOList(dataList, fileString, outFileName, map, drjcRptUtilPara.PDFfmt);
		de318.logs("", " in!!!! try ");
   		apjcBillPrint apBillPrint = new apjcBillPrint(_dsCom,compId,billNo);de318.logs("", " in!!!! ini ");
    	pdfUrl = apBillPrint.printPdf(); 
    	de318.logs("", pdfUrl);
	} catch(Exception e) {
		de318.logs("", " ex " + e);
	}de318.logs("", "out : " + pdfUrl);
%>
<script>
window.location.href = "<%=pdfUrl%>";
</script>
</body>
</html>
<%} %>
