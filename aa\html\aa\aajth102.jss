function deHiddenList(selectName) {
	this.values=new Array()
	this.selectName=selectName
	this.add=function(value,text) {
		this.values[this.values.length]=new deHiddenItem(value,text) 		
	}
	this.draw=function(orderNo) {
		var html="<select name='"+this.selectName+orderNo+"'>" 
		var selected=""
		var v=tr(document.getElementById(this.selectName+orderNo).innerText)
		for(var i=0;i<this.values.length;i++) {
			if (v==this.values[i].text)
				selected="selected"
			else
				selected=""
			html+="<option value=\""+this.values[i].value+"\" "+selected+" >"+this.values[i].text ;
		}
		html+="</select>"
		return html ;
	}
}
function deHiddenItem(value,text) {
	this.value=value
	this.text=text
}

function deListGroup() {
	this.selectGroup=new Array()
	this.add=function(hiddenList) {
		this.selectGroup[this.selectGroup.length]=hiddenList
	}
	this.targets=new Array() ;
	
	this.change=function (orderNo) {		
		for(var i=0;i<this.targets.length;i++) {
			var tar=document.getElementById(this.targets[i]+orderNo) 
			if (event.srcElement.checked)
				tar.innerHTML=this.selectGroup[i].draw(orderNo) ;
			else
				tar.innerHTML=tar.children[0].options[tar.children[0].selectedIndex].text ;
		}
	}
	this.end=function() {
		for(var i=0;i<this.selectGroup.length;i++) {
			this.targets[this.targets.length]=this.selectGroup[i].selectName ;
		}
	}
}
