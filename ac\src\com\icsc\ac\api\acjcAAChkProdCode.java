package com.icsc.ac.api;

import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;
import com.icsc.ip.bp.ipjcProdCode;
import com.icsc.ip.dao.ipjcpdBaseVO;
import java.util.*;

public class acjcAAChkProdCode implements dejiKey, dejiCode {
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:27:03 $";

	HashMap mp = new HashMap();

	public void put(int index, Object value) throws dejcEditException {
		mp.put(String.valueOf(index), (String) value);
	}

	public java.lang.Object get(int index) throws dejcNotFoundException {
		return mp.get(String.valueOf(index));
	}

	public boolean isExist(dsjccom dsCom, dejiKey dekey) throws dejcDAOException {
		try {
			ipjcpdBaseVO vo = ipjcProdCode.getPdBaseVo(dsCom, (String) dekey.get(1), (String) dekey.get(2));
			return vo.getProdCode().equals("") ? false : true;
		} catch (Exception e) {
			throw new dejcDAOException(e.toString());
		}
	}

	public java.lang.String getName(dsjccom dsCom, dejiKey dekey) throws dejcNotFoundException, dejcDAOException {
		try {
			ipjcpdBaseVO vo = ipjcProdCode.getPdBaseVo(dsCom, (String) dekey.get(1), (String) dekey.get(2));
			if (vo == null)
				return "";
			else
				return vo.getProdCodeDesc();
		} catch (Exception e) {
			throw new dejcDAOException(e.toString());
		}
	}
} // end class
