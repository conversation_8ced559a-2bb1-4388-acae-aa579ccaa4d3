package com.icsc.ac.bp;

import java.sql.Connection;

import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.ds.*;

public class acjcKey {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:26:58 $";

	private String compId, fiscalYear, costCenter, acProdCode, closeMonth;

	private dsjccom dsCom;

	private dejc318 de318;

	private Connection con;

	public acjcKey(dsjccom dsCom) {
		this.dsCom = dsCom;
		this.de318 = new dejc318(dsCom, CLASS_VERSION);
	}

	public acjcKey(dsjccom dsCom, Connection con) {
		this(dsCom);
		this.con = con;
	}

	public String getCompId() {
		return this.compId == null ? "" : this.compId;
	}

	public void setCompId(String compId) {
		this.compId = compId;
	}

	public String getFiscalYear() {
		return this.fiscalYear == null ? "" : this.fiscalYear;
	}

	public void setFiscalYear(String fiscalYear) {
		this.fiscalYear = fiscalYear;
	}

	public String getCostCenter() {
		return this.costCenter == null ? "" : this.costCenter;
	}

	public void setCostCenter(String costCenter) {
		this.costCenter = costCenter;
	}

	public String getAcProdCode() {
		return this.acProdCode == null ? "" : this.acProdCode;
	}

	public void setAcProdCode(String acProdCode) {
		this.acProdCode = acProdCode;
	}

	public dsjccom getDsCom() {
		return this.dsCom;
	}

	public dejc318 getDe318() {
		return this.de318;
	}

	public Connection getConnection() {
		return this.con;
	}

	public String getCloseMonth() {
		return this.closeMonth;
	}

	public void setCloseMonth(String closeMonth) {
		this.closeMonth = closeMonth;
	}

	public String toString() {
		String s = "\ncompId==" + compId + "\n";
		s += ("fiscalYear==" + fiscalYear + "\n");
		s += ("acProdCode==" + acProdCode + "\n");
		s += ("costCenter==" + costCenter + "\n");
		s += ("closeMonth==" + closeMonth + "\n");
		return s;
	}

}
