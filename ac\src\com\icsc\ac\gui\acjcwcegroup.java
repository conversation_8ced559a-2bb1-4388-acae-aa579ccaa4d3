package com.icsc.ac.gui;

import com.icsc.dpms.ds.dsjccom;
import com.icsc.ip.util.ipjcgetSelectValue;

public class acjcwcegroup {
	// public static final String CHILDTYPE_WA = "WA";

	public static final String RPT_LEAFID = "120106";

	// public static String getChildSelect(dsjccom dsCom, String compId, String
	// childType, String defV) {
	// if (childType.equals(CHILDTYPE_WA)) {
	// return getWASelect(dsCom, compId, defV);
	// }
	// return "";
	// }

	// public static String getWASelect(dsjccom dsCom, String compId, String
	// defV) {
	// String s = "<select name='key2_qry' style='width: 95%'>";
	// s += ipjcWorkArea.getWorkAreaByFactory(dsCom, dsCom.companyId, defV);
	// s += "</select>";
	// return s;
	// }

	public static String getRPTOptions(dsjccom dsCom, String defV) {
		String sql = "select itemA, dataA, dataB from db.tbzafrd where leafId='" + RPT_LEAFID + "' and compId='"
				+ dsCom.companyId + "' and STATUS='Y'";
		sql += " order by dataSeq";
		return ipjcgetSelectValue.getOptions(dsCom, sql, defV, " ", " ").toString();
	}
}
