CREATE TABLE DB.TBAAT2 (
compId VARCHAR (10) DEFAULT '' NOT NULL,vchrDate VARCHAR (8) DEFAULT '' NOT NULL,vchrNo VARCHAR (15) DEFAULT '' NOT NULL,tradeNo VARCHAR (15) DEFAULT '',apId VARCHAR (2) DEFAULT '',pgrmId VARCHAR (8) DEFAULT '',vchrDesc LVARCHAR(500) DEFAULT '',statusCode VARCHAR (1) DEFAULT '',bizNo VARCHAR (10) DEFAULT '',billNo VARCHAR (15) DEFAULT '',createDateTime VARCHAR (14) DEFAULT '',createUserId VARCHAR (10) DEFAULT '',updateDateTime VARCHAR (14) DEFAULT '',updateDateUserId VARCHAR (10) DEFAULT '',postDate VARCHAR (8) DEFAULT '',potstDateUserId VARCHAR (10) DEFAULT '',printYn VARCHAR (1) DEFAULT '',printCount DECIMAL (3,0)  DEFAULT 0 ,vchrType VARCHAR (1) DEFAULT '',confirmUserId VARCHAR (10) DEFAULT '',appendix DECIMAL (3,0)  DEFAULT 0 ,auditDate VARCHAR (8) DEFAULT '',auditor VARCHAR (10) DEFAULT '',closeUserId VARCHAR (10) DEFAULT '',commonVchr VARCHAR (1) DEFAULT '',confirmDate VARCHAR (8) DEFAULT '', PRIMARY KEY(compId,vchrDate,vchrNo) 
) ;
GRANT ALL ON DB.TBAAT2 TO JAVAUSER ;
