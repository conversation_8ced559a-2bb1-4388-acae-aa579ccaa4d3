<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="aajrVchrA4L"
		 columnCount="1"
		 printOrder="Horizontal"
		 orientation="Landscape"
		 pageWidth="842"
		 pageHeight="595"
		 columnWidth="595"
		 columnSpacing="0"
		 leftMargin="0"
		 rightMargin="0"
		 topMargin="0"
		 bottomMargin="0"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.encoding" value="UTF-8" />
	<property name="ireport.scriptlethandling" value="0" />
	<parameter name="isPrintBackgroundImg" isForPrompting="false" class="java.lang.Boolean"/>
	<field name="vchrDate" class="java.lang.String"/>
	<field name="vchrNo" class="java.lang.String"/>
	<field name="title" class="java.lang.String"/>
	<field name="compName" class="java.lang.String"/>
	<field name="potstDateUserId" class="java.lang.String"/>
	<field name="auditor" class="java.lang.String"/>
	<field name="confirmUserId" class="java.lang.String"/>
	<field name="sumWord" class="java.lang.String"/>
	<field name="appendix" class="java.lang.String"/>
	<field name="srlDesc1" class="java.lang.String"/>
	<field name="srlDesc2" class="java.lang.String"/>
	<field name="srlDesc3" class="java.lang.String"/>
	<field name="srlDesc4" class="java.lang.String"/>
	<field name="srlDesc5" class="java.lang.String"/>
	<field name="acct1" class="java.lang.String"/>
	<field name="acct2" class="java.lang.String"/>
	<field name="acct3" class="java.lang.String"/>
	<field name="acct4" class="java.lang.String"/>
	<field name="acct5" class="java.lang.String"/>
	<field name="drAmt1" class="java.math.BigDecimal"/>
	<field name="drAmt2" class="java.math.BigDecimal"/>
	<field name="drAmt3" class="java.math.BigDecimal"/>
	<field name="drAmt4" class="java.math.BigDecimal"/>
	<field name="drAmt5" class="java.math.BigDecimal"/>
	<field name="crAmt1" class="java.math.BigDecimal"/>
	<field name="crAmt2" class="java.math.BigDecimal"/>
	<field name="crAmt3" class="java.math.BigDecimal"/>
	<field name="crAmt4" class="java.math.BigDecimal"/>
	<field name="crAmt5" class="java.math.BigDecimal"/>
	<field name="crcyUnit" class="java.lang.String"/>
	<field name="drAmtSum" class="java.math.BigDecimal"/>
	<field name="crAmtSum" class="java.math.BigDecimal"/>
		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="500"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="60"
						y="22"
						width="526"
						height="45"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-1"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" isStyledText="true" lineSpacing="Single">
						<font fontName="宋体" pdfFontName="STSong-Light" size="24" isBold="true" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{title}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="60"
						y="130"
						width="172"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-2"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{srlDesc1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="60"
						y="246"
						width="172"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-3"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{srlDesc3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="60"
						y="304"
						width="172"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-4"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{srlDesc4}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="60"
						y="363"
						width="172"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-5"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{srlDesc5}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="60"
						y="188"
						width="172"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-6"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{srlDesc2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="780"
						y="146"
						width="14"
						height="60"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-8"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{appendix}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="237"
						y="130"
						width="330"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-9"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{acct1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="237"
						y="188"
						width="330"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-10"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{acct2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="237"
						y="247"
						width="330"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-11"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{acct3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="237"
						y="305"
						width="330"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-12"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{acct4}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="237"
						y="363"
						width="330"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-13"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{acct5}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="572"
						y="130"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-14"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{drAmt1}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{drAmt1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="572"
						y="188"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-15"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{drAmt2}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{drAmt2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="572"
						y="247"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-16"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{drAmt3}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{drAmt3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="572"
						y="305"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-17"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{drAmt4}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{drAmt4}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="572"
						y="363"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-18"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{drAmt5}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{drAmt5}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="675"
						y="130"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-19"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{crAmt1}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{crAmt1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="675"
						y="188"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-20"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{crAmt2}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{crAmt2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="675"
						y="247"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-21"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[(new Boolean($F{crAmt3}.compareTo( new BigDecimal("0") )!=0))]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{crAmt3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="675"
						y="305"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-22"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{crAmt4}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{crAmt4}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="675"
						y="363"
						width="100"
						height="54"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-23"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{crAmt5}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{crAmt5}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="150"
						y="448"
						width="150"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-24"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{potstDateUserId}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="390"
						y="448"
						width="150"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-25"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{auditor}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="631"
						y="448"
						width="150"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-26"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{confirmUserId}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="590"
						y="42"
						width="170"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-28"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{crcyUnit}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="205"
						y="422"
						width="363"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-29"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{sumWord}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="590"
						y="62"
						width="170"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-30"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{vchrNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="590"
						y="22"
						width="170"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-31"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{vchrDate}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="572"
						y="422"
						width="100"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-32"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{drAmtSum}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{drAmtSum}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="675"
						y="422"
						width="100"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-33"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="true">
							<printWhenExpression><![CDATA[new Boolean($F{crAmtSum}.compareTo( new BigDecimal("0") )!=0)]]></printWhenExpression>
						</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{crAmtSum}]]></textFieldExpression>
				</textField>
				<rectangle radius="0" >
					<reportElement
						mode="Transparent"
						x="58"
						y="90"
						width="720"
						height="354"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="rectangle-1"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</rectangle>
				<staticText>
					<reportElement
						mode="Opaque"
						x="60"
						y="67"
						width="50"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-9"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[核算单位]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="110"
						y="67"
						width="20"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-10"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Bottom" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Unit]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None" >					<reportElement
						mode="Opaque"
						x="130"
						y="67"
						width="457"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-34"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="false" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{compName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						mode="Opaque"
						x="60"
						y="92"
						width="172"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-11"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[摘    要]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="60"
						y="109"
						width="172"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-12"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Summary]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="237"
						y="109"
						width="330"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-13"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Account]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="237"
						y="92"
						width="330"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-14"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[会计科目]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="572"
						y="92"
						width="100"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-15"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[借 方]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="572"
						y="109"
						width="100"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-16"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Debit]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="675"
						y="109"
						width="100"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-17"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Credit]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="675"
						y="92"
						width="100"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-18"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[贷 方]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="235"
						y="91"
						width="0"
						height="329"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-1"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="674"
						y="91"
						width="0"
						height="352"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-2"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="570"
						y="91"
						width="0"
						height="352"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-3"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="59"
						y="128"
						width="718"
						height="0"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-4"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="59"
						y="186"
						width="718"
						height="0"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-5"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="59"
						y="245"
						width="718"
						height="0"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-6"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="59"
						y="303"
						width="718"
						height="0"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-7"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="59"
						y="361"
						width="718"
						height="0"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-8"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<staticText>
					<reportElement
						mode="Opaque"
						x="60"
						y="422"
						width="120"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-19"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[合计]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="180"
						y="422"
						width="24"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-20"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Total]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						mode="Opaque"
						x="59"
						y="420"
						width="718"
						height="0"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="line-9"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<graphicElement stretchType="NoStretch" pen="Thin" fill="Solid" />
				</line>
				<staticText>
					<reportElement
						mode="Opaque"
						x="60"
						y="448"
						width="40"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-21"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[记账人]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="100"
						y="448"
						width="50"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-22"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Bottom" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Recorded By]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="300"
						y="448"
						width="40"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-23"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[覆核人]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="340"
						y="448"
						width="50"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-24"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Bottom" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Checked By]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="541"
						y="448"
						width="40"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-25"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="12" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[制单人]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="581"
						y="448"
						width="50"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-26"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Bottom" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Produced By]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="780"
						y="92"
						width="14"
						height="40"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-27"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[附
单
据
数]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="780"
						y="132"
						width="14"
						height="14"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-28"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Right" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[(]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="780"
						y="206"
						width="14"
						height="14"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-29"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[(]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="780"
						y="220"
						width="14"
						height="20"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-30"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[张]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="780"
						y="240"
						width="14"
						height="80"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-31"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Right" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[Number of Tickets]]></text>
				</staticText>
				<staticText>
					<reportElement
						mode="Opaque"
						x="780"
						y="370"
						width="14"
						height="60"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-32"
						stretchType="NoStretch"
						positionType="FixRelativeToTop"
						isPrintRepeatedValues="true"
						isRemoveLineWhenBlank="false"
						isPrintInFirstWholeBand="false"
						isPrintWhenDetailOverflows="false"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="Left" lineSpacing="Single">
						<font fontName="SimSun" pdfFontName="STSong-Light" size="8" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<text><![CDATA[金额记账凭证]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
