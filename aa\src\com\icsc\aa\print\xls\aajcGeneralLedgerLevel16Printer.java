package com.icsc.aa.print.xls;

import java.util.List;
import java.util.Map;

import com.icsc.aa.dao.aajcAcctData4Level16DAO;
import com.icsc.aa.dao.aajcAcctDataDAO;
import com.icsc.dpms.ds.dsjccom;

public class aajcGeneralLedgerLevel16Printer extends aajcGeneralLedgerLevelPrinter{
	public final static String CLASS_VERSION = "$Id: aajcGeneralLedgerLevel16Printer.java,v 1.3 2018/03/26 05:58:49 I21312 Exp $";
	
    protected String bachNo;
    protected aajcAcctDataDAO dao;
    protected String topLevel;
    
	public aajcGeneralLedgerLevel16Printer(dsjccom dsCom, Map map) throws Exception {
		super(dsCom, map);
		
		bachNo = System.currentTimeMillis()+"";
		dao = new aajcAcctData4Level16DAO(dsCom, map);
		Map<String, Object> additionDataMap = dao.getAdditionData(map);
		topLevel = (String) additionDataMap.get("level");
	}
	
	@Override
    protected List<Map<String, Object>> getData() throws Exception {
		aajcAcctDataDAO dao = new aajcAcctData4Level16DAO(dsCom, map);
    	return dao.queryData();
    }
	
}
