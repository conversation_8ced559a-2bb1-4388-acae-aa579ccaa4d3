<component name="libraryTable">
  <library name="bg2">
    <CLASSES>
      <root url="file://$PROJECT_DIR$/WEB-INF/lib" />
      <root url="file://$PROJECT_DIR$/WEB-INF/lib/bg2dilib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="file://$PROJECT_DIR$/WEB-INF/lib" />
      <root url="file://$PROJECT_DIR$/WEB-INF/lib/bg2dilib" />
    </SOURCES>
    <jarDirectory url="file://$PROJECT_DIR$/WEB-INF/lib" recursive="false" />
    <jarDirectory url="file://$PROJECT_DIR$/WEB-INF/lib/bg2dilib" recursive="false" />
    <jarDirectory url="file://$PROJECT_DIR$/WEB-INF/lib" recursive="false" type="SOURCES" />
    <jarDirectory url="file://$PROJECT_DIR$/WEB-INF/lib/bg2dilib" recursive="false" type="SOURCES" />
  </library>
</component>