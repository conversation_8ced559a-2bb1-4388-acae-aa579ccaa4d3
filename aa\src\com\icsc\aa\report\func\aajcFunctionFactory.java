/**
 * 
 */
package com.icsc.aa.report.func;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import com.icsc.aa.report.aajcReportContext;
import com.icsc.aa.report.func.aajcFunctionLoader.function;
import com.icsc.dpms.ds.dsjccom;

/**
 * <AUTHOR>
 *
 */
public class aajcFunctionFactory {
	Map functionMap = new HashMap();
	
	aajcFunctionLoader aaFunctionLoader;
	
	/**
	 * 
	 */
	public aajcFunctionFactory(aajcReportContext aaReportContext) {
		aaFunctionLoader = new aajcFunctionLoader(aaReportContext); 
	}
	
	public function getMethod(String funcName,Class[] paras) throws SQLException, Exception{
		function function;
		
		String funcIndex = funcName;
		for(int i=0;i<paras.length;i++){
			Class className = paras[i];
			funcIndex = funcIndex + "-" + className.getName();
		}
		if(paras.length==0){
			funcIndex = funcIndex + "-";
		}
		if(functionMap.containsKey(funcIndex)){
			function =  (function) functionMap.get(funcIndex);
		}else{
			function = aaFunctionLoader.load(funcIndex,funcName, paras);
			
			functionMap.put(funcIndex, function);
		}
		
		return function;
	}
	
	/**
	 * @param args
	 */
	public static void main(String[] args) {
		dsjccom dscom = new dsjccom();

        dscom.companyId = "bx";
		dscom.appId = "AA";
		dscom.linkType = 1;
		dscom.user.ID = "icsc01";
		dscom.db.driver = "COM.ibm.db2.jdbc.app.DB2Driver";
		dscom.db.url = "jdbc:db2:dwctrldb";
		dscom.db.userId = "db2admin";
		dscom.db.passwd = "db2admin";
		dscom.db.linkFlag = true;
		
        aajcReportContext aaReportContext = aajcReportContext.buildContext(dscom);
        
		aajcFunctionFactory aaFunctionFactory=  new aajcFunctionFactory(aaReportContext);
		
		Class[] classs = new Class[2];
		classs[0] = String.class;
		classs[1] = String.class;
		try {
			function func = aaFunctionFactory.getMethod("KMZ_AMT",classs );
			
			System.out.println(func.getMethod());
			
			System.out.println(func.getMethod().invoke(func.getObject(), new Object[]{"222","44"}));
			
			function func2 = aaFunctionFactory.getMethod("QC",classs );
			System.out.println(func2.getMethod().invoke(func2.getObject(), new Object[]{"222","44"}));
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		

	}

}
