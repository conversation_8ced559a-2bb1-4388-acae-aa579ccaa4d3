rem DAOTool Ver 5.1125
rem DB.TBAAC2 的 新增 Table 的 sql
rem 106/06/12
rem (INPUT FILE VERSION:1.0)

rem DROP TABLE DB.TBAAC2 ;
rem RENAME TABLE DB.TBAAC2 TO $TABLE_NAME_OLD;

CREATE TABLE DB.TBAAC2 (pCompId VARCHAR (10) NOT NULL WITH DEFAULT '',compId VARCHAR (10) NOT NULL WITH DEFAULT '',checkCode VARCHAR (20) NOT NULL WITH DEFAULT '',fieldCode VARCHAR (20) NOT NULL WITH DEFAULT '',fieldCodeName VARCHAR (100) WITH DEFAULT '',fieldOperator VARCHAR (20) WITH DEFAULT '',helpCategory VARCHAR (20) WITH DEFAULT '',helpSql VARCHAR (200) WITH DEFAULT '',helpJsp VARCHAR (200) WITH DEFAULT '',helpPara VARCHAR (200) WITH DEFAULT '',defaultA VARCHAR (100) WITH DEFAULT '',defaultB VARCHAR (100) WITH DEFAULT '',keyInA VARCHAR (100) WITH DEFAULT '',keyInB VARCHAR (100) WITH DEFAULT '',remark VARCHAR (200) WITH DEFAULT '',settingA VARCHAR (30) WITH DEFAULT '',settingB VARCHAR (30) WITH DEFAULT '',settingC VARCHAR (30) WITH DEFAULT '',updateEmpNo VARCHAR (10) WITH DEFAULT '',updateTime VARCHAR (20) WITH DEFAULT '', PRIMARY KEY(pCompId,compId,checkCode,fieldCode) ) DATA CAPTURE NONE  ;

GRANT  CONTROL ON TABLE DB.TBAAC2 TO USER JAVAUSER ;
GRANT  SELECT,INSERT,UPDATE,DELETE,ALTER,INDEX,REFERENCES ON TABLE DB.TBAAC2 TO USER JAVAUSER WITH GRANT OPTION ;
