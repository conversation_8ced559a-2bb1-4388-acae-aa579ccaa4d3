// $ANTLR 3.1.2 D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g 2012-06-15 17:11:56

package com.icsc.aa.report.expression;


import org.antlr.runtime.*;
import java.util.Stack;
import java.util.List;
import java.util.ArrayList;

public class aajcExprssionLexer extends Lexer {
    public static final int LT=5;
    public static final int T__26=26;
    public static final int T__25=25;
    public static final int T__24=24;
    public static final int T__23=23;
    public static final int T__22=22;
    public static final int T__21=21;
    public static final int T__20=20;
    public static final int LTEQ=6;
    public static final int CHAR=16;
    public static final int NOTEQUALS=17;
    public static final int CELL=12;
    public static final int EQUALS=4;
    public static final int INT=11;
    public static final int GTEQ=8;
    public static final int ID=13;
    public static final int EOF=-1;
    public static final int Float=9;
    public static final int WS=19;
    public static final int NEWLINE=18;
    public static final int GT=7;
    public static final int CHINESE=15;
    public static final int METHOD=14;
    public static final int STRING=10;

    // delegates
    // delegators

    public aajcExprssionLexer() {;} 
    public aajcExprssionLexer(CharStream input) {
        this(input, new RecognizerSharedState());
    }
    public aajcExprssionLexer(CharStream input, RecognizerSharedState state) {
        super(input,state);

    }
    public String getGrammarFileName() { return "D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g"; }

    // $ANTLR start "T__20"
    public final void mT__20() throws RecognitionException {
        try {
            int _type = T__20;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:3:7: ( '+' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:3:9: '+'
            {
            match('+'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__20"

    // $ANTLR start "T__21"
    public final void mT__21() throws RecognitionException {
        try {
            int _type = T__21;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:4:7: ( '-' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:4:9: '-'
            {
            match('-'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__21"

    // $ANTLR start "T__22"
    public final void mT__22() throws RecognitionException {
        try {
            int _type = T__22;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:5:7: ( '*' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:5:9: '*'
            {
            match('*'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__22"

    // $ANTLR start "T__23"
    public final void mT__23() throws RecognitionException {
        try {
            int _type = T__23;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:6:7: ( '/' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:6:9: '/'
            {
            match('/'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__23"

    // $ANTLR start "T__24"
    public final void mT__24() throws RecognitionException {
        try {
            int _type = T__24;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:7:7: ( '(' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:7:9: '('
            {
            match('('); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__24"

    // $ANTLR start "T__25"
    public final void mT__25() throws RecognitionException {
        try {
            int _type = T__25;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:8:7: ( ')' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:8:9: ')'
            {
            match(')'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__25"

    // $ANTLR start "T__26"
    public final void mT__26() throws RecognitionException {
        try {
            int _type = T__26;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:9:7: ( ',' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:9:9: ','
            {
            match(','); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "T__26"

    // $ANTLR start "METHOD"
    public final void mMETHOD() throws RecognitionException {
        try {
            int _type = METHOD;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:204:8: ( ( 'A' .. 'Z' | 'a' .. 'z' | '_' | CHINESE )+ )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:204:10: ( 'A' .. 'Z' | 'a' .. 'z' | '_' | CHINESE )+
            {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:204:10: ( 'A' .. 'Z' | 'a' .. 'z' | '_' | CHINESE )+
            int cnt1=0;
            loop1:
            do {
                int alt1=2;
                int LA1_0 = input.LA(1);

                if ( ((LA1_0>='A' && LA1_0<='Z')||LA1_0=='_'||(LA1_0>='a' && LA1_0<='z')||(LA1_0>='\u4E00' && LA1_0<='\u9FFF')) ) {
                    alt1=1;
                }


                switch (alt1) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:
                    {
                    if ( (input.LA(1)>='A' && input.LA(1)<='Z')||input.LA(1)=='_'||(input.LA(1)>='a' && input.LA(1)<='z')||(input.LA(1)>='\u4E00' && input.LA(1)<='\u9FFF') ) {
                        input.consume();

                    }
                    else {
                        MismatchedSetException mse = new MismatchedSetException(null,input);
                        recover(mse);
                        throw mse;}


                    }
                    break;

                default :
                    if ( cnt1 >= 1 ) break loop1;
                        EarlyExitException eee =
                            new EarlyExitException(1, input);
                        throw eee;
                }
                cnt1++;
            } while (true);


            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "METHOD"

    // $ANTLR start "CELL"
    public final void mCELL() throws RecognitionException {
        try {
            int _type = CELL;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:205:6: ( 'A' .. 'Z' INT | 'A' .. 'I' 'A' .. 'Z' INT )
            int alt2=2;
            int LA2_0 = input.LA(1);

            if ( ((LA2_0>='A' && LA2_0<='I')) ) {
                int LA2_1 = input.LA(2);

                if ( ((LA2_1>='A' && LA2_1<='Z')) ) {
                    alt2=2;
                }
                else if ( ((LA2_1>='0' && LA2_1<='9')) ) {
                    alt2=1;
                }
                else {
                    NoViableAltException nvae =
                        new NoViableAltException("", 2, 1, input);

                    throw nvae;
                }
            }
            else if ( ((LA2_0>='J' && LA2_0<='Z')) ) {
                alt2=1;
            }
            else {
                NoViableAltException nvae =
                    new NoViableAltException("", 2, 0, input);

                throw nvae;
            }
            switch (alt2) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:205:8: 'A' .. 'Z' INT
                    {
                    matchRange('A','Z'); 
                    mINT(); 

                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:205:23: 'A' .. 'I' 'A' .. 'Z' INT
                    {
                    matchRange('A','I'); 
                    matchRange('A','Z'); 
                    mINT(); 

                    }
                    break;

            }
            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "CELL"

    // $ANTLR start "ID"
    public final void mID() throws RecognitionException {
        try {
            int _type = ID;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:206:5: ( '$' ( CHAR | CHINESE )+ )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:206:9: '$' ( CHAR | CHINESE )+
            {
            match('$'); 
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:206:13: ( CHAR | CHINESE )+
            int cnt3=0;
            loop3:
            do {
                int alt3=2;
                int LA3_0 = input.LA(1);

                if ( (LA3_0==' '||LA3_0=='.'||(LA3_0>='A' && LA3_0<='Z')||(LA3_0>='a' && LA3_0<='z')||(LA3_0>='\u4E00' && LA3_0<='\u9FFF')) ) {
                    alt3=1;
                }


                switch (alt3) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:
                    {
                    if ( input.LA(1)==' '||input.LA(1)=='.'||(input.LA(1)>='A' && input.LA(1)<='Z')||(input.LA(1)>='a' && input.LA(1)<='z')||(input.LA(1)>='\u4E00' && input.LA(1)<='\u9FFF') ) {
                        input.consume();

                    }
                    else {
                        MismatchedSetException mse = new MismatchedSetException(null,input);
                        recover(mse);
                        throw mse;}


                    }
                    break;

                default :
                    if ( cnt3 >= 1 ) break loop3;
                        EarlyExitException eee =
                            new EarlyExitException(3, input);
                        throw eee;
                }
                cnt3++;
            } while (true);


            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "ID"

    // $ANTLR start "STRING"
    public final void mSTRING() throws RecognitionException {
        try {
            int _type = STRING;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:9: ( '\"' ( CHAR | INT | CHINESE | '*' | '-' | '_' | '%' | ',' | ':' | '!' )* '\"' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:11: '\"' ( CHAR | INT | CHINESE | '*' | '-' | '_' | '%' | ',' | ':' | '!' )* '\"'
            {
            match('\"'); 
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:15: ( CHAR | INT | CHINESE | '*' | '-' | '_' | '%' | ',' | ':' | '!' )*
            loop4:
            do {
                int alt4=11;
                int LA4_0 = input.LA(1);

                if ( (LA4_0==' '||LA4_0=='.'||(LA4_0>='A' && LA4_0<='Z')||(LA4_0>='a' && LA4_0<='z')) ) {
                    alt4=1;
                }
                else if ( ((LA4_0>='0' && LA4_0<='9')) ) {
                    alt4=2;
                }
                else if ( ((LA4_0>='\u4E00' && LA4_0<='\u9FFF')) ) {
                    alt4=3;
                }
                else if ( (LA4_0=='*') ) {
                    alt4=4;
                }
                else if ( (LA4_0=='-') ) {
                    alt4=5;
                }
                else if ( (LA4_0=='_') ) {
                    alt4=6;
                }
                else if ( (LA4_0=='%') ) {
                    alt4=7;
                }
                else if ( (LA4_0==',') ) {
                    alt4=8;
                }
                else if ( (LA4_0==':') ) {
                    alt4=9;
                }
                else if ( (LA4_0=='!') ) {
                    alt4=10;
                }


                switch (alt4) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:16: CHAR
                    {
                    mCHAR(); 

                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:22: INT
                    {
                    mINT(); 

                    }
                    break;
                case 3 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:28: CHINESE
                    {
                    mCHINESE(); 

                    }
                    break;
                case 4 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:38: '*'
                    {
                    match('*'); 

                    }
                    break;
                case 5 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:44: '-'
                    {
                    match('-'); 

                    }
                    break;
                case 6 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:50: '_'
                    {
                    match('_'); 

                    }
                    break;
                case 7 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:56: '%'
                    {
                    match('%'); 

                    }
                    break;
                case 8 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:62: ','
                    {
                    match(','); 

                    }
                    break;
                case 9 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:68: ':'
                    {
                    match(':'); 

                    }
                    break;
                case 10 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:207:74: '!'
                    {
                    match('!'); 

                    }
                    break;

                default :
                    break loop4;
                }
            } while (true);

            match('\"'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "STRING"

    // $ANTLR start "CHAR"
    public final void mCHAR() throws RecognitionException {
        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:210:6: ( 'a' .. 'z' | 'A' .. 'Z' | '.' | ' ' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:
            {
            if ( input.LA(1)==' '||input.LA(1)=='.'||(input.LA(1)>='A' && input.LA(1)<='Z')||(input.LA(1)>='a' && input.LA(1)<='z') ) {
                input.consume();

            }
            else {
                MismatchedSetException mse = new MismatchedSetException(null,input);
                recover(mse);
                throw mse;}


            }

        }
        finally {
        }
    }
    // $ANTLR end "CHAR"

    // $ANTLR start "CHINESE"
    public final void mCHINESE() throws RecognitionException {
        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:212:9: ( '\\u4e00' .. '\\u9fff' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:212:11: '\\u4e00' .. '\\u9fff'
            {
            matchRange('\u4E00','\u9FFF'); 

            }

        }
        finally {
        }
    }
    // $ANTLR end "CHINESE"

    // $ANTLR start "INT"
    public final void mINT() throws RecognitionException {
        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:214:5: ( ( '0' .. '9' )+ )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:214:9: ( '0' .. '9' )+
            {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:214:9: ( '0' .. '9' )+
            int cnt5=0;
            loop5:
            do {
                int alt5=2;
                int LA5_0 = input.LA(1);

                if ( ((LA5_0>='0' && LA5_0<='9')) ) {
                    alt5=1;
                }


                switch (alt5) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:214:9: '0' .. '9'
                    {
                    matchRange('0','9'); 

                    }
                    break;

                default :
                    if ( cnt5 >= 1 ) break loop5;
                        EarlyExitException eee =
                            new EarlyExitException(5, input);
                        throw eee;
                }
                cnt5++;
            } while (true);


            }

        }
        finally {
        }
    }
    // $ANTLR end "INT"

    // $ANTLR start "EQUALS"
    public final void mEQUALS() throws RecognitionException {
        try {
            int _type = EQUALS;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:216:9: ( '==' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:216:13: '=='
            {
            match("=="); 


            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "EQUALS"

    // $ANTLR start "NOTEQUALS"
    public final void mNOTEQUALS() throws RecognitionException {
        try {
            int _type = NOTEQUALS;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:218:11: ( '!=' | '<>' )
            int alt6=2;
            int LA6_0 = input.LA(1);

            if ( (LA6_0=='!') ) {
                alt6=1;
            }
            else if ( (LA6_0=='<') ) {
                alt6=2;
            }
            else {
                NoViableAltException nvae =
                    new NoViableAltException("", 6, 0, input);

                throw nvae;
            }
            switch (alt6) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:218:16: '!='
                    {
                    match("!="); 


                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:218:23: '<>'
                    {
                    match("<>"); 


                    }
                    break;

            }
            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "NOTEQUALS"

    // $ANTLR start "LT"
    public final void mLT() throws RecognitionException {
        try {
            int _type = LT;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:220:9: ( '<' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:220:14: '<'
            {
            match('<'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "LT"

    // $ANTLR start "LTEQ"
    public final void mLTEQ() throws RecognitionException {
        try {
            int _type = LTEQ;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:221:9: ( '<=' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:221:14: '<='
            {
            match("<="); 


            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "LTEQ"

    // $ANTLR start "GT"
    public final void mGT() throws RecognitionException {
        try {
            int _type = GT;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:222:9: ( '>' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:222:14: '>'
            {
            match('>'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "GT"

    // $ANTLR start "GTEQ"
    public final void mGTEQ() throws RecognitionException {
        try {
            int _type = GTEQ;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:223:9: ( '>=' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:223:14: '>='
            {
            match(">="); 


            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "GTEQ"

    // $ANTLR start "Float"
    public final void mFloat() throws RecognitionException {
        try {
            int _type = Float;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:5: ( ( '-' )? ( '0' .. '9' )+ '.' ( '0' .. '9' )* | ( '-' )? '.' ( '0' .. '9' )+ | ( '-' )? ( '0' .. '9' )+ )
            int alt14=3;
            alt14 = dfa14.predict(input);
            switch (alt14) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:8: ( '-' )? ( '0' .. '9' )+ '.' ( '0' .. '9' )*
                    {
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:8: ( '-' )?
                    int alt7=2;
                    int LA7_0 = input.LA(1);

                    if ( (LA7_0=='-') ) {
                        alt7=1;
                    }
                    switch (alt7) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:8: '-'
                            {
                            match('-'); 

                            }
                            break;

                    }

                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:13: ( '0' .. '9' )+
                    int cnt8=0;
                    loop8:
                    do {
                        int alt8=2;
                        int LA8_0 = input.LA(1);

                        if ( ((LA8_0>='0' && LA8_0<='9')) ) {
                            alt8=1;
                        }


                        switch (alt8) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:14: '0' .. '9'
                            {
                            matchRange('0','9'); 

                            }
                            break;

                        default :
                            if ( cnt8 >= 1 ) break loop8;
                                EarlyExitException eee =
                                    new EarlyExitException(8, input);
                                throw eee;
                        }
                        cnt8++;
                    } while (true);

                    match('.'); 
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:29: ( '0' .. '9' )*
                    loop9:
                    do {
                        int alt9=2;
                        int LA9_0 = input.LA(1);

                        if ( ((LA9_0>='0' && LA9_0<='9')) ) {
                            alt9=1;
                        }


                        switch (alt9) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:226:30: '0' .. '9'
                            {
                            matchRange('0','9'); 

                            }
                            break;

                        default :
                            break loop9;
                        }
                    } while (true);


                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:227:8: ( '-' )? '.' ( '0' .. '9' )+
                    {
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:227:8: ( '-' )?
                    int alt10=2;
                    int LA10_0 = input.LA(1);

                    if ( (LA10_0=='-') ) {
                        alt10=1;
                    }
                    switch (alt10) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:227:8: '-'
                            {
                            match('-'); 

                            }
                            break;

                    }

                    match('.'); 
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:227:17: ( '0' .. '9' )+
                    int cnt11=0;
                    loop11:
                    do {
                        int alt11=2;
                        int LA11_0 = input.LA(1);

                        if ( ((LA11_0>='0' && LA11_0<='9')) ) {
                            alt11=1;
                        }


                        switch (alt11) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:227:18: '0' .. '9'
                            {
                            matchRange('0','9'); 

                            }
                            break;

                        default :
                            if ( cnt11 >= 1 ) break loop11;
                                EarlyExitException eee =
                                    new EarlyExitException(11, input);
                                throw eee;
                        }
                        cnt11++;
                    } while (true);


                    }
                    break;
                case 3 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:228:8: ( '-' )? ( '0' .. '9' )+
                    {
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:228:8: ( '-' )?
                    int alt12=2;
                    int LA12_0 = input.LA(1);

                    if ( (LA12_0=='-') ) {
                        alt12=1;
                    }
                    switch (alt12) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:228:8: '-'
                            {
                            match('-'); 

                            }
                            break;

                    }

                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:228:13: ( '0' .. '9' )+
                    int cnt13=0;
                    loop13:
                    do {
                        int alt13=2;
                        int LA13_0 = input.LA(1);

                        if ( ((LA13_0>='0' && LA13_0<='9')) ) {
                            alt13=1;
                        }


                        switch (alt13) {
                        case 1 :
                            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:228:14: '0' .. '9'
                            {
                            matchRange('0','9'); 

                            }
                            break;

                        default :
                            if ( cnt13 >= 1 ) break loop13;
                                EarlyExitException eee =
                                    new EarlyExitException(13, input);
                                throw eee;
                        }
                        cnt13++;
                    } while (true);


                    }
                    break;

            }
            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "Float"

    // $ANTLR start "NEWLINE"
    public final void mNEWLINE() throws RecognitionException {
        try {
            int _type = NEWLINE;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:231:8: ( ( '\\r' )? '\\n' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:231:9: ( '\\r' )? '\\n'
            {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:231:9: ( '\\r' )?
            int alt15=2;
            int LA15_0 = input.LA(1);

            if ( (LA15_0=='\r') ) {
                alt15=1;
            }
            switch (alt15) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:231:9: '\\r'
                    {
                    match('\r'); 

                    }
                    break;

            }

            match('\n'); 

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "NEWLINE"

    // $ANTLR start "WS"
    public final void mWS() throws RecognitionException {
        try {
            int _type = WS;
            int _channel = DEFAULT_TOKEN_CHANNEL;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:232:5: ( ( ' ' | '\\t' )+ )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:232:9: ( ' ' | '\\t' )+
            {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:232:9: ( ' ' | '\\t' )+
            int cnt16=0;
            loop16:
            do {
                int alt16=2;
                int LA16_0 = input.LA(1);

                if ( (LA16_0=='\t'||LA16_0==' ') ) {
                    alt16=1;
                }


                switch (alt16) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:
                    {
                    if ( input.LA(1)=='\t'||input.LA(1)==' ' ) {
                        input.consume();

                    }
                    else {
                        MismatchedSetException mse = new MismatchedSetException(null,input);
                        recover(mse);
                        throw mse;}


                    }
                    break;

                default :
                    if ( cnt16 >= 1 ) break loop16;
                        EarlyExitException eee =
                            new EarlyExitException(16, input);
                        throw eee;
                }
                cnt16++;
            } while (true);

            skip();

            }

            state.type = _type;
            state.channel = _channel;
        }
        finally {
        }
    }
    // $ANTLR end "WS"

    public void mTokens() throws RecognitionException {
        // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:8: ( T__20 | T__21 | T__22 | T__23 | T__24 | T__25 | T__26 | METHOD | CELL | ID | STRING | EQUALS | NOTEQUALS | LT | LTEQ | GT | GTEQ | Float | NEWLINE | WS )
        int alt17=20;
        alt17 = dfa17.predict(input);
        switch (alt17) {
            case 1 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:10: T__20
                {
                mT__20(); 

                }
                break;
            case 2 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:16: T__21
                {
                mT__21(); 

                }
                break;
            case 3 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:22: T__22
                {
                mT__22(); 

                }
                break;
            case 4 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:28: T__23
                {
                mT__23(); 

                }
                break;
            case 5 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:34: T__24
                {
                mT__24(); 

                }
                break;
            case 6 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:40: T__25
                {
                mT__25(); 

                }
                break;
            case 7 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:46: T__26
                {
                mT__26(); 

                }
                break;
            case 8 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:52: METHOD
                {
                mMETHOD(); 

                }
                break;
            case 9 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:59: CELL
                {
                mCELL(); 

                }
                break;
            case 10 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:64: ID
                {
                mID(); 

                }
                break;
            case 11 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:67: STRING
                {
                mSTRING(); 

                }
                break;
            case 12 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:74: EQUALS
                {
                mEQUALS(); 

                }
                break;
            case 13 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:81: NOTEQUALS
                {
                mNOTEQUALS(); 

                }
                break;
            case 14 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:91: LT
                {
                mLT(); 

                }
                break;
            case 15 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:94: LTEQ
                {
                mLTEQ(); 

                }
                break;
            case 16 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:99: GT
                {
                mGT(); 

                }
                break;
            case 17 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:102: GTEQ
                {
                mGTEQ(); 

                }
                break;
            case 18 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:107: Float
                {
                mFloat(); 

                }
                break;
            case 19 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:113: NEWLINE
                {
                mNEWLINE(); 

                }
                break;
            case 20 :
                // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:1:121: WS
                {
                mWS(); 

                }
                break;

        }

    }


    protected DFA14 dfa14 = new DFA14(this);
    protected DFA17 dfa17 = new DFA17(this);
    static final String DFA14_eotS =
        "\2\uffff\1\4\3\uffff";
    static final String DFA14_eofS =
        "\6\uffff";
    static final String DFA14_minS =
        "\1\55\2\56\3\uffff";
    static final String DFA14_maxS =
        "\3\71\3\uffff";
    static final String DFA14_acceptS =
        "\3\uffff\1\2\1\3\1\1";
    static final String DFA14_specialS =
        "\6\uffff}>";
    static final String[] DFA14_transitionS = {
            "\1\1\1\3\1\uffff\12\2",
            "\1\3\1\uffff\12\2",
            "\1\5\1\uffff\12\2",
            "",
            "",
            ""
    };

    static final short[] DFA14_eot = DFA.unpackEncodedString(DFA14_eotS);
    static final short[] DFA14_eof = DFA.unpackEncodedString(DFA14_eofS);
    static final char[] DFA14_min = DFA.unpackEncodedStringToUnsignedChars(DFA14_minS);
    static final char[] DFA14_max = DFA.unpackEncodedStringToUnsignedChars(DFA14_maxS);
    static final short[] DFA14_accept = DFA.unpackEncodedString(DFA14_acceptS);
    static final short[] DFA14_special = DFA.unpackEncodedString(DFA14_specialS);
    static final short[][] DFA14_transition;

    static {
        int numStates = DFA14_transitionS.length;
        DFA14_transition = new short[numStates][];
        for (int i=0; i<numStates; i++) {
            DFA14_transition[i] = DFA.unpackEncodedString(DFA14_transitionS[i]);
        }
    }

    class DFA14 extends DFA {

        public DFA14(BaseRecognizer recognizer) {
            this.recognizer = recognizer;
            this.decisionNumber = 14;
            this.eot = DFA14_eot;
            this.eof = DFA14_eof;
            this.min = DFA14_min;
            this.max = DFA14_max;
            this.accept = DFA14_accept;
            this.special = DFA14_special;
            this.transition = DFA14_transition;
        }
        public String getDescription() {
            return "225:1: Float : ( ( '-' )? ( '0' .. '9' )+ '.' ( '0' .. '9' )* | ( '-' )? '.' ( '0' .. '9' )+ | ( '-' )? ( '0' .. '9' )+ );";
        }
    }
    static final String DFA17_eotS =
        "\2\uffff\1\24\5\uffff\1\11\1\uffff\1\11\4\uffff\1\30\1\32\4\uffff"+
        "\1\11\5\uffff";
    static final String DFA17_eofS =
        "\33\uffff";
    static final String DFA17_minS =
        "\1\11\1\uffff\1\56\5\uffff\1\60\1\uffff\1\60\4\uffff\2\75\4\uffff"+
        "\1\60\5\uffff";
    static final String DFA17_maxS =
        "\1\u9fff\1\uffff\1\71\5\uffff\1\132\1\uffff\1\71\4\uffff\1\76\1"+
        "\75\4\uffff\1\71\5\uffff";
    static final String DFA17_acceptS =
        "\1\uffff\1\1\1\uffff\1\3\1\4\1\5\1\6\1\7\1\uffff\1\10\1\uffff\1"+
        "\12\1\13\1\14\1\15\2\uffff\1\22\1\23\1\24\1\2\1\uffff\1\11\1\17"+
        "\1\16\1\21\1\20";
    static final String DFA17_specialS =
        "\33\uffff}>";
    static final String[] DFA17_transitionS = {
            "\1\23\1\22\2\uffff\1\22\22\uffff\1\23\1\16\1\14\1\uffff\1\13"+
            "\3\uffff\1\5\1\6\1\3\1\1\1\7\1\2\1\21\1\4\12\21\2\uffff\1\17"+
            "\1\15\1\20\2\uffff\11\10\21\12\4\uffff\1\11\1\uffff\32\11\u4d85"+
            "\uffff\u5200\11",
            "",
            "\1\21\1\uffff\12\21",
            "",
            "",
            "",
            "",
            "",
            "\12\26\7\uffff\32\25",
            "",
            "\12\26",
            "",
            "",
            "",
            "",
            "\1\27\1\16",
            "\1\31",
            "",
            "",
            "",
            "",
            "\12\26",
            "",
            "",
            "",
            "",
            ""
    };

    static final short[] DFA17_eot = DFA.unpackEncodedString(DFA17_eotS);
    static final short[] DFA17_eof = DFA.unpackEncodedString(DFA17_eofS);
    static final char[] DFA17_min = DFA.unpackEncodedStringToUnsignedChars(DFA17_minS);
    static final char[] DFA17_max = DFA.unpackEncodedStringToUnsignedChars(DFA17_maxS);
    static final short[] DFA17_accept = DFA.unpackEncodedString(DFA17_acceptS);
    static final short[] DFA17_special = DFA.unpackEncodedString(DFA17_specialS);
    static final short[][] DFA17_transition;

    static {
        int numStates = DFA17_transitionS.length;
        DFA17_transition = new short[numStates][];
        for (int i=0; i<numStates; i++) {
            DFA17_transition[i] = DFA.unpackEncodedString(DFA17_transitionS[i]);
        }
    }

    class DFA17 extends DFA {

        public DFA17(BaseRecognizer recognizer) {
            this.recognizer = recognizer;
            this.decisionNumber = 17;
            this.eot = DFA17_eot;
            this.eof = DFA17_eof;
            this.min = DFA17_min;
            this.max = DFA17_max;
            this.accept = DFA17_accept;
            this.special = DFA17_special;
            this.transition = DFA17_transition;
        }
        public String getDescription() {
            return "1:1: Tokens : ( T__20 | T__21 | T__22 | T__23 | T__24 | T__25 | T__26 | METHOD | CELL | ID | STRING | EQUALS | NOTEQUALS | LT | LTEQ | GT | GTEQ | Float | NEWLINE | WS );";
        }
    }
 

}