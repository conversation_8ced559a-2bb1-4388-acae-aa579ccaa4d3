package com.icsc.ac.bp;

import java.util.Map;

import com.icsc.ac.api.acjcFactory;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.bp.zafcComBp;

public class acjcBatStep extends zafcComBp {

	public acjcBatStep(dsjccom dsCom) {
		super(dsCom);
	}

	public void runStep(String className, Map para) throws Exception {
		try {
			this.createTransaction();

			new acjcFactory(dsCom, conn).newZafiExecMehod(className, para);

			this.closeTransaction();
		} catch (Exception e) {
			this.throwExp(e);
		}
	}
}
