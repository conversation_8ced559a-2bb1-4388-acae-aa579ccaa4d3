package com.icsc.ad.bat;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;



import com.icsc.ad.dao.adjcmlDAO;
import com.icsc.ad.dao.adjcmlVO;
import com.icsc.ip.dao.ipjcTDOKTPDAO;
import com.icsc.ip.dao.ipjcTDOKTPVO;
import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.de.dejc330;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.dpms.dx.dxjcConverter;
import com.icsc.facc.zaf.zafiExecMehod;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.dei.zafcComdei;
import com.icsc.facc.zaf.util.zafcMapUtil;
import com.icsc.facc.zaf.util.zafctool;

public class adjcPut69ToOktp implements zafiExecMehod {
	

	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2023/04/13 07:02:07 $";

	private String compId, dataDate;

	private dsjccom dsCom;

	private Connection con;

	private dejc318 de318;
	
	private zafcCommonDAO comDAO;

	
	private ipjcTDOKTPDAO tdoktpDAO;

	public adjcPut69ToOktp(dsjccom dc, Connection cn) {
		dsCom = dc;
		con = cn;
	}

	private void init() {
		de318 = new dejc318(dsCom, CLASS_VERSION);
		tdoktpDAO = new ipjcTDOKTPDAO(dsCom, con);
	}

	public Object run(Map para) throws  Exception {
		compId = zafctool.trim(para.get("compId".toUpperCase()));
		dataDate = zafctool.trim(para.get("dataDate".toUpperCase()));
		init();
		
		de318.logs("run", "para==" + para);
 
		delete69Data();
		create69Data();
		
		return "";
		
	}
	
	private void create69Data() throws Exception {
	
		String sql = "SELECT * FROM DB.TBiptdok where compid = '" + compId + "' and  acctItem = '69' and acctdate like '" + dataDate + "%'";
		comDAO = new zafcCommonDAO(this.dsCom, this.con);
		Map[] data = comDAO.queryVOs(sql);
		Map result = new HashMap();
		//Map tmp;
		ipjcTDOKTPVO tdoktpVO = null;
		for (int i = 0; i < data.length; i++) {
			Map m = data[i];
						
			String key = (String) m.get("COMPID")+(String) m.get("SLIPNO")+(String) m.get("SLIPNOITEM");
			
			if (result.containsKey(key)) {
				tdoktpVO = (ipjcTDOKTPVO) result.get(key);
			} else {
				tdoktpVO = new ipjcTDOKTPVO();
				tdoktpVO.setCompId(compId);
				tdoktpVO.setSlipNo((String) m.get("SLIPNO"));
				tdoktpVO.setSlipNoItem((String) m.get("SLIPNOITEM"));
				tdoktpVO.setAdjType((String) m.get("ADJTYPE"));
				tdoktpVO.setAdjItem((String) m.get("ADJITEM"));
				tdoktpVO.setAcctDate((String) m.get("ACCTDATE"));
				tdoktpVO.setSysId((String) m.get("SYSID"));
				tdoktpVO.setAppId((String) m.get("APPID"));
				tdoktpVO.setGmCode((String) m.get("GMCODE"));
				tdoktpVO.setExecJobFunc((String) m.get("EXECJOBFUNC"));
				tdoktpVO.setDataAttriIdx((String) m.get("DATAATTRIIDX"));
				tdoktpVO.setActivityId((String) m.get("ACTIVITYID"));
				tdoktpVO.setAcctItem((String) m.get("ACCTITEM"));
				tdoktpVO.setProdCode((String) m.get("PRODCODE"));
				tdoktpVO.setDpd((String) m.get("DPD"));
				tdoktpVO.setCrntMill((String) m.get("CRNTMILL"));
				tdoktpVO.setLocNo((String) m.get("LOCNO"));
				tdoktpVO.setBatchNo((String) m.get("BATCHNO"));
				tdoktpVO.setInvId((String) m.get("INVID"));
				tdoktpVO.setInvWgt(zafctool.parseBD((String) m.get("INVWGT")));
				tdoktpVO.setInvUnit((String) m.get("INVUNIT"));
				tdoktpVO.setInvQty(zafctool.parseBD((String) m.get("INVQTY")));
				tdoktpVO.setCostCenter((String) m.get("COSTCENTER"));
				tdoktpVO.setProcess((String) m.get("PROCESS"));
				tdoktpVO.setInnerUseCC((String) m.get("INNERUSECC"));
				tdoktpVO.setProdOrder((String) m.get("PRODORDER"));
				tdoktpVO.setTeam((String) m.get("TEAM"));
				tdoktpVO.setConnectId((String) m.get("CONNECTID"));
				tdoktpVO.setPurchaseOrder((String) m.get("PURCHASEORDER"));
				tdoktpVO.setPoItem((String) m.get("POITEM"));
				tdoktpVO.setPoSettleQty((String) m.get("POSETTLEQTY"));
				tdoktpVO.setSalesOrder((String) m.get("SALESORDER"));
				tdoktpVO.setSoItem((String) m.get("SOITEM"));
				tdoktpVO.setStdAmt(zafctool.parseBD((String) m.get("STDAMT")));
				tdoktpVO.setVarAmt(zafctool.parseBD((String) m.get("VARAMT")));
				tdoktpVO.setValAmtA(zafctool.parseBD((String) m.get("VARAMT")));
				tdoktpVO.setValAmtB(zafctool.parseBD((String) m.get("VARAMT")));
				tdoktpVO.setValAmtC(zafctool.parseBD((String) m.get("VARAMT")));
				tdoktpVO.setValAmtD(zafctool.parseBD((String) m.get("VARAMT")));
				tdoktpVO.setValAmtE(zafctool.parseBD((String) m.get("VARAMT")));
				tdoktpVO.setApToIPDate((String) m.get("APTOIPDATE"));
				tdoktpVO.setApToIPTime((String) m.get("APTOIPTIME"));
				tdoktpVO.setIpProcDate((String) m.get("IPPROCDATE"));
				tdoktpVO.setIpProcTime((String) m.get("IPPROCTIME"));
				tdoktpVO.setIpDocument((String) m.get("IPDOCUMENT"));
				tdoktpVO.setRefDocument((String) m.get("REFDOCUMENT"));
				tdoktpVO.setStatus((String) m.get("STATUS"));
				tdoktpVO.setItemText((String) m.get("ITEMTEXT"));
				tdoktpVO.setCreateId((String) m.get("CREATEID"));
				tdoktpVO.setLastUpdtId((String) m.get("LASTUPDTID"));
				tdoktpVO.setLastUpdtDate((String) m.get("LASTUPDTDATE"));
				result.put(key, tdoktpVO);
			}
		}
		tdoktpDAO.updateCreateList(zafcMapUtil.fetchMap(result, zafcMapUtil.TYPE_VALUE));
	}

	public String getMsg() {
		return "";
	}
	
	private void delete69Data() throws Exception{
		zafcCommonDAO comDAO = new zafcCommonDAO(dsCom);
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("delete from db." + "tbiptdoktp");
		sqlStr.append(" where compId='" + compId + "'");
		sqlStr.append(" and acctdate like '" + dataDate + "%'");
		
		comDAO.updateBySQL(sqlStr.toString());
		
	}
}
