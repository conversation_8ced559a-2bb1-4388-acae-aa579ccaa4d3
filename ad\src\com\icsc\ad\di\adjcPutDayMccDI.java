package com.icsc.ad.di;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

import com.icsc.ad.bat.adjcPutDayMcc;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.di.diji234;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.util.zafcFile;
import com.icsc.facc.zaf.util.zafctool;

public class adjcPutDayMccDI implements diji234 {
	public final static String CLASS_VERSION = "adjcPutDayMccDI $Revision: 1.1 $ $Date: 2021/02/20 06:35:49 $";

	private dejc318 de318;

	public void run(dsjccom dsCom) {
		try {
			de318 = new dejc318(dsCom, CLASS_VERSION);
			zafcCommonDAO zafcDAO = new zafcCommonDAO(dsCom);
			String sql = "select distinct pcompId as COMPID from db.tbaacp";
			Map[] maps = zafcDAO.queryVOs(sql);
			Map map = new HashMap();
			String date = zafctool.computeDate(zafctool.todayW(), -1);
			map.put("STARTDATE", date.substring(0, 6) + "01");
			map.put("ENDDATE", date);
			for (int i = 0; i < maps.length; i++) {
				map.put("COMPID", maps[i].get("COMPID"));
				runByCompId(dsCom, map);
			}
		} catch (Exception e) {
			de318.logs("run", "error:", e);
		}
	}

	private void runByCompId(dsjccom dsCom, Map map) throws Exception {
		dejc301 de301 = new dejc301();
		try {
			Connection con = de301.getConnection(dsCom, CLASS_VERSION);
			de301.setAutoCommit(false);
			adjcPutDayMcc adPutDayMcc = new adjcPutDayMcc(dsCom, con);
			adPutDayMcc.run(map);
			de301.commit();
		} catch (Exception e) {
			de318.logs("runByCompId", "error:", e);
			de301.rollback();
			zafcFile.makeFile(this.getClass().getName() + "_" + map.get("COMPID"), e);
		} finally {
			de301.close();
		}

	}
}
