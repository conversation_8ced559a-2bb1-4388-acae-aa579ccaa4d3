eclipse.preferences.version=1
encoding//config/bg2/il/ilStructs.xml=UTF-8
encoding//config/bg2/mf/mfStructs.xml=UTF-8
encoding//config/bg2/mr/mrStructs.xml=UTF-8
encoding//config/bg2/sh/shStructs.xml=UTF-8
encoding//config/bg2/so/soStructs.xml=UTF-8
encoding//dez/src=GBK
encoding//dez/utf8=UTF-8
encoding//html/html/dr/drjtutl.jss=GBK
encoding//il/config/bg2/il/ilStructs.xml=UTF-8
encoding//mf/config/bg2/mf/mfStructs.xml=UTF-8
encoding//mf/src/com/icsc/mf/util/xt/info/WorkflowRequestInfoNew.java=UTF-8
encoding//mf/src/com/icsc/mf/util/xt/webservice/workflow/WorkflowService.java=UTF-8
encoding//mf/src/com/icsc/mf/util/xt/webservice/workflow/WorkflowServiceHttpBindingStub.java=UTF-8
encoding//mf/src/com/icsc/mf/util/xt/webservice/workflow/WorkflowServiceLocator.java=UTF-8
encoding//mf/src/com/icsc/mf/util/xt/webservice/workflow/WorkflowServicePortType.java=UTF-8
encoding//mp/jsp/mpjjm302AbEdit.jsp=GB18030
encoding//mr/config/bg2/mr/mrStructs.xml=UTF-8
encoding//sh/config/bg2/sh/shStructs.xml=UTF-8
encoding//so/config/bg2/so/soStructs.xml=UTF-8
encoding//sr/config/bg2/sr/srStructs.xml=UTF-8
encoding//src/IWorkflowService.java=UTF-8
encoding//wss/jsp/wssjInit_BG_big5.jsp=MS950
encoding//xx/gul=GBK
encoding//xx/src=GBK
