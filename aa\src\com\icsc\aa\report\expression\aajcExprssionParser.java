// $ANTLR 3.1.2 D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g 2012-06-15 17:11:56

package com.icsc.aa.report.expression;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Vector;
import java.util.Stack;
import java.math.BigDecimal;


import org.antlr.runtime.*;
import java.util.Stack;
import java.util.List;
import java.util.ArrayList;

public class aajcExprssionParser extends Parser {
    public static final String[] tokenNames = new String[] {
        "<invalid>", "<EOR>", "<DOWN>", "<UP>", "EQUALS", "LT", "LTEQ", "GT", "GTEQ", "Float", "STRING", "INT", "CELL", "ID", "METHOD", "CHINESE", "CHAR", "NOTEQUALS", "NEWLINE", "WS", "'+'", "'-'", "'*'", "'/'", "'('", "')'", "','"
    };
    public static final int LT=5;
    public static final int T__26=26;
    public static final int T__25=25;
    public static final int T__24=24;
    public static final int T__23=23;
    public static final int T__22=22;
    public static final int T__21=21;
    public static final int T__20=20;
    public static final int LTEQ=6;
    public static final int CHAR=16;
    public static final int NOTEQUALS=17;
    public static final int CELL=12;
    public static final int EQUALS=4;
    public static final int INT=11;
    public static final int GTEQ=8;
    public static final int ID=13;
    public static final int EOF=-1;
    public static final int Float=9;
    public static final int WS=19;
    public static final int NEWLINE=18;
    public static final int GT=7;
    public static final int CHINESE=15;
    public static final int METHOD=14;
    public static final int STRING=10;

    // delegates
    // delegators


        public aajcExprssionParser(TokenStream input) {
            this(input, new RecognizerSharedState());
        }
        public aajcExprssionParser(TokenStream input, RecognizerSharedState state) {
            super(input, state);
             
        }
        

    public String[] getTokenNames() { return aajcExprssionParser.tokenNames; }
    public String getGrammarFileName() { return "D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g"; }


    /** Map variable name to Integer object holding value */
            Map memory = new HashMap();
        //memory.put("AMT",new BigDecimal("100"));
        public Object invokeMethod(List parameters, String methodName) throws RecognitionException {
            return new BigDecimal("0");
        }

        public Object cellValue(String cellName) throws RecognitionException {
            return new BigDecimal("0");
        }
        
            public void setContextValue(String key,Object v){
               memory.put(key,v);
            }

            public void setContextValues(Map map ){
                memory = map;
            }



    // $ANTLR start "exp"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:35:1: exp returns [Object value] : e= expr ;
    public final Object exp() throws RecognitionException {
        Object value = null;

        Object e = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:36:6: (e= expr )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:36:10: e= expr
            {
            pushFollow(FOLLOW_expr_in_exp35);
            e=expr();

            state._fsp--;

            value =e;System.out.println(value);

            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "exp"


    // $ANTLR start "expr"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:40:1: expr returns [Object value] : ea= relationalExpr ( EQUALS e= relationalExpr )* ;
    public final Object expr() throws RecognitionException {
        Object value = null;

        Object ea = null;

        Object e = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:41:5: (ea= relationalExpr ( EQUALS e= relationalExpr )* )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:41:7: ea= relationalExpr ( EQUALS e= relationalExpr )*
            {
            pushFollow(FOLLOW_relationalExpr_in_expr62);
            ea=relationalExpr();

            state._fsp--;

            value =ea;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:42:6: ( EQUALS e= relationalExpr )*
            loop1:
            do {
                int alt1=2;
                int LA1_0 = input.LA(1);

                if ( (LA1_0==EQUALS) ) {
                    alt1=1;
                }


                switch (alt1) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:43:6: EQUALS e= relationalExpr
                    {
                    match(input,EQUALS,FOLLOW_EQUALS_in_expr78); 
                    pushFollow(FOLLOW_relationalExpr_in_expr82);
                    e=relationalExpr();

                    state._fsp--;


                             if (ea instanceof BigDecimal && e instanceof BigDecimal) {
                                  value = ((BigDecimal)ea).compareTo(((BigDecimal)e))==0 ? new Boolean(true) : new Boolean(false);
                             }else if(ea instanceof Boolean && e instanceof Boolean){
                              value = new Boolean(((Boolean)ea).booleanValue()==((Boolean)e).booleanValue());
                             }else{
                                  value = ((String)ea).compareTo(((String)e))==0 ? new Boolean(true) : new Boolean(false);
                             }
                         

                    }
                    break;

                default :
                    break loop1;
                }
            } while (true);


            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "expr"


    // $ANTLR start "relationalExpr"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:58:1: relationalExpr returns [Object value] : ea= additiveExpr ( LT e= additiveExpr | LTEQ e= additiveExpr | GT e= additiveExpr | GTEQ e= additiveExpr )? ;
    public final Object relationalExpr() throws RecognitionException {
        Object value = null;

        Object ea = null;

        Object e = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:59:5: (ea= additiveExpr ( LT e= additiveExpr | LTEQ e= additiveExpr | GT e= additiveExpr | GTEQ e= additiveExpr )? )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:59:7: ea= additiveExpr ( LT e= additiveExpr | LTEQ e= additiveExpr | GT e= additiveExpr | GTEQ e= additiveExpr )?
            {
            pushFollow(FOLLOW_additiveExpr_in_relationalExpr130);
            ea=additiveExpr();

            state._fsp--;

            value =ea;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:60:7: ( LT e= additiveExpr | LTEQ e= additiveExpr | GT e= additiveExpr | GTEQ e= additiveExpr )?
            int alt2=5;
            switch ( input.LA(1) ) {
                case LT:
                    {
                    alt2=1;
                    }
                    break;
                case LTEQ:
                    {
                    alt2=2;
                    }
                    break;
                case GT:
                    {
                    alt2=3;
                    }
                    break;
                case GTEQ:
                    {
                    alt2=4;
                    }
                    break;
            }

            switch (alt2) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:60:9: LT e= additiveExpr
                    {
                    match(input,LT,FOLLOW_LT_in_relationalExpr142); 
                    pushFollow(FOLLOW_additiveExpr_in_relationalExpr146);
                    e=additiveExpr();

                    state._fsp--;


                                 if (ea instanceof BigDecimal && e instanceof BigDecimal) {
                                    value = ((BigDecimal)ea).compareTo(((BigDecimal)e))<0 ? new Boolean(true) : new Boolean(false);
                                 }else{
                            value = (ea.toString()).compareTo(e.toString())<0 ? new Boolean(true) : new Boolean(false);
                                 }
                            

                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:68:10: LTEQ e= additiveExpr
                    {
                    match(input,LTEQ,FOLLOW_LTEQ_in_relationalExpr167); 
                    pushFollow(FOLLOW_additiveExpr_in_relationalExpr171);
                    e=additiveExpr();

                    state._fsp--;


                                 if (ea instanceof BigDecimal && e instanceof BigDecimal) {
                                    value = ((BigDecimal)ea).compareTo(((BigDecimal)e))<=0 ? new Boolean(true) : new Boolean(false);
                                 }else{
                            value = (ea.toString()).compareTo(e.toString())<=0 ? new Boolean(true) : new Boolean(false);
                                 }
                            

                    }
                    break;
                case 3 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:76:10: GT e= additiveExpr
                    {
                    match(input,GT,FOLLOW_GT_in_relationalExpr192); 
                    pushFollow(FOLLOW_additiveExpr_in_relationalExpr196);
                    e=additiveExpr();

                    state._fsp--;


                                 if (ea instanceof BigDecimal && e instanceof BigDecimal) {
                                    value = ((BigDecimal)ea).compareTo(((BigDecimal)e))>0 ? new Boolean(true) : new Boolean(false);
                                 }else{
                            value = (ea.toString()).compareTo(e.toString())>0 ? new Boolean(true) : new Boolean(false);
                                 }
                            

                    }
                    break;
                case 4 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:84:10: GTEQ e= additiveExpr
                    {
                    match(input,GTEQ,FOLLOW_GTEQ_in_relationalExpr217); 
                    pushFollow(FOLLOW_additiveExpr_in_relationalExpr221);
                    e=additiveExpr();

                    state._fsp--;


                                 if (ea instanceof BigDecimal && e instanceof BigDecimal) {
                                    value = ((BigDecimal)ea).compareTo(((BigDecimal)e))>=0 ? new Boolean(true) : new Boolean(false);
                                 }else{
                            value = (ea.toString()).compareTo(e.toString())>=0 ? new Boolean(true) : new Boolean(false);
                                 }
                            

                    }
                    break;

            }


            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "relationalExpr"


    // $ANTLR start "additiveExpr"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:97:1: additiveExpr returns [Object value] : e= multExpr ( '+' e= multExpr | '-' e= multExpr | e= multExpr )* ;
    public final Object additiveExpr() throws RecognitionException {
        Object value = null;

        Object e = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:98:5: (e= multExpr ( '+' e= multExpr | '-' e= multExpr | e= multExpr )* )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:98:9: e= multExpr ( '+' e= multExpr | '-' e= multExpr | e= multExpr )*
            {
            pushFollow(FOLLOW_multExpr_in_additiveExpr278);
            e=multExpr();

            state._fsp--;

            value = e;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:99:9: ( '+' e= multExpr | '-' e= multExpr | e= multExpr )*
            loop3:
            do {
                int alt3=4;
                switch ( input.LA(1) ) {
                case 20:
                    {
                    alt3=1;
                    }
                    break;
                case 21:
                    {
                    alt3=2;
                    }
                    break;
                case Float:
                case STRING:
                case INT:
                case CELL:
                case ID:
                case METHOD:
                case 24:
                    {
                    alt3=3;
                    }
                    break;

                }

                switch (alt3) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:99:12: '+' e= multExpr
                    {
                    match(input,20,FOLLOW_20_in_additiveExpr293); 
                    pushFollow(FOLLOW_multExpr_in_additiveExpr297);
                    e=multExpr();

                    state._fsp--;

                    if (value instanceof String) {
                                    value = (String)value + e.toString();
                                 }else{
                                    if(e instanceof String ){
                                      value = value.toString() + "+" + (String)e;
                                    }else{
                                      value = ((BigDecimal)value).add((BigDecimal)e);
                                    }
                                 }
                               

                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:110:11: '-' e= multExpr
                    {
                    match(input,21,FOLLOW_21_in_additiveExpr323); 
                    pushFollow(FOLLOW_multExpr_in_additiveExpr327);
                    e=multExpr();

                    state._fsp--;

                    if (value instanceof String) {
                                    value = (String)value + "-" + e.toString();
                                 }else{
                                      if(e instanceof String ){
                                        value =value.toString() + "-" +  (String)e;
                                      }else{
                                        value = ((BigDecimal)value).subtract((BigDecimal)e);
                                      }
                                 }
                               

                    }
                    break;
                case 3 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:121:13: e= multExpr
                    {
                    pushFollow(FOLLOW_multExpr_in_additiveExpr357);
                    e=multExpr();

                    state._fsp--;

                    if (value instanceof String) {
                                    value = (String)value + e.toString();
                                 }else{
                                    if(e instanceof String ){
                                      value = value.toString() + "+" + (String)e;
                                    }else{
                                      value = ((BigDecimal)value).add((BigDecimal)e);
                                    }
                                 }
                               

                    }
                    break;

                default :
                    break loop3;
                }
            } while (true);


            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "additiveExpr"


    // $ANTLR start "multExpr"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:135:1: multExpr returns [Object value] : e= atom ( '*' e= atom | '/' e= atom )* ;
    public final Object multExpr() throws RecognitionException {
        Object value = null;

        Object e = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:136:5: (e= atom ( '*' e= atom | '/' e= atom )* )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:136:9: e= atom ( '*' e= atom | '/' e= atom )*
            {
            pushFollow(FOLLOW_atom_in_multExpr417);
            e=atom();

            state._fsp--;

            value = e;
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:137:10: ( '*' e= atom | '/' e= atom )*
            loop4:
            do {
                int alt4=3;
                int LA4_0 = input.LA(1);

                if ( (LA4_0==22) ) {
                    alt4=1;
                }
                else if ( (LA4_0==23) ) {
                    alt4=2;
                }


                switch (alt4) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:137:12: '*' e= atom
                    {
                    match(input,22,FOLLOW_22_in_multExpr433); 
                    pushFollow(FOLLOW_atom_in_multExpr437);
                    e=atom();

                    state._fsp--;


                                     if (value instanceof BigDecimal && e instanceof BigDecimal) {
                                        value = ((BigDecimal)value).multiply((BigDecimal)e);
                                     }else{
                                        value = "#VALUE!";
                                     }
                                  

                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:145:13: '/' e= atom
                    {
                    match(input,23,FOLLOW_23_in_multExpr468); 
                    pushFollow(FOLLOW_atom_in_multExpr472);
                    e=atom();

                    state._fsp--;


                                    if (value instanceof BigDecimal && e instanceof BigDecimal) {
                                       if(((BigDecimal)e).compareTo(new BigDecimal("0"))==0){
                                          value = "#DIV/0!";
                                       }else{
                                         value = ((BigDecimal)value).divide((BigDecimal)e,10, 5) ;
                                       }
                                    }else{
                                       value = "#VALUE!";
                                    }
                                 

                    }
                    break;

                default :
                    break loop4;
                }
            } while (true);


            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "multExpr"


    // $ANTLR start "args"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:160:1: args returns [List value ] : '(' (p= parameterList )? ')' ;
    public final List args() throws RecognitionException {
        List value = null;

        List p = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:161:2: ( '(' (p= parameterList )? ')' )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:162:2: '(' (p= parameterList )? ')'
            {
            match(input,24,FOLLOW_24_in_args520); 
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:162:6: (p= parameterList )?
            int alt5=2;
            int LA5_0 = input.LA(1);

            if ( ((LA5_0>=Float && LA5_0<=METHOD)||LA5_0==24) ) {
                alt5=1;
            }
            switch (alt5) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:162:7: p= parameterList
                    {
                    pushFollow(FOLLOW_parameterList_in_args525);
                    p=parameterList();

                    state._fsp--;


                    }
                    break;

            }

            match(input,25,FOLLOW_25_in_args529); 
            value = p;

            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "args"


    // $ANTLR start "parameterList"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:165:1: parameterList returns [List value=new Vector();] : p= parameter ( ',' p= parameter )* ;
    public final List parameterList() throws RecognitionException {
        List value = new Vector();;

        Object p = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:167:5: (p= parameter ( ',' p= parameter )* )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:168:7: p= parameter ( ',' p= parameter )*
            {
            pushFollow(FOLLOW_parameter_in_parameterList562);
            p=parameter();

            state._fsp--;

            value.add(p);
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:169:7: ( ',' p= parameter )*
            loop6:
            do {
                int alt6=2;
                int LA6_0 = input.LA(1);

                if ( (LA6_0==26) ) {
                    alt6=1;
                }


                switch (alt6) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:169:8: ',' p= parameter
                    {
                    match(input,26,FOLLOW_26_in_parameterList574); 
                    pushFollow(FOLLOW_parameter_in_parameterList578);
                    p=parameter();

                    state._fsp--;

                    value.add(p);

                    }
                    break;

                default :
                    break loop6;
                }
            } while (true);


            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "parameterList"


    // $ANTLR start "parameter"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:172:1: parameter returns [Object value] : e= expr ;
    public final Object parameter() throws RecognitionException {
        Object value = null;

        Object e = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:173:5: (e= expr )
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:173:7: e= expr
            {
            pushFollow(FOLLOW_expr_in_parameter605);
            e=expr();

            state._fsp--;

            value = e;

            }

        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "parameter"


    // $ANTLR start "atom"
    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:176:1: atom returns [Object value] : ( Float | STRING | INT | CELL | ID | METHOD para= args | '(' expr ')' );
    public final Object atom() throws RecognitionException {
        Object value = null;

        Token Float1=null;
        Token STRING2=null;
        Token INT3=null;
        Token CELL4=null;
        Token ID5=null;
        Token METHOD6=null;
        List para = null;

        Object expr7 = null;


        try {
            // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:177:5: ( Float | STRING | INT | CELL | ID | METHOD para= args | '(' expr ')' )
            int alt7=7;
            switch ( input.LA(1) ) {
            case Float:
                {
                alt7=1;
                }
                break;
            case STRING:
                {
                alt7=2;
                }
                break;
            case INT:
                {
                alt7=3;
                }
                break;
            case CELL:
                {
                alt7=4;
                }
                break;
            case ID:
                {
                alt7=5;
                }
                break;
            case METHOD:
                {
                alt7=6;
                }
                break;
            case 24:
                {
                alt7=7;
                }
                break;
            default:
                NoViableAltException nvae =
                    new NoViableAltException("", 7, 0, input);

                throw nvae;
            }

            switch (alt7) {
                case 1 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:177:9: Float
                    {
                    Float1=(Token)match(input,Float,FOLLOW_Float_in_atom636); 
                    value = new BigDecimal((Float1!=null?Float1.getText():null));

                    }
                    break;
                case 2 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:178:8: STRING
                    {
                    STRING2=(Token)match(input,STRING,FOLLOW_STRING_in_atom647); 
                    value = (STRING2!=null?STRING2.getText():null).substring(1, (STRING2!=null?STRING2.getText():null).length()-1);

                    }
                    break;
                case 3 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:179:8: INT
                    {
                    INT3=(Token)match(input,INT,FOLLOW_INT_in_atom658); 
                    value = new BigDecimal((INT3!=null?INT3.getText():null));

                    }
                    break;
                case 4 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:180:7: CELL
                    {
                    CELL4=(Token)match(input,CELL,FOLLOW_CELL_in_atom668); 
                    value = cellValue((CELL4!=null?CELL4.getText():null));

                    }
                    break;
                case 5 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:181:9: ID
                    {
                    ID5=(Token)match(input,ID,FOLLOW_ID_in_atom680); 

                            Object v = memory.get((ID5!=null?ID5.getText():null));

                            if ( v!=null ){
                              if(v instanceof Boolean){
                                value = new BigDecimal("1");
                              }else if(v instanceof BigDecimal){
                                value = (BigDecimal)v;
                              }else{
                                value = v.toString();
                              }
                            }else{
                              System.out.println((ID5!=null?ID5.getText():null));
                               //System.err.println("undefined variable "+(ID5!=null?ID5.getText():null));
                            }
                            

                    }
                    break;
                case 6 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:198:7: METHOD para= args
                    {
                    METHOD6=(Token)match(input,METHOD,FOLLOW_METHOD_in_atom698); 
                    pushFollow(FOLLOW_args_in_atom704);
                    para=args();

                    state._fsp--;

                     value = invokeMethod(para,(METHOD6!=null?METHOD6.getText():null));

                    }
                    break;
                case 7 :
                    // D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\aajcExprssion.g:199:7: '(' expr ')'
                    {
                    match(input,24,FOLLOW_24_in_atom716); 
                    pushFollow(FOLLOW_expr_in_atom718);
                    expr7=expr();

                    state._fsp--;

                    match(input,25,FOLLOW_25_in_atom720); 
                    value = expr7;

                    }
                    break;

            }
        }
        catch (RecognitionException re) {
            reportError(re);
            recover(input,re);
        }
        finally {
        }
        return value;
    }
    // $ANTLR end "atom"

    // Delegated rules


 

    public static final BitSet FOLLOW_expr_in_exp35 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_relationalExpr_in_expr62 = new BitSet(new long[]{0x0000000000000012L});
    public static final BitSet FOLLOW_EQUALS_in_expr78 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_relationalExpr_in_expr82 = new BitSet(new long[]{0x0000000000000012L});
    public static final BitSet FOLLOW_additiveExpr_in_relationalExpr130 = new BitSet(new long[]{0x00000000000001E2L});
    public static final BitSet FOLLOW_LT_in_relationalExpr142 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_additiveExpr_in_relationalExpr146 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_LTEQ_in_relationalExpr167 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_additiveExpr_in_relationalExpr171 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_GT_in_relationalExpr192 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_additiveExpr_in_relationalExpr196 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_GTEQ_in_relationalExpr217 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_additiveExpr_in_relationalExpr221 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_multExpr_in_additiveExpr278 = new BitSet(new long[]{0x0000000001307E02L});
    public static final BitSet FOLLOW_20_in_additiveExpr293 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_multExpr_in_additiveExpr297 = new BitSet(new long[]{0x0000000001307E02L});
    public static final BitSet FOLLOW_21_in_additiveExpr323 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_multExpr_in_additiveExpr327 = new BitSet(new long[]{0x0000000001307E02L});
    public static final BitSet FOLLOW_multExpr_in_additiveExpr357 = new BitSet(new long[]{0x0000000001307E02L});
    public static final BitSet FOLLOW_atom_in_multExpr417 = new BitSet(new long[]{0x0000000000C00002L});
    public static final BitSet FOLLOW_22_in_multExpr433 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_atom_in_multExpr437 = new BitSet(new long[]{0x0000000000C00002L});
    public static final BitSet FOLLOW_23_in_multExpr468 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_atom_in_multExpr472 = new BitSet(new long[]{0x0000000000C00002L});
    public static final BitSet FOLLOW_24_in_args520 = new BitSet(new long[]{0x0000000003007E00L});
    public static final BitSet FOLLOW_parameterList_in_args525 = new BitSet(new long[]{0x0000000002000000L});
    public static final BitSet FOLLOW_25_in_args529 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_parameter_in_parameterList562 = new BitSet(new long[]{0x0000000004000002L});
    public static final BitSet FOLLOW_26_in_parameterList574 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_parameter_in_parameterList578 = new BitSet(new long[]{0x0000000004000002L});
    public static final BitSet FOLLOW_expr_in_parameter605 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_Float_in_atom636 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_STRING_in_atom647 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_INT_in_atom658 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_CELL_in_atom668 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_ID_in_atom680 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_METHOD_in_atom698 = new BitSet(new long[]{0x0000000001000000L});
    public static final BitSet FOLLOW_args_in_atom704 = new BitSet(new long[]{0x0000000000000002L});
    public static final BitSet FOLLOW_24_in_atom716 = new BitSet(new long[]{0x0000000001007E00L});
    public static final BitSet FOLLOW_expr_in_atom718 = new BitSet(new long[]{0x0000000002000000L});
    public static final BitSet FOLLOW_25_in_atom720 = new BitSet(new long[]{0x0000000000000002L});

}