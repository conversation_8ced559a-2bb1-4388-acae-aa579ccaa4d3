rem DAOTool Ver 5.1125
rem db.tbact1 sql
rem 098/08/10
rem (INPUT FILE VERSION:2.0)

rem DROP TABLE db.tbact1 ;
rem RENAME TABLE db.tbact1 TO $TABLE_NAME_OLD;

CREATE TABLE db.tbact1 (
		compId VARCHAR (10) DEFAULT '' NOT NULL,
		closeMonth VARCHAR (6) DEFAULT '' NOT NULL,
		recordSerialNo DECIMAL (30) DEFAULT 0 NOT NULL,
		applTransId VARCHAR (30) DEFAULT '',
		sysId VARCHAR (10) DEFAULT '',
		recordType VARCHAR (4) DEFAULT '',
		voucherDate VARCHAR (8) DEFAULT '',
		voucherNo VARCHAR (20) DEFAULT '',
		costCenter VARCHAR (10) DEFAULT '',
		activityCode VARCHAR (30) DEFAULT '',
		wce VARCHAR (10) DEFAULT '',
		actualQunantities DECIMAL (17,4)  DEFAULT 0 ,
		unit VARCHAR (10) DEFAULT '',
		standardAmount DECIMAL (17,2)  DEFAULT 0 ,
		actualAmount DECIMAL (17,2)  DEFAULT 0 ,
		sourceId VARCHAR (10) DEFAULT '',
		referenceNo VARCHAR (100) DEFAULT '',
		recordSpec VARCHAR (200) DEFAULT '',
		updateUserId VARCHAR (10) DEFAULT '',
		updateDate VARCHAR (8) DEFAULT '',
		tradeVar DECIMAL (17,2)  DEFAULT 0 , 
		PRIMARY KEY(compId,closeMonth,recordSerialNo) 
)
GRANT ALL ON db.tbact1 TO JAVAUSER ;
GRANT SELECT,INSERT,UPDATE,DELETE,ALTER,INDEX,REFERENCES ON db.TBACT1 TO JAVAUSER WITH GRANT OPTION;
