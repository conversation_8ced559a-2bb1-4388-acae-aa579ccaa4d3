package com.icsc.ac.gui;

import java.util.*;

import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.dao.zafcrdDAO;
import com.icsc.facc.zaf.dao.zafcrdVO;
import com.icsc.ip.util.ipjcgetSelectValue;

public class acjcPostCode {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:27:13 $";

	public static final String LEAFID = "120109";

	public static String getSelectOptions(dsjccom dsCom, String defV) {
		String sql = "SELECT distinct ITEMA,ITEMA||' '||DATAA FROM db" + ".tbzafrd";
		sql += " where compId='" + dsCom.companyId + "' and leafId='" + LEAFID + "' order by itemA";

		return new ipjcgetSelectValue(dsCom).getOptions(sql, defV).toString();
	}

	public static List getRDList(dsjccom dsCom) {
		try {
			return new zafcrdDAO(dsCom).queryByLeafId(dsCom.companyId, LEAFID);
		} catch (Exception e) {
			return new ArrayList();
		}
	}

	public static String getName(dsjccom dsCom, String defV) {
		try {
			zafcrdVO zafVO = new zafcrdDAO(dsCom).findByIA(dsCom.companyId, LEAFID, defV);
			if (zafVO == null)
				return "";
			return zafVO.getDataA();
		} catch (Exception e) {
			return e.getMessage();
		}
	}

}
