package com.icsc.aa.dei;

import java.util.Comparator;
import java.util.Map;

import com.icsc.aa.util.aajcUtil;

public class aajcAcctComparator implements Comparator {
	
	private String[] comparatorOrder = new String[0];
	
	public int compare(Object obj1, Object obj2) {
		Map map1 = (Map) obj1;
		Map map2 = (Map) obj2;

		int compare = 0;
		
		for (int i = 0; i < comparatorOrder.length; i++) {
			String order1 = aajcUtil.getString(map1.get(comparatorOrder[i]));
			String order2 = aajcUtil.getString(map2.get(comparatorOrder[i]));
			
			if (compare==0) {
				compare = compare(order1, order2);
			} else {
				break;
			}
		}
		
		return compare;
	}
	
	private int compare(String s1, String s2) {
		return s1.compareTo(s2);
	}
	
	public void setComparatorOrder(String[] comparatorOrder) {
		this.comparatorOrder = comparatorOrder;
	}

}
