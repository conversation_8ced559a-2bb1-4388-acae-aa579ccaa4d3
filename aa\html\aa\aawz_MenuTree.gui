﻿/* ---- WinXP 文件夹常见任务样式 ---- */
.WINXP {
	margin: 0px;/* 边距 */
	padding: 12px;
	width: 185px;/* 宽度 */;
	background-color: #6f8cde;/* 背景 */;	
	white-space: nowrap;
}
.WINXP * {
	margin: 0px;
	padding: 0px;
}
.WINXP ul {
	list-style-type: none;
}
.WINXP div {
	padding-left: 20px;/* 缩进 */	
}
.WINXP img {
	vertical-align: middle;
}
.WINXP a {
	margin-left: 4px;
	cursor:pointer;
	font-size: 12px;/* 字号 */;
	line-height: 18px;/* 行距 */
	color: #215dc6;
	text-decoration: none;
}
.WINXP a:hover {
	color: #428eff;
	text-decoration: underline;
}
.WINXP a.ACTIVE {
	color: #ff0000;
}
/* ---- MT_L0 ---- */
.WINXP .MT_L0_BODY {
	padding-left: 0px;
}
.WINXP .MT_L0_ITEM {
	margin-top: 12px;
	padding: 4px 4px 3px 8px;
	height: 18px;
	line-height: 18px;	
	font-size: 13px;
	font-weight: bold;
	background: url('../images/WINXP/_MT_L0.gif') no-repeat;
}
.WINXP .MT_L0_ITEM img {
	float: right;
}
.WINXP .MT_L0_ITEM a {
	color: #215dc6;
}
.WINXP .MT_L0_ITEM a:hover {
	color: #428eff;
}
/* ---- MT_L1 ---- */
.WINXP .MT_L1 {
	padding-left: 0px;
}
.WINXP .MT_L1_BODY {
	padding: 8px 0px 8px 12px;
	background-color: #d6dff7;
	border-right: 1px #FFFFFF solid;
	border-bottom: 1px #FFFFFF solid;
	border-left: 1px #FFFFFF solid;
}
/* ---- WinXP 文件夹常见任务样式 ---- */

/* ---- MSDN 目录树样式 ---- */
.MSDN {
	margin: 0px 0px 0px 13px;/* 边距 */
	padding: 8px;
	width: 166px;/* 宽度 */;
	background-color: #f1f1f1;/* 背景 */;
	border: 1px gray solid;
	white-space: nowrap;
}
.MSDN * {
	margin: 0px;
	padding: 0px;
}
.MSDN ul {
	list-style-type: none;
}
.MSDN li {
	margin-top: 1px !important;/* Firefox修正 */
	margin-top: 2px;/* IE6修正 */
}
.MSDN div {
	padding-left: 20px;/* 缩进 */	
}
.MSDN img {
	vertical-align: middle;
}
.MSDN a {
	margin-left: 2px;
	padding: 1px 2px 0px 2px;
	cursor: pointer;
	color: #000000;
	font-size: 12px;/* 字号 */
	line-height: 12px;/* 行距 */
	text-decoration: none;
	border: 1px #f1f1f1 solid;
}
.MSDN a:hover {
	border: 1px #999999 solid;
	background-color: #cccccc;
}
.MSDN a.ACTIVE {
	color: #ffffff;	
	border: 1px #999999 solid;
	background-color: #cccccc;
}
.MSDN a.NONE {
	color: #888888;
}
/* ---- MT_L0 ---- */
.MSDN .MT_L0 {
	padding-left: 0px;
}
/* ---- MSDN 目录树样式 ---- */

/* ---- EC21 目录树样式 ---- */
.EC21 {
	margin: 0px 0px 0px 13px;/* 边距 */
	padding: 0px;
	width: 184px;/* 宽度 */;
	background-color: #d2e4fc;/* 背景 */;
	white-space: nowrap;
}
.EC21 * {
	margin: 0px;
	padding: 0px;
}
.EC21 ul {
	list-style-type: none;
}
.EC21 div {
	padding-left: 20px;/* 缩进 */	
}
.EC21 img {
	vertical-align: middle;
}
.EC21 a {
	margin-left: 4px;
	cursor: pointer;
	font-size: 12px;/* 字号 */
	line-height: 14px;/* 行距 */
	color: #215dc6;
	text-decoration: none;
}
.EC21 a:hover {
	color: #428eff;
}
.EC21 a.ACTIVE {
	color:#ff0000;
}
.EC21 a.NONE {
	color:#888888;
}
/* ---- MT_L0 ---- */
.EC21 .MT_L0_HEADER {
	height: 3px;
	font-size: 3px;/* IE6修正 */
	background: url('../images/EC21/_MT_L0_HEADER.gif') no-repeat;
}
.EC21 .MT_L0_BODY {
	padding: 0px 5px 0px 5px;
	border-left: 1px #75a3ed solid;
	border-right: 1px #75a3ed solid;
}
.EC21 .MT_L0_FOOTER {
	height: 6px;
	font-size: 6px;/* IE6修正 */
	background: url('../images/EC21/_MT_L0_FOOTER.gif') no-repeat;
}
.EC21 .MT_L0_ITEM {
	padding: 3px 0px 3px 5px;
}
.EC21 .MT_L0_ITEM a {
	font-size:13px;
	font-weight: bold;
	color: #000000;
}
.EC21 .MT_L0_ITEM a:hover {
	color: #428eff;
}
.EC21 .MT_L0_ITEM a.ACTIVE {
	color: #ff0000;
}
/* ---- MT_L1 ---- */
.EC21 .MT_L1 {
	padding-left: 0px;
}
.EC21 .MT_L1_HEADER {
	height: 3px;
	font-size: 3px;/* IE6修正 */
	background-position:right;
	background: url('../images/EC21/_MT_L1_HEADER.gif') no-repeat;
}
.EC21 .MT_L1_BODY {
	padding-top: 4px;
	padding-left: 4px;
	border-left: 1px #75a3ed solid;
	border-right: 1px #75a3ed solid;
	background-color: #FFFFFF;
}
.EC21 .MT_L1_FOOTER {
	height: 4px;
	font-size: 4px;/* IE6修正 */
	background-position:right;
	background: url('../images/EC21/_MT_L1_FOOTER.gif') no-repeat;
}
/* ---- EC21 目录树样式 ---- */

/* ---- CSDN 目录树样式 ---- */
.CSDN {
	margin: 0px 0px 0px 5px;/* 边距 */
	padding: 8px;
	width: 180px;/* 宽度 */;
	background-color: #f1f1f1;/* 背景 */;
	border: 1px gray solid;
	white-space: nowrap;
}
.CSDN * {
	margin: 0px;
	padding: 0px;
}
.CSDN ul {
	list-style-type: none;	
}
.CSDN div {
	padding-left: 20px;
	background-image: url('/erp/images/aa/aawi_UL_LINE.gif');
	background-repeat: repeat-y;
}
.CSDN div.LAST {
	background-image: none;
}
.CSDN img {
	vertical-align: middle;
}
.CSDN a {
	margin-left: 2px;
	padding: 1px 2px 0px 2px;
	cursor: pointer;
	color: #000000;
	font-size: 12px;/* 字号 */
	line-height: 12px;/* 行距 */
	text-decoration: none;
	border: 1px #f1f1f1 solid;
}
.CSDN a:hover {
	border: 1px #999999 solid;
	background-color: #cccccc;
}
.CSDN a.ACTIVE {
	color: #ffffff;	
	border: 1px #999999 solid;
	background-color: #cccccc;
}
.CSDN a.NONE {
	color: #888888;
}
/* ---- MT_L0 ---- */
.CSDN .MT_L0_BODY {
	padding-left: 0px;
	background-image: none;
}
/* ---- CSDN 目录树样式 ---- */