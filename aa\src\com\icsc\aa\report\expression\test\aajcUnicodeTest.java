/**
 * 
 */
package com.icsc.aa.report.expression.test;

import java.math.BigDecimal;

import junit.framework.TestCase;

/**
 * <AUTHOR>
 * 
 */
public class aajcUnicodeTest extends TestCase {

	/*
	 * (non-Javadoc)
	 * 
	 * @see junit.framework.TestCase#setUp()
	 */
	protected void setUp() throws Exception {
		super.setUp();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see junit.framework.TestCase#tearDown()
	 */
	protected void tearDown() throws Exception {
		super.tearDown();
	}

	public void testUnicode() {

		// Letter
		// : '\u0024' |
		// '\u0041'..'\u005a' |
		// '\u005f' |
		// '\u0061'..'\u007a' |
		// '\u00c0'..'\u00d6' |
		// '\u00d8'..'\u00f6' |
		// '\u00f8'..'\u00ff' |
		// '\u0100'..'\u1fff' |
		// '\u3040'..'\u318f' |
		// '\u3300'..'\u337f' |
		// '\u3400'..'\u3d2d' |
		// '\u4e00'..'\u9fff' |
		// '\uf900'..'\ufaff'
		// ;

		System.out.print(new BigDecimal("39.014999999").setScale(2, 4));
//		int[] charstart = { 0x0024, 0x0041, 0x005f, 0x0061, 0x00c0, 0x00d8, 0x00f8, 0x0100, 0x3040, 0x3400, 0x4e00, 0xf900 };
//		int[] charEnd = { 0x0024, 0x005a, 0x005f, 0x007a, 0x00d6, 0x00f6, 0x00ff, 0x1fff, 0x31bf, 0x3d2d, 0x9fff, 0xfaff };
//		for (int i = 0; i < charstart.length; i++) {
//			String hexS = Integer.toHexString(charstart[i] & 0xFF);
//			String hexE = Integer.toHexString(charEnd[i] & 0xFF);
//			if (hexS.length() == 1) {
//				hexS = '0' + hexS;
//			}
//			if (hexE.length() == 1) {
//				hexE = '0' + hexE;
//			}
//			System.out.println("");
//			System.out.println(hexS.toUpperCase() + "-" + hexE.toUpperCase());
//
//			int num = 0;
//			for (int j = charstart[i]; j <= charEnd[i]; j++) {
//				if (num != 0 && num % 50 == 0) {
//					System.out.println("");
//				}
//				System.out.print((char) j);
//				num++;
//			}
//		}

	}
}
