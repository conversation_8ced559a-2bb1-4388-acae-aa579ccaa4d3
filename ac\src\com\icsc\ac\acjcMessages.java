package com.icsc.ac;

import com.icsc.dpms.dd.msg.ddjcMsg;
import com.icsc.dpms.de.dejc332;

public class acjcMessages {
	// private static dejc332 de332;
	public static String[] LangList = { "Chinese", "English" };

	private static String Language = LangList[1];

	public static void setLanguage(String Lang) {
		acjcMessages.Language = Lang;
	}

	public static String getLanguage() {
		return acjcMessages.Language;
	}

	private static ddjcMsg ddMsg = new ddjcMsg("ac");
	static {
		try {
			dejc332.getConfig("ddjcMsg", "ddMsg.txt");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static String get(String key, String str) {
		try {
			if (acjcMessages.Language.equals("English")) {
				return ddMsg.get(key, str);
			} else
				return str;
		} catch (Exception e) {
			return str;
		}
	}
}
