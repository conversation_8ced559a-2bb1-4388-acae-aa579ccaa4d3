<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%@ page import = "com.icsc.ip.gui.*" %>

<%! public static String _AppId = "ACJJMAKECOST";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = zafctool.trim(request.getParameter("compId_qry"));
	//String factory = zafctool.trim(request.getParameter("factory_qry"));
	String workArea = zafctool.trim(request.getParameter("workArea_qry"));
	String costCenter = zafctool.trim(request.getParameter("costCenter_qry"));
	String fiscalYear = zafctool.trim(request.getParameter("fiscalYear_qry"));
%>
<textarea name="CHILDSELECT" >
	<select name="COSTCENTER0">
		<%=ipjcWorkArea.getCCOptions(_dsCom, compId, fiscalYear, workArea, costCenter)%>
	</select>
</textarea>

<script>
	if(parent){
		parent.document.all("CC").innerHTML = document.all("CHILDSELECT").value;
	}
</script>

