<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ADJJPLANPRICE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = zafctool.trim(request.getParameter("compId"));
	String prodCode = request.getParameter("prodCode") == null ? "" :request.getParameter("prodCode");
	String tradeNo = request.getParameter("tradeNo") == null ? "" :request.getParameter("tradeNo");
	
%>
<textarea name="CHILDSELECT">
	<select name="tradeNo_qry">
		<%=ipjcAlloyNo.getSelectOptions(_dsCom, compId, prodCode, tradeNo) %>
	</select>
</textarea>  

<script>
	if(parent){
		parent.document.all("TN").innerHTML = document.all("CHILDSELECT").value;
	}
</script>