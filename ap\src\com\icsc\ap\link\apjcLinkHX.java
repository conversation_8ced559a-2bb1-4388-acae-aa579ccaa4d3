/**
 * 
 */
package com.icsc.ap.link;

import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.hr.api.hrjcApiForAp;

/**
 *
 */
public class apjcLinkHX {
    String AppId;

    dsjccom dsCom = null;

    private dejc318 de318;
    
    public apjcLinkHX(dsjccom dsCom) {
        this.dsCom = dsCom;
        this.AppId = "apjcLinkHX".toUpperCase();
        de318 = new dejc318(dsCom,AppId);
    }
    
    public String getDeptByCostCenter(String compId, String costCenter){
        de318.logs("compId---costCenter:", compId + "---" + costCenter);
        
        String deptNo = hrjcApiForAp.getDeptNoByCostCenter(dsCom, costCenter);

        de318.logs("deptNo---:", deptNo);
        
        return deptNo;
    }
    
}
