<%@ page contentType="text/html; charset=gb2312" %>
<%! static final String _pageId= "SUONLINECOUNTSERVER";%>

<%@ page import="java.util.*" %>
<%@ page import="java.lang.reflect.Method" %>
<%@ page import="java.math.BigDecimal" %>
<%!
    private List  getMethodNames(String className) throws ClassNotFoundException,Exception{
        Class c = Class.forName(className);
        Method[] ms2 = c.getDeclaredMethods();
        List methodNames=new ArrayList();
        for(int i=0;i<ms2.length;i++){
            String methodName="";
            Method mt = ms2[i];
            
            Class rtnType = mt.getReturnType();
            if(!rtnType.getName().equals("java.lang.String") &&!rtnType.getName().equals("java.math.BigDecimal")&&!rtnType.getName().equals("java.lang.Object") ){
                continue;
            }
            methodName=mt.getName()+"(";
            Class[] cp = mt.getParameterTypes();
            for(int j=0;j<cp.length;j++){
                 methodName+=cp[j].getName();
                 if(j!=cp.length-1){
                    methodName+=",";
                 }
            }
            methodName+=")";
            methodNames.add(methodName);
        }
        return methodNames;
    }
    
%>
<%
	String className=request.getParameter("className")==null?"":request.getParameter("className").toString().trim();
	response.setContentType("text/xml; charset=UTF-8");
	response.setHeader("Cache-Control","no-cache");
	
	StringBuffer sb = new StringBuffer();
	sb.append("<response>"); 
	
	try{
		List methodNames= this.getMethodNames(className);
		
		sb.append("<statusCode>1</statusCode>");
		sb.append("<methods>" + methodNames.size() + "</methods>");
		for(int i = 0 ; i < methodNames.size() ; i++){
			sb.append("<method" + i + ">" +methodNames.get(i).toString().trim() + "</method" + i + ">");
	  }
		
	}catch(ClassNotFoundException e){
		sb.append("<statusCode>2</statusCode>");
	}catch(Exception e){
		sb.append("<statusCode>3</statusCode>");
	}
	
	
	sb.append("</response>");
	
	System.out.println(sb.toString());
	out.println(sb.toString());
	
%> 
