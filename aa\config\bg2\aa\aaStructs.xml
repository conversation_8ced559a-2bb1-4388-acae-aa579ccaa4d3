<?xml version="1.0" encoding="BIG5"?>
<pages>
<version>2.0</version> 
    <page pageID="aajjcp" path="aajjcpm.jsp">
        <controller>com.icsc.aa.func.aajccpFunc</controller>
        <action flag="I" method="query" forward="aajjcpm.jsp"/>
        <action flag="N" method="create" validate="" forward="aajjcpm.jsp"/>
        <action flag="R" method="update" validate="" forward="aajjcpm.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajjcpm.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajccpVO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjFactory" path="aajjFactorym.jsp">
        <controller>com.icsc.aa.func.aajcFactoryFunc</controller>
        <action flag="I" method="query" forward="aajjFactorym.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjFactorym.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjFactorym.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjFactorym.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcFactoryVO"
                type="unique"/>
        </converter>
    </page>

    <page pageID="aajj01" path="aajj013.jsp">
        <controller>com.icsc.aa.func.aajc01Func</controller>
				<action flag="Q" method="queryList" forward="aajj012.jsp"/>
				<action flag="I" method="query" forward="aajj013.jsp"/>
        <action flag="N" method="create" forward="aajj013.jsp"/>
 				<action flag="R" method="update"  forward="aajj013.jsp"/>
        <action flag="D" method="delete" forward="aajj013.jsp"/>
				<action flag="S" method="cancelDelete" forward="aajj013.jsp"/>
				<action flag="NEXT" method="next"  forward="aajj013.jsp"/>
				<action flag="PREV" method="prev"  forward="aajj013.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc01VO"
                type="unique"/>
        </converter>
        <converter>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajc01VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajj02" path="aajj02.jsp">
       <action flag="I" method="query" forward="aajj02.jsp"/> 
       <converter>
             <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct1VO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajct2VO"
                type="unique"/>      
       </converter>
    </page>
    
    <page pageID="aajj02Edit" path="aajj02Edit.jsp">
        <controller>com.icsc.aa.func.aajc02Func</controller>
        <action flag="N" method="create" validate="create_validate" forward="aajj02Edit.jsp"/>
        <action flag="NC" method="createConfirm" validate="createConfirm_validate" forward="aajj02Edit.jsp"/>
        <action flag="I" method="query" forward="aajj02Edit.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajj02Edit.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajj02Edit.jsp"/>
        <action flag="RU" method="updateHead"  forward="aajj02Edit.jsp"/>
        <action flag="C" method="confirm" validate="confirm_validate" forward="aajj02Edit.jsp"/>
        <action flag="A" method="audit" validate="audit_validate" forward="aajj02Edit.jsp"/>
        <action flag="U" method="unConfirm" forward="aajj02Edit.jsp"/>
        <action flag="UA" method="unAudit" forward="aajj02Edit.jsp"/>
        <action flag="P" method="prev" forward="aajj02Edit.jsp"/>
        <action flag="X" method="next" forward="aajj02Edit.jsp"/>
        <action flag="T" method="print" forward="aajj02Edit.jsp"/>
        <action flag="PO" method="post" validate="post_validate" forward="aajj02Edit.jsp"/>
        <action flag="UPO" method="unPost" validate="unPost_validate" forward="aajj02Edit.jsp"/>
        <action flag="CX" method="doCx" forward="aajj02Edit.jsp"/>        
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct1VO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajct2VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajj021" path="aajj021.jsp">
        <controller>com.icsc.aa.func.aajc02Func</controller>
        <action flag="N" method="create" validate="create_validate" forward="aajj021.jsp"/>
        <action flag="I" method="query" forward="aajj021.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajj021.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajj021.jsp"/>
        <action flag="C" method="confirm" validate="confirm_validate" forward="aajj021.jsp"/>
        <action flag="A" method="audit" validate="audit_validate" forward="aajj021.jsp"/>
        <action flag="U" method="unConfirm" forward="aajj021.jsp"/>
        <action flag="UA" method="unAudit" forward="aajj021.jsp"/>
        <action flag="P" method="prev" forward="aajj021.jsp"/>
        <action flag="X" method="next" forward="aajj021.jsp"/>
        <action flag="T" method="print" forward="aajj021.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct1VO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajct2VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjrf" path="aajjrfm.jsp">
        <controller>com.icsc.aa.func.aajcrfFunc</controller>
        <action flag="I" method="query" forward="aajjrfm.jsp"/>
        <action flag="N" method="create" validate="" forward="aajjrfm.jsp"/>
        <action flag="R" method="update" validate="" forward="aajjrfm.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajjrfm.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcrfVO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajj0y" path="aajj0ym.jsp">
        <controller>com.icsc.aa.func.aajc0yFunc</controller>
        <action flag="I" method="query" forward="aajj0ym.jsp"/>
        <action flag="N" method="create" validate="" forward="aajj0ym.jsp"/>
        <action flag="R" method="update" validate="" forward="aajj0ym.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajj0ym.jsp"/>
        <action flag="B" method="prevYear" validate="" forward="aajj0ym.jsp"/>
        <action flag="F" method="NextYear" validate="" forward="aajj0ym.jsp"/>
        <action flag="Y" method="addAcctYear" validate="" forward="aajj0ym.jsp"/>
        <action flag="O" method="openPeriod" validate="" forward="aajj0ym.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc04VO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajj0k" path="aajj0km.jsp">
        <controller>com.icsc.aa.func.aajc0kFunc</controller>
        <action flag="I" method="query" forward="aajj0km.jsp"/>
        <action flag="N" method="create" validate="" forward="aajj0km.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajj0km.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc0KVO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajj23" path="aajj23m.jsp">
        <controller>com.icsc.aa.func.aajc23Func</controller>
        <action flag="I" method="query" forward="aajj23m.jsp"/>
        <action flag="N" method="create" validate="" forward="aajj23m.jsp"/>
        <action flag="R" method="update" validate="" forward="aajj23m.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajj23m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc23VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajj25" path="aajj25m.jsp">
        <controller>com.icsc.aa.func.aajc25Func</controller>
        <action flag="I" method="query" forward="aajj25m.jsp"/>
        <action flag="N" method="create" validate="" forward="aajj25m.jsp"/>
        <action flag="R" method="update" validate="" forward="aajj25m.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajj25m.jsp"/>
        <action flag="K" method="open" validate="" forward="aajj25m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc25VO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajj24" path="aajj24m.jsp">
        <controller>com.icsc.aa.func.aajc24Func</controller>
        <action flag="I" method="query" forward="aajj24m.jsp"/>
        <action flag="N" method="create" validate="" forward="aajj24m.jsp"/>
        <action flag="R" method="update" validate="" forward="aajj24m.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajj24m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc24VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajj26" path="aajj26m.jsp">
        <controller>com.icsc.aa.func.aajc26Func</controller>
        <action flag="I" method="query" forward="aajj26m.jsp"/>
        <action flag="N" method="create" validate="" forward="aajj26m.jsp"/>
        <action flag="R" method="update" validate="" forward="aajj26m.jsp"/>
        <action flag="D" method="delete" validate="" forward="aajj26m.jsp"/>
        <action flag="K" method="open" validate="" forward="aajj26m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc26VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajjm04" path="aajjm04m.jsp">
        <controller>com.icsc.aa.func.aajcm04Func</controller>
        <action flag="I" method="query" forward="aajjm04m.jsp"/>
        <action flag="R" method="update" forward="aajjm04m.jsp"/>
        <action flag="Q" method="queryDetail" forward="aajjm04d.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc04VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjt2" path="aajjt2m.jsp">
        <controller>com.icsc.aa.func.aajct2Func</controller>
        <action flag="I" method="query" forward="aajjt2m.jsp"/>
        <action flag="A" method="advquery" forward="aajjt2m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct2VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjt2tmp" path="aajjt2mtmp.jsp">
        <controller>com.icsc.aa.func.aajct2tmpFunc</controller>
        <action flag="I" method="query" forward="aajjt2mtmp.jsp"/>
        <action flag="A" method="advquery" forward="aajjt2mtmp.jsp"/>
        <action flag="IM" method="doImport"/>
        <action flag="CA" method="cancleImport"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct2tmpVO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajjt1" path="aajjt1m.jsp">
        <controller>com.icsc.aa.func.aajct1Func</controller>
        <action flag="I" method="query" forward="aajjt1m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct1VO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajct2VO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjt1tmp" path="aajjt1mtmp.jsp">
        <controller>com.icsc.aa.func.aajct1tmpFunc</controller>
        <action flag="I" method="query" forward="aajjt1mtmp.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct1tmpVO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajct2tmpVO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjcpLoginD" path="aajjcpLoginD.jsp">
        <controller>com.icsc.aa.func.aajccpLoginFunc</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" validate="create_validate" forward="aajjcpLoginD.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjcpLoginD.jsp"/>
        <action flag="D" method="remove" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajccpLoginMVO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajccpLoginDVO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajj17" path="aajj17m.jsp">
        <controller>com.icsc.aa.func.aajc17Func</controller>
        <action flag="I" method="query" forward="aajj17m.jsp"/>
        <action flag="E" method="Execute" forward="aajj17m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc17VO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajj18" path="aajj18.jsp">
        <controller>com.icsc.aa.func.aajc17Func</controller>
        <action flag="I" method="query" forward="aajj18.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc17VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajj20" path="aajj20.jsp">
        <controller>com.icsc.aa.func.aajc19Func</controller>
        <action flag="I" method="query" forward="aajj20.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc19VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajj19" path="aajj19m.jsp">
        <controller>com.icsc.aa.func.aajc19Func</controller>
        <action flag="I" method="query" forward="aajj19m.jsp"/>
        <action flag="E" method="Execute" forward="aajj19m.jsp"/>
        <action flag="C" method="RollBack" forward="aajj19m.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc19VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajjTrialBalanceR" path="aajjd352.jsp">
        <controller>com.icsc.aa.func.aajcTrialBalanceFunc</controller>
        <action flag="I" method="query" forward="aajjd352.jsp"/>
        <action flag="XLS" method="printXls" forward="aajjreportview.jsp"/>
        <converter>
        </converter>
    </page>


    <page pageID="aajjsuspend" path="aajjsuspendm.jsp">
        <controller>com.icsc.aa.func.aajcsuspendFunc</controller>
        <action flag="I"  method="query" forward="aajjsuspendm.jsp"/>
        <action flag="A"  method="advquery" forward="aajjsuspendm.jsp"/>
        <action flag="D"  method="delete" forward="aajjsuspendm.jsp" validate="delete_validate"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc05VO"
                type="sequence"/>
        </converter>
    </page>


    <page pageID="aajjalsSrc" path="aajjalsSrc.jsp">
        <controller>com.icsc.aa.func.aajcalsFunc</controller>
        <action flag="I" method="query"  forward="aajjalsSrc.jsp"/>
        <action flag="N" method="insert" validate="insert_validate" forward="aajjalsSrc.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjalsSrc.jsp"/>
        <action flag="D" method="delete" forward="aajjalsSrc.jsp"/>
        <action flag="O" method="outDB" forward="aajjalsSrc.jsp"/>
        <action flag="P" method="post"   forward="aajjalsSrc.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.aajbt3"
                type="unique"/>
        </converter>
    </page>

    <page pageID="aajjPrintVc" path="aajjPrintVcm.jsp">
        <controller>com.icsc.aa.aajcPrintVc</controller>
        <action flag="Pr" method="preview" forward="aajjPrintVcm.jsp"/>
        <action flag="P" method="print" forward="aajjPrintVcP.jsp"/>
        <action flag="Pre" method="prevPage" forward="aajjPrintVcm.jsp"/>
        <action flag="Nxt" method="nxtPage" forward="aajjPrintVcm.jsp"/>
        <action flag="A" method="advquery" forward="aajjPrintVcm.jsp"/>
        <converter>
        </converter>
    </page>



    <page pageID="aajjPreCountPdf" path="aajjPreCountPdf.jsp"  uiCtrl="false"  >
        <controller>com.icsc.aa.prtPdf.aajcPreCountPrtp</controller>
        <action flag="P" method="print"  forward="aajjPreCountPdf.jsp" />
        <converter>
            <valueObject objectID="t"
                class="com.icsc.aa.dao.aajc02VO"
                type="sequence"/>
        </converter>
    </page>


    <page pageID="aajjExcelImportMain" path="aajjExcelImportMain.jsp">
        <controller>com.icsc.aa.func.aajcExcelImportFunc</controller>
        <action flag="IP" method="Exlimport" forward="aajjExcelImportMain.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc02VO"
                type="unique"/>
        </converter>
    </page>

    <page pageID="aajjPrintMain" path="aajjPrintMain.jsp">
        <controller>com.icsc.aa.func.aajcPrintMainFunc</controller>
        <action flag="I" method="preview" forward="aajjPrintMain.jsp"/>
        <action flag="P" method="print" forward="aajjPrintMain.jsp"/>
        <action flag="A" method="advquery" forward="aajjPrintMain.jsp"/>
				<action flag="PDF" method="printPDF" forward="aajjPrintMain.jsp"/>
        <action flag="APPLET" method="printAPPLET" forward="aajjPrintMain.jsp"/>
				<action flag="APPLETPRINT" method="printAPPLETOnInit" forward="aajjPrintMain.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajct2VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajj19s" path="aajj19sm.jsp">
        <controller>com.icsc.aa.func.aajc19sFunc</controller>
        <action flag="I" method="query" forward="aajj19sm.jsp"/>
        <action flag="E" method="confirm" forward="aajj19sm.jsp"/>
        <action flag="C" method="back" forward="aajj19sm.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc19VO"
                type="sequence"/>
        </converter>
    </page>


	
    <page pageID="aajjAutoComplete" path="aajjAutoComplete.jsp">
        <controller>com.icsc.aa.func.aajcAutoComplete</controller>
        <action flag="acctCode" method="queryAcctCode" completeAction="true"/>
        <action flag="idCode" method="queryIdCode" completeAction="true"/>
        <action flag="refNo" method="queryRefNo" completeAction="true"/>
        <action flag="aid03Code" method="queryAid03Code" completeAction="true"/>
        <action flag="aid04Code" method="queryAid04Code" completeAction="true"/>
        <converter>
        </converter>
    </page>

  <page pageID="aajjVchrUpload" path="aajjVchrUpload.jsp">
			<controller>com.icsc.aa.func.aajcVchrUploadFunc</controller>
			<action flag="U" method="upload" forward="aajjVchrUpload.jsp" />
			<converter>

			</converter>
	</page>


		<page pageID="aajjreport" path="aajjreportview.jsp">
			<controller>com.icsc.aa.print.aajcPrtProxy</controller>
			<action flag="PDF" method="printPDF" forward="aajjreportviewPDF.jsp" />
			<action flag="XLS" method="printXLS" forward="aajjreportview.jsp" />
			<action flag="PDFAJAX" method="printPDFAjax" forward="aajjAjaxEcho.jsp" />
			<action flag="XLSAJAX" method="printXLSAjax" forward="aajjAjaxEcho.jsp" />
			<action flag="JSON" method="getJsonData" forward="aajjAjaxEcho.jsp" />
			<converter>

			</converter>
		</page>

    <page pageID="aajj03R" path="aajj03R.jsp">
        <controller>com.icsc.aa.func.aajc03RFunc</controller>
        <action flag="I" method="query" forward="aajj03R.jsp"/>
        <action flag="XLS" method="printXls" forward="aajjreportview.jsp"/>
        <converter>
        </converter>
    </page>
    <page pageID="aajj03RReceive" path="aajj03RReceive.jsp">
        <controller>com.icsc.aa.func.aajc03RReceiveFunc</controller>
        <action flag="I" method="query" forward="aajj03RRecevie.jsp"/>
        <action flag="XLS" method="printXls" forward="aajjreportview.jsp"/>
        <converter>
        </converter>
    </page>

 	<page pageID="aajj16R" path="aajj16R.jsp">
        <controller>com.icsc.aa.func.aajc16RFunc</controller>
        <action flag="I" method="query" forward="aajj16R.jsp"/>
        <action flag="XLS" method="printXls" forward="aajjreportview.jsp"/>
        <converter>
        </converter>
    </page>

	<page pageID="aajjFuncDefEdit" path="aajjFuncDefEdit.jsp">
        <controller>com.icsc.aa.report.web.aajcFuncDefEditFunc</controller>
        <action flag="I" method="query" forward="aajjFuncDefEdit.jsp"/>
        <action flag="RE" method="register" forward="aajjFuncDefEdit.jsp"/>
        <action flag="UNRE" method="unRegister" forward="aajjFuncDefEdit.jsp"/>
				<action flag="UP" method="update" forward="aajjFuncDefEdit.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcFuncDefVO"
                type="unique"/>
			<valueObject objectID="v2"
                class="com.icsc.aa.dao.aajcFuncParameterVO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajjTemplateManage" path="aajjTemplateManage01.jsp">
        <controller>com.icsc.aa.report.web.aajcTemplateManageFunc</controller>
        <action flag="II" method="query" forward="aajjTemplateManage01.jsp"/>
				<action flag="I" method="query1" forward="aajjTemplateManageList.jsp"/>
				<action flag="U" method="upload" forward="aajjTemplateManage01.jsp" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcRptTemplateVO"
                type="unique"/>
        </converter>
    </page>

    <page pageID="aajjTemplateManage02" path="aajjTemplateManage02.jsp">
        <controller>com.icsc.aa.report.web.aajcTemplateManageFunc</controller>
        <action flag="I" method="query0" forward="aajjTemplateManage02.jsp"/>
        <action flag="R" method="update" validate="" forward="aajjTemplateManage02.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcRptTemplateVO"
                type="unique"/>
        </converter>
    </page>
    <page pageID="aajjRptGen" path="aajjRptGen01.jsp">
        <controller>com.icsc.aa.report.web.aajcRptGenFunc</controller>
        <action flag="I" method="query" forward="aajjRptGen01.jsp"/>
 				<action flag="C" method="excute" forward="aajjRptGen01.jsp"/>
        <action flag="O" method="outexcel"  forward="aajjRptGenResult.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcRptTemplateVO"
                type="sequence"/>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajcRptDataVO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajjTemplateManage" path="aajjTemplateManage01.jsp">
        <controller>com.icsc.aa.report.web.aajcTemplateManageFunc</controller>
        <action flag="II" method="query" forward="aajjTemplateManage01.jsp"/>
				<action flag="I" method="query1" forward="aajjTemplateManageList.jsp"/>
				<action flag="D" method="delete" forward="aajjTemplateManageList.jsp"/>
				<action flag="U" method="upload" forward="aajjTemplateManage01.jsp" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcRptTemplateVO"
                type="unique"/>
			<valueObject objectID="v2"
                class="com.icsc.aa.dao.aajcRptTemplateVO"
                type="sequence"/>
        </converter>
    </page>

	<page pageID="aajjAjaxQuery" path="aajjAjaxQuery.jsp">
		<controller>com.icsc.aa.func.aajcAjaxQueryFunc</controller>
			<action flag="qB" method="queryBalance" forward="aajjAjaxQuery.jsp" />
		<converter>
			
		</converter>
	</page>
    
	<page pageID="aajjAjax" path="aajjAjaxEcho.jsp">
		<controller>com.icsc.aa.func.aajcAjaxFunc</controller>
			<action flag="validateAcctSrl" method="validateAcctSrl" forward="aajjAjaxEcho.jsp" />
			<action flag="queryAcctCodeLevl1" method="queryAcctCodeLevl1" forward="aajjAjaxEcho.jsp" />
			<action flag="queryGroup" method="queryGroup" forward="aajjAjaxEcho.jsp" />
			<action flag="createGroup" method="createGroup" forward="aajjAjaxEcho.jsp" />
			<action flag="authAcct" method="authAcct" forward="aajjAjaxEcho.jsp" />
			<action flag="queryQuthList" method="queryQuthList" forward="aajjAjaxEcho.jsp" />
			<action flag="queryAcctCodeAuthed" method="queryAcctCodeAuthed" forward="aajjAjaxEcho.jsp" />
			<action flag="queryAcctCodeList" method="queryAcctCodeList" forward="aajjAjaxEcho.jsp" />
      <action flag="remove" method="remove" forward="aajjAjaxEcho.jsp" />
			<action flag="queryGpCodeList" method="queryGpCodeList" forward="aajjAjaxEcho.jsp" />
			<action flag="queryGpCode" method="queryGpCode" forward="aajjAjaxEcho.jsp" />
			<action flag="queryIdCodeList" method="queryIdCodeList" forward="aajjAjaxEcho.jsp" />
			<action flag="queryRefCodeList" method="queryRefCodeList" forward="aajjAjaxEcho.jsp" />
			<action flag="OE" method="outExcel" forward="aajjAjaxEcho.jsp" />
		<converter>
			
		</converter>
	</page>

     <page pageID="aajj2911aEdit" path="aajj2911aEdit.jsp" uiCtrl="true">
        <controller>com.icsc.aa.func.aajc2911aEditFunc</controller>
        <action flag="I" method="query" forward="aajj2911aEdit.jsp"/>
        <action flag="N" method="create"/>
        <action flag="D" method="delete"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc291VO"
                type="sequence"/>
        </converter>
    </page>

    <page pageID="aajj2911bEdit" path="aajj2911bEdit.jsp" uiCtrl="true">
        <controller>com.icsc.aa.func.aajc2911bEditFunc</controller>
        <action flag="I" method="query" forward="aajj2911bEdit.jsp"/>
                <action flag="N" method="save"/>
                <action flag="D" method="remove"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajc293VO"
                type="sequence"/>
        </converter>
    </page>
    
        <page pageID="aajjVchrRule" path="aajjVchrRule.jsp" >
        <controller>com.icsc.aa.func.aajcVchrRuleFunc</controller>
        <action flag="G" method="generate" forward="aajjVchrRule.jsp"/>
        <action flag="P" method="preView" validate="" forward="aajjVchrRule.jsp"/>
        <action flag="E" method="edit" validate="" forward="aajjVchrRule.jsp"/>
        <converter>

        </converter>
    </page>

    <page pageID="aajjVchrRuleEdit" path="aajjVchrRuleEdit.jsp" >
        <controller>com.icsc.aa.func.aajcVchrRuleEditFunc</controller>
                <action flag="Q" method="query" forward="aajjVchrRuleEdit.jsp"/>
        <action flag="S" method="save" forward="aajjVchrRuleEdit.jsp"/>
                <action flag="D" method="delete" forward="aajjVchrRuleEdit.jsp"/>
        <converter>
                        <valueObject objectID="v1" class="com.icsc.aa.dao.aajcVchrRuleVO" type="unique"/>
                        <valueObject objectID="v2" class="com.icsc.aa.dao.aajcVchrRuleDetailVO" type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjAid03Type" path="aajjAid03Typem.jsp">
        <controller>com.icsc.aa.func.aajcAid03TypeFunc</controller>
        <action flag="I" method="query" forward="aajjAid03Typem.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjAid03Typem.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjAid03Typem.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjAid03Typem.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcAid03TypeVO"
                type="unique"/>
        </converter>
    </page>
    
    <page pageID="aajjAid03Data" path="aajjAid03Datam.jsp">
        <controller>com.icsc.aa.func.aajcAid03DataFunc</controller>
        <action flag="I" method="query" forward="aajjAid03Datam.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjAid03Datam.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjAid03Datam.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjAid03Datam.jsp"/>
        <action flag="K" method="open" validate="open_validate" forward="aajjAid03Datam.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcAid03DataVO"
                type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjAid04Type" path="aajjAid04Typem.jsp">
        <controller>com.icsc.aa.func.aajcAid04TypeFunc</controller>
        <action flag="I" method="query" forward="aajjAid04Typem.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjAid04Typem.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjAid04Typem.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjAid04Typem.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcAid04TypeVO"
                type="unique"/>
        </converter>
    </page>
    
    <page pageID="aajjAid04Data" path="aajjAid04Datam.jsp">
        <controller>com.icsc.aa.func.aajcAid04DataFunc</controller>
        <action flag="I" method="query" forward="aajjAid04Datam.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjAid04Datam.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjAid04Datam.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjAid04Datam.jsp"/>
        <action flag="K" method="open" validate="open_validate" forward="aajjAid04Datam.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcAid04DataVO"
                type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjFileA" path="aajjFileA.jsp">
        <controller>com.icsc.aa.func.aajcFileAFunc</controller>
        <action flag="I" method="query" forward="aajjFileA.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjFileA.jsp"/>        
        <action flag="D" method="delete" validate="delete_validate" forward="aajjFileA.jsp"/>        
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcFileVO"
                type="sequence"/>
        </converter>
    </page>
    
     <page pageID="aajj05R" path="aajj05R.jsp">
        <controller>com.icsc.aa.func.aajc05RFunc</controller>
        <action flag="I" method="query" forward="aajj05R.jsp"/>
        <action flag="XLS" method="printXls" forward="aajjreportview.jsp"/>
        <converter>
        </converter>
    </page>
    
    <page pageID="aajjBankAmtA" path="aajjBankAmtA.jsp">
        <controller>com.icsc.aa.func.aajcBankAmtAFunc</controller>
        <action flag="I" method="query" forward="aajjBankAmtA.jsp"/>  
        <action flag="N" method="create" validate="create_validate" forward="aajjBankAmtA.jsp"/>             
        <converter>            
        </converter>
    </page>
        
    <page pageID="aajjc1A" path="aajjc1A.jsp">
        <controller>com.icsc.aa.func.aajcc1AFunc</controller>
        <action flag="I" method="query" forward="aajjc1A.jsp"/>
        <action flag="C" method="clear" forward="aajjc1A.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjc1A.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjc1A.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjc1A.jsp"/>
        <action flag="ND" method="createDetail" validate="createDetail_validate" forward="aajjc1A.jsp"/>
        <action flag="RD" method="updateDetail" validate="updateDetail_validate" forward="aajjc1A.jsp"/>
        <action flag="DD" method="deleteDetail" validate="deleteDetail_validate" forward="aajjc1A.jsp"/>        
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc1VO"
                         type="unique"/>
            <valueObject objectID="v2"
                         class="com.icsc.aa.dao.aajcc2VO"
                         type="sequence"/>
        </converter>
    </page>
    <page pageID="aajjc1B" path="aajjc1B.jsp">
        <controller>com.icsc.aa.func.aajcc1BFunc</controller>
        <action flag="I" method="query" forward="aajjc1B.jsp"/>
        <action flag="P" method="print" validate="print_validate" forward="aajjc1B.jsp"/>               
        <converter>       
        	<valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc2VO"
                         type="sequence"/>     
        </converter>
    </page>
    <page pageID="aajjc1C" path="aajjc1C.jsp">
        <controller>com.icsc.aa.func.aajcc1CFunc</controller>
        <action flag="I" method="query" forward="aajjc1C.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjc1C.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjc1C.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjc1C.jsp"/>
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc11VO"
                         type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjc2A" path="aajjc2A.jsp">
        <controller>com.icsc.aa.func.aajcc2AFunc</controller>
        <action flag="I" method="query" forward="aajjc2A.jsp"/>
        <action flag="P" method="print" validate="print_validate" forward="aajjc2A.jsp"/>
        <action flag="M" method="mailNotify" validate="mailNotify_validate" forward="aajjc2A.jsp"/>                
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc1VO"
                         type="sequence"/>            
        </converter>
    </page>
    
    <page pageID="aajjc3A" path="aajjc3A.jsp">
        <controller>com.icsc.aa.func.aajcc3AFunc</controller>
        <action flag="I" method="query" forward="aajjc3A.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjc3A.jsp"/>
        <action flag="P" method="print" validate="print_validate" forward="aajjc3A.jsp"/>
        <action flag="M" method="mailNotify" validate="mailNotify_validate" forward="aajjc3A.jsp"/>                       
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc3VO"
                         type="sequence"/>            
        </converter>
    </page>
        
     <page pageID="aajjc4A" path="aajjc4A.jsp">
        <controller>com.icsc.aa.func.aajcc4AFunc</controller>
        <action flag="I" method="query" forward="aajjc4A.jsp"/>
        <action flag="C" method="confirm" validate="confirm_validate" forward="aajjc4A.jsp"/>
        <action flag="CC" method="cancelConfirm" validate="cancelConfirm_validate" forward="aajjc4A.jsp"/>
        <action flag="A" method="audit" validate="audit_validate" forward="aajjc4A.jsp"/>
        <action flag="CA" method="cancelAudit" validate="cancelAudit_validate" forward="aajjc4A.jsp"/>
        <action flag="P" method="post" validate="post_validate" forward="aajjc4A.jsp"/>
        <action flag="CP" method="cancelPost" validate="cancelPost_validate" forward="aajjc4A.jsp"/>
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajct2VO"
                         type="sequence"/>            
        </converter>
    </page>
    
    <page pageID="aajjc5A" path="aajjc5A.jsp">
        <controller>com.icsc.aa.func.aajcc5AFunc</controller>
        <action flag="I" method="query" forward="aajjc5A.jsp"/>
        <action flag="P" method="post" validate="post_validate" forward="aajjc5A.jsp"/>
        <action flag="UP" method="unPost" validate="unPost_validate" forward="aajjc5A.jsp"/>
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcExglVO"
                         type="sequence"/>            
        </converter>
    </page>        
    
    <page pageID="aajjc61B" path="aajjc61B.jsp">
        <controller>com.icsc.aa.func.aajcc61BFunc</controller>
        <action flag="I" method="Query" forward="aajjc61B.jsp"/>
        <action flag="create" method="create" forward="aajjc61B.jsp"/>
        <action flag="update" method="update" forward="aajjc61B.jsp"/>
        <action flag="delete" method="delete" forward="aajjc61B.jsp"/>
        <action flag="U" method="upload" forward="aajjc61B.jsp" />
        <action flag="export" method="export" forward="aajjc61B.jsp"/>
        <action flag="NI" method="NewQuery" forward="aajjc61B.jsp"/>
        <converter>       
        	<valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcC63VO"
                         type="sequence"/>     
        </converter>
    </page>    
    
	<page pageID="aajjc61C" path="aajjc61C.jsp">
        <controller>com.icsc.aa.func.aajcc61CFunc</controller>
        <action flag="I" method="query" forward="aajjc61C.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjc61C.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjc61C.jsp"/>
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcC62VO"
                         type="sequence"/>
        </converter>
    </page>
    
	<page pageID="aajjc61A" path="aajjc61A.jsp">
        <controller>com.icsc.aa.func.aajcc61AFunc</controller>
        <action flag="I" method="query" forward="aajjc61A.jsp"/>
        <action flag="C" method="clear" forward="aajjc61A.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjc61A.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjc61A.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjc61A.jsp"/>
        <action flag="ND" method="createDetail" validate="createDetail_validate" forward="aajjc61A.jsp"/>
        <action flag="RD" method="updateDetail" validate="updateDetail_validate" forward="aajjc61A.jsp"/>
        <action flag="DD" method="deleteDetail" validate="deleteDetail_validate" forward="aajjc61A.jsp"/>        
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcC6VO"
                         type="unique"/>
            <valueObject objectID="v2"
                         class="com.icsc.aa.dao.aajcC61VO"
                         type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjc63" path="aajjc63List.jsp">
        <controller>com.icsc.aa.func.aajcc63Func</controller>
        <action flag="I" method="query" forward="aajjc63List.jsp"/>
		<action flag="C" method="excute" forward="aajjc63List.jsp"/>
        <action flag="O" method="outexcel"  forward="aajjc63List.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcC6VO"
                type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjc8A" path="aajjc8A.jsp">
        <controller>com.icsc.aa.func.aajcc8AFunc</controller>
        <action flag="I" method="query" forward="aajjc8A.jsp"/>
        <action flag="P" method="print" validate="print_validate" forward="aajjc8A.jsp"/>                        
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc1VO"
                         type="sequence"/>            
        </converter>
    </page>
    
    <page pageID="aajjc9A" path="aajjc9A.jsp">
        <controller>com.icsc.aa.func.aajcc9AFunc</controller>
        <action flag="I" method="query" forward="aajjc9A.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjc9A.jsp"/>        
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajct2VO"
                         type="sequence"/>            
        </converter>
    </page>
        
    <page pageID="aajjc10A" path="aajjc10A.jsp">
        <controller>com.icsc.aa.func.aajcc10AFunc</controller>
        <action flag="I" method="query" forward="aajjc10A.jsp"/>
        <action flag="N" method="create" validate="create_validate" forward="aajjc10A.jsp"/>
        <action flag="R" method="update" validate="update_validate" forward="aajjc10A.jsp"/>
        <action flag="D" method="delete" validate="delete_validate" forward="aajjc10A.jsp"/>
        <action flag="P" method="post" validate="post_validate" forward="aajjc10A.jsp"/>
        <action flag="CP" method="cancelPost" validate="cancelPost_validate" forward="aajjc10A.jsp"/>        
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcc8VO"
                         type="sequence"/>            
        </converter>
    </page>
    
    <page pageID="aajjProdPrint" path="aajjProdPrint.jsp">
        <controller>com.icsc.aa.func.aajcProdPrintFunc</controller>
        <action flag="prodPrint" method="prodPrint" forward="aajjProdPrint.jsp"/>
        <converter>
        </converter>
    </page>
    <page pageID="aajjc631" path="aajjc631A.jsp">
        <controller>com.icsc.aa.func.aajcc63Func</controller>
        <action flag="I1" method="query1" forward="aajjc631A.jsp"/>
        <converter>
            <valueObject objectID="v2"
                class="com.icsc.aa.dao.aajcc3VO"
                type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjc64A" path="aajjc64A.jsp">
        <controller>com.icsc.aa.func.aajcc64Func</controller>
        <action flag="I" method="query" forward="aajjc64A.jsp"/>
        <action flag="doQuery" method="doQuery" forward="aajjc64B.jsp"/>
        <action flag="doVerificate" method="doVerificate" forward="aajjc65.jsp"/>
        <action flag="doCancle" method="doCancle" forward="aajjc65.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcC65VO"
                type="sequence"/>
        </converter>
    </page>
    <page pageID="aajjc64B" path="aajjc64B.jsp">
        <controller>com.icsc.aa.func.aajcc64Func</controller>
        <action flag="I" method="queryIS" forward="aajjc64B.jsp"/>
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.aa.dao.aajcC65VO"
                type="sequence"/>
        </converter>
    </page>
    
    <page pageID="aajjc65" path="aajjc65.jsp">
        <controller>com.icsc.aa.func.aajcc64Func</controller>
        <action flag="doVerificate" method="doVerificate"  forward="aajjc65.jsp"/>
        <action flag="UnwVerificate" method="UnwVerificate"  forward="aajjc65X.jsp"/>
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.aa.dao.aajcC62VO"
                         type="sequence"/>
        </converter>
    </page>	
        
    
</pages>
