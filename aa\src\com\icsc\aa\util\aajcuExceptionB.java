package com.icsc.aa.util;

import java.math.BigDecimal;

import com.icsc.dpms.de.dejcMyException;

public class aajcuExceptionB extends dejcMyException {

	private static final long serialVersionUID = 1L;

	public final String AppId = "aajcuException".toUpperCase();

	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:20 $";

	public aajcuExceptionB(String msg) {
		super(msg);
	}


	public static void isNull(Object obj, String msg) throws aajcuExceptionB {
		if (obj == null)
			throw new aajcuExceptionB(msg);
	}

	
	public static void isSpace(String s, String msg) throws aajcuExceptionB {
		if (s == null || s.trim().equals("")) {
			throw new aajcuExceptionB(msg);
		}
	}


	public static void isZero(int i, String msg) throws aajcuExceptionB {
		if (i == 0) {
			throw new aajcuExceptionB(msg);
		}
	}

	
	public static void isZero(BigDecimal value, String msg) throws aajcuExceptionB {
		if (aajcuMoneyB.isEqualZero(value)) {
			throw new aajcuExceptionB(msg);
		}
	}

	
	public static void isTrue(boolean value, String msg) throws aajcuExceptionB {
		if (value) {
			throw new aajcuExceptionB(msg);
		}
	}
	
	public static void isStar(String s, String msg) throws aajcuExceptionB {
		if (s == null || s.trim().equals("*")) {
			throw new aajcuExceptionB(msg);
		}
	}

}
