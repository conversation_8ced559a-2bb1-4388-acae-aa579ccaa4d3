CREATE TABLE DB.TBAAC2 (
pCompId CHAR(10) DEFAULT '' NOT NULL,compId CHAR(10) DEFAULT '' NOT NULL,checkCode CHAR(20) DEFAULT '' NOT NULL,fieldCode CHAR(20) DEFAULT '' NOT NULL,fieldCodeName CHAR(100) DEFAULT '',fieldOperator CHAR(20) DEFAULT '',helpCategory CHAR(20) DEFAULT '',helpSql CHAR(200) DEFAULT '',helpJsp CHAR(200) DEFAULT '',helpPara CHAR(200) DEFAULT '',defaultA CHAR(100) DEFAULT '',defaultB CHAR(100) DEFAULT '',keyInA CHAR(100) DEFAULT '',keyInB CHAR(100) DEFAULT '',remark CHAR(200) DEFAULT '',settingA CHAR(30) DEFAULT '',settingB CHAR(30) DEFAULT '',settingC CHAR(30) DEFAULT '',updateEmpNo CHAR(10) DEFAULT '',updateTime CHAR(20) DEFAULT '', PRIMARY KEY(pCompId,compId,checkCode,fieldCode) 
) ;
GRANT ALL ON DB.TBAAC2 TO JAVAUSER ;
