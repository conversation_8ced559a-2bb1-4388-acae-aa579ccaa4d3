CREATE TABLE DB.TBAA04 (
compId CHAR(10) DEFAULT '' NOT NULL,acctPeriod CHAR(6) DEFAULT '' NOT NULL,startDate CHAR(8) DEFAULT '' NOT NULL,endDate CHAR(8) DEFAULT '',closeYn CHAR(1) DEFAULT '',rTradeId CHAR(10) DEFAULT '',pTradeId CHAR(10) DEFAULT '',dTradeId CHAR(10) DEFAULT '',cTradeId CHAR(10) DEFAULT '',tTradeId CHAR(10) DEFAULT '',rTradeSrl NUMBER (5,0)  DEFAULT 0 ,pTradeSrl NUMBER (5,0)  DEFAULT 0 ,dTradeSrl NUMBER (5,0)  DEFAULT 0 ,cTradeSrl NUMBER (5,0)  DEFAULT 0 ,tTradeSrl NUMBER (5,0)  DEFAULT 0 ,t3TradeSrl NUMBER (5,0)  DEFAULT 0 ,isProcessing CHAR(1) DEFAULT '', PRIMARY KEY(compId,acctPeriod,startDate) 
) ;
GRANT ALL ON DB.TBAA04 TO JAVAUSER ;
