package com.icsc.aa.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class aajcuMoneyB {
	public final String AppId = "aajcuMoney".toUpperCase();

	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:20 $";

	public static BigDecimal BD_ZERO = new BigDecimal("0");

	public static BigDecimal BD_ONE = new BigDecimal("1");

	public static String getNumFormat(BigDecimal num, int decimal) {
		if (num == null)
			return null;
		String tmp = ".";
		for (int i = 0; i < decimal; i++) {
			tmp += "0";
		}
		DecimalFormat fmt = new DecimalFormat("##,###,###,###,##0");
		if (decimal != 0)
			fmt = new DecimalFormat("##,###,###,###,##0" + tmp);
		return fmt.format(num);
	}

	public static BigDecimal getUnitPrice(BigDecimal qty, BigDecimal amt, int scale) {
		if (qty.compareTo(BD_ZERO) != 0) {
			BigDecimal unitPrice = amt.divide(qty, scale, BigDecimal.ROUND_HALF_UP);
			return unitPrice;
		} else {
			BigDecimal unitPrice = BD_ZERO;
			unitPrice = unitPrice.setScale(scale);
			return unitPrice;
		}
	}

	public static String getNumFormatNoComma(BigDecimal num, int decimal) {
		if (num == null)
			return null;
		String tmp = ".";
		for (int i = 0; i < decimal; i++) {
			tmp += "0";
		}
		DecimalFormat fmt = new DecimalFormat("######0");
		if (decimal != 0)
			fmt = new DecimalFormat("######0" + tmp);
		return fmt.format(num);
	}

	public static boolean isGreatZero(BigDecimal value) {
		if (value.compareTo(aajcuMoneyB.BD_ZERO) > 0)
			return true;
		return false;
	}

	public static boolean isLessZero(BigDecimal value) {
		if (value.compareTo(aajcuMoneyB.BD_ZERO) < 0)
			return true;
		return false;
	}

	public static boolean isEqualZero(BigDecimal value) {
		if (value.compareTo(aajcuMoneyB.BD_ZERO) == 0)
			return true;
		return false;
	}

	public static boolean isNotZero(BigDecimal value) {
		if (value.compareTo(aajcuMoneyB.BD_ZERO) != 0)
			return true;
		return false;
	}

	public static boolean isAEqualB(BigDecimal valueA, BigDecimal valueB) {
		if (valueA.compareTo(valueB) == 0)
			return true;
		return false;
	}

	public static boolean isANotEqualB(BigDecimal valueA, BigDecimal valueB) {
		if (valueA.compareTo(valueB) != 0)
			return true;
		return false;
	}

	public static boolean isAGreatB(BigDecimal valueA, BigDecimal valueB) {
		if (valueA.compareTo(valueB) > 0)
			return true;
		return false;
	}

	public static boolean isALessB(BigDecimal valueA, BigDecimal valueB) {
		if (valueA.compareTo(valueB) < 0)
			return true;
		return false;
	}

	public static String getNumFormatNonZero(BigDecimal num, int decimal) {
		if (num == null)
			return null;
		String tmp = ".";
		for (int i = 0; i < decimal; i++) {
			tmp += "#";
		}
		DecimalFormat fmt = new DecimalFormat("##,###,###,###,##0");
		if (decimal != 0)
			fmt = new DecimalFormat("##,###,###,###,##0" + tmp);
		return fmt.format(num);
	}
	
	public static boolean judgeCashItem(String cashItem,String DrCr){
		if ((cashItem.equals("IN")&&DrCr.equals("1"))||(cashItem.equals("OUT")&&DrCr.equals("2"))) {
			return false;
		}
		else{
			return true;
		}	
	}
}
