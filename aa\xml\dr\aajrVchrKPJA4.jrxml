<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="aajrVchrKPJA4"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Landscape"
		 pageWidth="841"
		 pageHeight="595"
		 columnWidth="781"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="2" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />


		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="50"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="50"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="30"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="100"  isSplitAllowed="true" >
			</band>
		</detail>
		<columnFooter>
			<band height="30"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="50"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<lastPageFooter>
			<band height="50"  isSplitAllowed="true" >
			</band>
		</lastPageFooter>
		<summary>
			<band height="50"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
