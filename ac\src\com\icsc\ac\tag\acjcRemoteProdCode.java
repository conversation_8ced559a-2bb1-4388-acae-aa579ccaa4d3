package com.icsc.ac.tag;

import java.util.*;

import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.de.tag.dejcTagRemoteOption;
import com.icsc.dpms.de.tag.dejiWebDataFinder;
import com.icsc.dpms.ds.*;
import com.icsc.facc.zaf.dao.zafcCommonDAO;

public class acjcRemoteProdCode implements dejiWebDataFinder {
	private dejc318 de318;

	public List findOptions(dsjccom dsCom, String key, String selected, Map params) {
		de318 = new dejc318(dsCom, "acjcRemoteProdCode".toUpperCase());
		List list = new LinkedList();

		zafcCommonDAO dataDao = new zafcCommonDAO(dsCom);

		try {
			de318.logs("findOptions", "key==" + key);
			de318.logs("findOptions", "selected==" + selected);
			de318.logs("findOptions", "params==" + params);

			
			String workArea = null == params.get("WORKAREA0") ? "" : (String) params.get("WORKAREA0");
			String cc = null == params.get("COSTCENTER0") ? key : (String) params.get("COSTCENTER0");

			String sql = "select distinct a.FACTORA,c.PRODCODEDESC from DB.tbadpdfactor a,DB.TBIPPDATTRI b,DB.TBIPPDBASE c"
					+ " where a.compId=b.compId and a.FACTORA=b.PRODCODE and a.compId=c.compId and a.FACTORA=c.PRODCODE"
					+ " and a.compId='" + dsCom.companyId + "' and b.ATTRINAME='WORKAREA'";
			if (workArea.length() == 0) {
				sql += " and 1=2";
			} else {
				sql += (" and b.ATTRIVALUE='" + workArea + "'");
			}
			if (!cc.equals("") && !cc.equals("@@@@@")) {
				sql += (" and a.ACTIVECOSTCENTER='" + cc + "'");
			}
			sql += " order by a.FACTORA";

			Map[] datas = (Map[]) dataDao.queryVOs(sql);
			for (int i = 0; i < datas.length; i++) {
				Map data = datas[i];
				dejcTagRemoteOption option = new dejcTagRemoteOption(data.get("FACTORA").toString(), data.get(
						"PRODCODEDESC").toString());
				if (option.getValue().equals(selected)) {
					option.setSelected(true);
				}
				list.add(option);
			}
		} catch (Exception e) {
			de318.logs("findOptions", "error!", e);
		}

		return list;
	}

	public List findFieldValue(dsjccom dsCom, String key, Map m, String myDatas) {
		return null;
	}
}
