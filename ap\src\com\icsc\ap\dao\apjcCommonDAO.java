package com.icsc.ap.dao;

import com.icsc.dpms.de.*;
import com.icsc.dpms.ds.dsjccom;
import java.math.BigDecimal;
import java.sql.*;
import java.util.*;

public class apjcCommonDAO extends dejcCommonDAO {

    public apjcCommonDAO(dsjccom dsjccom) {
        super(dsjccom, AppId);
    }

    public apjcCommonDAO(dsjccom dsjccom, Connection connection) {
        super(dsjccom, connection);
        super.appId = AppId;
    }

    public int updateBySQL(String s) throws Exception {
        sql = s;
        de318.logs("updateBySQL", "sql==" + s);
        return executeUpdate(sql);
    }

    public int updateBySQLExp(String s) throws Exception {
        sql = s;
        de318.logs("updateBySQLExp", "sql==" + s);
        int i = executeUpdate(s);
        if (i == 0)
            throw new Exception(s);
        else
            return i;
    }

    public int updateExp(String s, String s1, String s2) throws Exception {
        StringBuffer stringbuffer = new StringBuffer();
        stringbuffer.append("UPDATE " + s + " SET ");
        stringbuffer.append(s1);
        stringbuffer.append(" WHERE ");
        stringbuffer.append(s2);
        sql = stringbuffer.toString();
        de318.logs("updateExp", "sql==" + sql);
        int i = executeUpdate(sql);
        if (i == 0)
            throw new Exception(sql);
        else
            return i;
    }

    public int update(String s, String s1, String s2) throws Exception {
        StringBuffer stringbuffer = new StringBuffer();
        stringbuffer.append("UPDATE " + s + " SET ");
        stringbuffer.append(s1);
        stringbuffer.append(" WHERE ");
        stringbuffer.append(s2);
        sql = stringbuffer.toString();
        de318.logs("update", "sql==" + sql);
        return executeUpdate(sql);
    }

    public void create(String s, String s1, String s2) throws Exception {
        try {
            StringBuffer stringbuffer = new StringBuffer();
            stringbuffer.append("insert into " + s);
            if (s1.length() > 0)
                stringbuffer.append(" (" + s1 + ")");
            stringbuffer.append(" values (" + s2 + ")");
            sql = stringbuffer.toString();
            de318.logs("create", "sql==" + sql);
            de318.logs("create", "rslt==" + executeUpdate(sql));
        } catch (Exception exception) {
            throw new Exception(sql);
        }
    }

    public void create(String s, Map map) throws Exception {
        StringBuffer stringbuffer = new StringBuffer();
        StringBuffer stringbuffer1 = new StringBuffer();
        String s1;
        for (Iterator iterator = map.keySet().iterator(); iterator.hasNext(); stringbuffer1
                .append(map.get(s1) + ",")) {
            s1 = (String) iterator.next();
            stringbuffer.append(s1 + ",");
        }

        create(s, stringbuffer.substring(0, stringbuffer.length() - 1),
                stringbuffer1.substring(0, stringbuffer1.length() - 1));
    }

    public BigDecimal queryAmt(String s) {
        sql = s;
        de318.logs("queryAmt", "sql==" + s);
        try {
            Map map = myQuery(s);
            return new BigDecimal(map.get("AMT") != null ? (String) map
                    .get("AMT") : "0");
        } catch (Exception exception) {
            return new BigDecimal(0.0D);
        }
    }

    public BigDecimal[] query2Amt(String s) {
        BigDecimal abigdecimal[] = new BigDecimal[2];
        sql = s;
        de318.logs("queryAmt", "sql==" + s);
        try {
            Map map = myQuery(s);
            abigdecimal[0] = new BigDecimal(
                    (String) map.get("AMT1") != null ? (String) map.get("AMT1")
                            : "0");
            de318.logs("queryAmt", "amt[0]~:" + abigdecimal[0]);
            abigdecimal[1] = new BigDecimal(
                    (String) map.get("AMT2") != null ? (String) map.get("AMT2")
                            : "0");
            de318.logs("queryAmt", "amt[1]~:" + abigdecimal[1]);
        } catch (Exception exception) {
            abigdecimal[0] = new BigDecimal(0.0D);
            abigdecimal[1] = new BigDecimal(0.0D);
        }
        return abigdecimal;
    }

    public int getCount(String s, String s1) throws Exception {
        sql = "select count(*) as COUNT from " + s + " " + s1;
        return getCount(sql);
    }

    public int getCount(String s) throws Exception {
        sql = s;
        de318.logs("getCount", "sql==" + s);
        Map map = myQuery(sql);
        if (map == null)
            return 0;
        else
            return Integer.parseInt((String) map.get("COUNT"));
    }

    public Map queryVO(String s) throws Exception {
        sql = s;
        de318.logs("queryVO", "sql==" + s);
        return myQuery(sql);
    }

    public Map[] queryVOs(String s) throws Exception {
        sql = s;
        de318.logs("queryVOs", "sql==" + s);
        return myQueryAll(sql);
    }

    public Map[] queryVOsLimit(String s, int limit) throws Exception {
        sql = s;
        de318.logs("queryVOs", "sql==" + s);
        return myQueryAll(sql, limit);
    }

    public Object queryObj(String s) throws Exception {
        sql = s;
        de318.logs("queryObj", "sql==" + s);
        return eQuery(sql);
    }

    public List queryObjs(String s) throws Exception {
        sql = s;
        de318.logs("queryObj", "sql==" + s);
        return eQueryAll(s);
    }

    public List queryVecList(String s) throws Exception {
        sql = s;
        de318.logs("queryVecList", s);
        return super.eQueryAll2(s);
    }

    public List getTableInfo(String s, Map map) throws Exception {
        dejc301 dejc301_1 = new dejc301();
        ArrayList arraylist1;
        try {
            if (map == null)
                map = new HashMap();
            ArrayList arraylist = new ArrayList();
            Connection connection = getConnection(dejc301_1);
            Statement statement = connection.createStatement();
            ResultSet resultset = statement.executeQuery(s);
            ResultSetMetaData resultsetmetadata = resultset.getMetaData();
            int i = resultsetmetadata.getColumnCount();
            for (int j = 1; j <= i; j++) {
                String s2 = resultsetmetadata.getColumnName(j);
                String s4 = resultsetmetadata.getColumnTypeName(j);
                map.put(s2, s4);
                arraylist.add(s2);
            }

            statement = null;
            resultset = null;
            arraylist1 = arraylist;
        } finally {
            dejc301_1.close();
            dejc301_1 = null;
        }
        return arraylist1;
    }

    public static final String AppId = "aajcCommonDAO".toUpperCase();

}
