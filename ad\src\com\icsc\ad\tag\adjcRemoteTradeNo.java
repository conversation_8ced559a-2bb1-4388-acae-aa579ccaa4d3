package com.icsc.ad.tag;

import java.util.*;

import com.icsc.dpms.de.tag.dejcTagRemoteOption;
import com.icsc.dpms.de.tag.dejiWebDataFinder;
import com.icsc.dpms.ds.*;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.util.zafctool;

public class adjcRemoteTradeNo implements dejiWebDataFinder {

	public List findOptions(dsjccom dsCom, String key, String selected, Map params) {
		List list = new ArrayList();

		zafcCommonDAO dataDao = new zafcCommonDAO(dsCom);

		try {
			String workArea = (String) params.get("WORKAREA0");
			String cc = null == params.get("COSTCENTER0") ? "" : (String) params.get("COSTCENTER0");
			String prodCode = null == params.get("PRODCODE0") ? "" : (String) params.get("PRODCODE0");

			// Map map = ipjcAlloyNo.getNameMap(dsCom);

			String sql = "select distinct a.FACTORB,a.DETAILPD from DB.tbadpdfactor a,DB.TBIPPDATTRI b"
					+ " where a.compId=b.compId and a.FACTORA=b.PRODCODE and a.compId='" + dsCom.companyId
					+ "' and b.ATTRINAME='WORKAREA'";
			if (workArea.length() == 0) {
				sql += " and 1=2";
			} else {
				sql += (" and b.ATTRIVALUE='" + workArea + "'");
			}
			if (!cc.equals("") && !cc.equals("@@@@")) {
				sql += (" and a.ACTIVECOSTCENTER='" + cc + "'");
			}
			if (!prodCode.equals("")) {
				sql += (" and a.FACTORA='" + prodCode + "'");
			}
			sql += " order by a.FACTORB";

			Map[] datas = (Map[]) dataDao.queryVOs(sql);
			for (int i = 0; i < datas.length; i++) {
				Map data = datas[i];

				String alloyNo = zafctool.trim(data.get("FACTORB"));
				// String dpd = zafctool.trim(data.get("DETAILPD"));
				//
				// String name = "";
				// if (dpd.length() >= 3) {
				// String code = dpd.substring(0, 3) + "_" + alloyNo;
				// name = zafctool.trim(map.get(code));
				// }

				// dejcTagRemoteOption option = new dejcTagRemoteOption(alloyNo,
				// name);
				dejcTagRemoteOption option = new dejcTagRemoteOption(alloyNo, alloyNo);
				if (option.getValue().equals(selected)) {
					option.setSelected(true);
				}
				list.add(option);
			}
		} catch (Exception e) {
			list.add(new dejcTagRemoteOption("", e.getMessage()));
		}

		return list;
	}

	public List findFieldValue(dsjccom dsCom, String key, Map m, String myDatas) {
		return null;
	}

	public static Map[] getTradeNos(dsjccom dsCom, String prodCode) throws Exception {
		zafcCommonDAO comDAO = new zafcCommonDAO(dsCom);

		StringBuffer sb = new StringBuffer();
		sb.append(" select distinct FACTORB TRADENO from db.tbadpdfactor where compId='" + dsCom.companyId + "'");
		sb.append(" and factorA='" + prodCode + "' and FACTORB<>'' order by FACTORB");

		return comDAO.queryVOs(sb.toString());
	}

}
