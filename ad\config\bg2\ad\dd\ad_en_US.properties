ACCTITEM=Acct. Item Code
ACCTDATE=Accounting Date
COMPID=Company ID.
ORDER=Costing No.
DPD=Item No.
FUNC=Function
QUERY=Query
MSG=Message
UPDATE=Update
INSERT=Insert
DELETE=Delete
BATCHNO=Batch No.
WORKORDER=Order No.
INVWGT=Inventory Weight
LOCNO=Location No.
SALESORDER=Sales Order
SOROWNO=Sales Order Row No.
CC=Cost Center
UPDATEDATE=Updated Date
UPDATEID=Updated User ID.
SELECTONE=Please Select At Least One Row
PRICETYPE=Price Type
BELONG=Work Area
PD=Production Code
TRADENO=Steel Grade
QUERY2CONDITION=Query Condition
QUERY2RESULT=Query Result
RENEW=Renew
SET=Uniform Settings
NODATA=Data No Found
QUERYOK=Query Success
MONTH=Month
UNIT=Unit
PRICE=Price
CHECKNUM=Std. Price Must Be Numeric.
CONFIRM=Confirm to Update Price?
CHECKD=Confirm to Delete?
CHECKN=Confirm to Create?
EXPENSETYPE=EXPENSETYPE
LOGIC=LOGIC
LOGICSET=LOGICSET
LINESETNOTFOUND=Prod. Line Data not found !
CHECKR=CHECKR
CHECKU=Confirm to Update?
KEYCHECK=Key Value Can not Be Modified!
SETNODE=Please set node:
ADDISTCHK1=Subtotal
ADDISTCHK2=Has fee but not find datas!!Please check setting!!
ADDISTCHK3=Share not Complete,Please check setting!!
ADDISTCHK4=Production costCenter share result table
ADDISTCHK5=Production costCenter
ADDISTCHK6=Total Fee
ADDISTCHK7=Share Wce
ADDISTCHK8=Share Costing No.
ADDISTCHK9=Share Amt
ADDISTCHK10=Diff
ADDISTCHK11=Memo
PRODMILL=Prod. Mill
CRNTMILL=Crnt. Mill
INVQTY=Inv Qty
INVUNIT=Inv Unit
INNERUSECC=Inner Use CC
STDAMT=Std. Amt
MTRLEXP=Mtrl. Exp.
LABOREXP=Labor Exp.
MFGFIXEXP=Mfg. Exp. Fix
MFGVAREXP=Mfg. Exp. Var
TOTALAMT=Total Amt
AASTDDOC=Std. Voucher
AADOC=Var. Voucher
PROCESS=Process
ITEMNO=Item No.
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcmccVO.compId=Accounting company
adjcmccVO.dataDate=Data date
adjcmccVO.orderNo=Work order number
adjcmccVO.batchNo=Batch number
adjcmccVO.invWgt=Inventory weight
adjcmccVO.invUnit=Inventory weight metering unit
adjcmccVO.locNo=Library
adjcmccVO.costCenter=Cost center
adjcmccVO.dpd=Fine production of substandard goods code
adjcmccVO.stdAmt=The standard amount.
adjcmccVO.mtrlExp=Direct materials
adjcmccVO.laborExp=Direct labor
adjcmccVO.mfgFixExp=Fixed manufacturing overhead
adjcmccVO.mfgVarExp=Variable manufacturing costs
adjcmccVO.totalAmt=The total amount of
adjcmccVO.createId=Establishment of human
adjcmccVO.lastUpdtId=Last Upd. User
adjcmccVO.lastUpdtDate=Last Upd. Date
adjcCIMIVO.compId=Accounting company
adjcCIMIVO.acctItem=Account code
adjcCIMIVO.dataDate=Billing date
adjcCIMIVO.dpd=Fine production of substandard goods code
adjcCIMIVO.batchNo=Batch number
adjcCIMIVO.invWgt=Inventory weight
adjcCIMIVO.invRefWgt=Inventory (note)
adjcCIMIVO.stdAmt=Planned amount
adjcCIMIVO.mtrlExp=Direct materials
adjcCIMIVO.laborExp=Direct labor
adjcCIMIVO.mfgFixExp=Fixed manufacturing overhead
adjcCIMIVO.mfgVarExp=Variable manufacturing costs
adjcCIMIVO.totalAmt=The total amount of
adjcCIMIVO.createId=Establishment of human
adjcCIMIVO.lastUpdtId=Last Upd. User
adjcCIMIVO.lastUpdtDate=Last Upd. Date
adjcpbVO.compId=Company
adjcpbVO.dataDate=Billing date
adjcpbVO.costCenter=Cost center
adjcpbVO.dpdOut=Production of substandard goods code
adjcpbVO.dpdIn=Input output and substandard goods code
adjcpbVO.inWce=Cost accounts
adjcpbVO.inQty=Input
adjcpbVO.createEmp=Founder
adjcpbVO.createDate=Creation date
adjcpbVO.updateEmp=Modifier
adjcpbVO.updateDate=The modification date
adjctcVO.compId=Company
adjctcVO.dataDate=The settlement month / date
adjctcVO.costCenter=Cost center
adjctcVO.expenseType=Cost fees classification
adjctcVO.expenseTypeDesc=Cost classification description
adjctcVO.logic=Allocation logic
adjctcVO.updateUserId=Changes of people
adjctcVO.updateDate=Change the date
adjcProfitVO.compId=Company code
adjcProfitVO.dataDate=Date
adjcProfitVO.seq=Flow number
adjcProfitVO.rptId=The type of report
adjcProfitVO.sysId=The source system
adjcProfitVO.custNo=The customer number
adjcProfitVO.workArea=Product attribution
adjcProfitVO.pdClassify=Production of substandard goods categories
adjcProfitVO.detailPD=Fine product code
adjcProfitVO.acctItem=Account code
adjcProfitVO.unit=Units of measurement
adjcProfitVO.saleQty=Sales volume
adjcProfitVO.costQty=The number of cost
adjcProfitVO.saleAmt=Sales revenue
adjcProfitVO.costAmt=Cost of sales
adjcProfitVO.statusCode=State
adjcProfitVO.refNoA=Reservation reference number
adjcProfitVO.refNoB=Reservation reference number
adjcProfitVO.refNoC=Reservation reference number
adjcProfitVO.crtUser=The establishment of personnel
adjcProfitVO.crtTime=Creation date
adjcProfitVO.updUser=Modify staff
adjcProfitVO.updTime=Creation date
adjcDayMccVO.compId=Company
adjcDayMccVO.dataDate=Date
adjcDayMccVO.manageMent=Management unit
adjcDayMccVO.locNo=Library
adjcDayMccVO.recordSerialNo=Serial number
adjcDayMccVO.costCenter=Cost center
adjcDayMccVO.detailProdCode=Fine product code
adjcDayMccVO.acctItem=Account code
adjcDayMccVO.qty=Number
adjcDayMccVO.toManageMent=Benefit management unit
adjcDayMccVO.toCC=Contract number
adjcDayMccVO.updateUserId=Last Upd. User
adjcDayMccVO.updateDate=Last Upd. Date
adjcDayMccVO.createUserId=Establishment of human
adjcDayMccVO.createDate=Creation date
adjcDayMccVO.statusCode=State
adjcpdTimeAttriVO.compId=Company
adjcpdTimeAttriVO.detailPD=Fine class code
adjcpdTimeAttriVO.dataDate=Data date
adjcpdTimeAttriVO.attriName=Account code attribute name
adjcpdTimeAttriVO.attriValue=Account code attribute value
adjcpdTimeAttriVO.intValue=Account code attribute value
adjcpdTimeAttriVO.factorA=Factor A
adjcpdTimeAttriVO.factorB=Factor B
adjcpdTimeAttriVO.factorC=Factor C
adjcpdTimeAttriVO.createId=Establishment of human
adjcpdTimeAttriVO.createDate=Creation date
adjcpdTimeAttriVO.lastUpdtId=Last Upd. User
adjcpdTimeAttriVO.lastUpdtDate=Last Upd. Date
adjcpdFactorVO.compId=Company
adjcpdFactorVO.prodCode=Product code
adjcpdFactorVO.factorA=Factor A
adjcpdFactorVO.factorB=Factor B
adjcpdFactorVO.factorC=Factor C
adjcpdFactorVO.factorD=Factor D
adjcpdFactorVO.factorE=Factor E
adjcpdFactorVO.factorF=Factor F
adjcpdFactorVO.factorG=Factor G
adjcpdFactorVO.dpd=Fine production of substandard goods code
adjcpdFactorVO.dpdDesc=Fine production of substandard goods name
adjcpdFactorVO.createId=Establishment of human
adjcpdFactorVO.createDate=Creation date
adjcpdFactorVO.lastUpdtId=Last Upd. User
adjcpdFactorVO.lastUpdtDate=Last Upd. Date
adjctdVO.compId=Company
adjctdVO.dataDate=The settlement month / date
adjctdVO.costCenter=Cost center
adjctdVO.toCC=The costs of a cost center
adjctdVO.expenseType=Cost fees classification
adjctdVO.detailProdCode=Fine product code
adjctdVO.toDetailPD=Different node transfer record
adjctdVO.serviceQunantities=Allocation reference quantity
adjctdVO.shareFactor=Adjust the weights
adjctdVO.contributorytPercent=Distribution rate
adjctdVO.contributorytValue=Apportioned amount
adjctdVO.contributorytQty=Sharing differences
adjctdVO.updateUserId=Changes of people
adjctdVO.updateDate=Change the date
adjcDpdChgVO.compId=Company code
adjcDpdChgVO.dataDate=Years
adjcDpdChgVO.fromCC=Before the change cost center
adjcDpdChgVO.fromDpd=Before the change of product code
adjcDpdChgVO.toCC=After the change of cost center
adjcDpdChgVO.toDpd=After the change of product code
adjcDpdChgVO.toQty=Different momentum
adjcDpdChgVO.toAmt=The daily cost of transaction
adjcDpdChgVO.toVar=Transaction end difference
adjcDpdChgVO.toTtQty=After the change of the amount of products
adjcDpdChgVO.toTtAmt=After the change of the total product cost
adjcDpdChgVO.toTtVar=After the change of product difference
adjcmscVO.compId=Accounting company
adjcmscVO.dataDate=Data date
adjcmscVO.salesOrder=Sales order number
adjcmscVO.soRowNo=Order line item number
adjcmscVO.batchNo=Batch number
adjcmscVO.invWgt=Inventory weight
adjcmscVO.invUnit=Inventory weight metering unit
adjcmscVO.locNo=Library
adjcmscVO.costCenter=Cost center
adjcmscVO.dpd=Fine production of substandard goods code
adjcmscVO.stdAmt=The standard amount.
adjcmscVO.mtrlExp=Direct materials
adjcmscVO.laborExp=Direct labor
adjcmscVO.mfgFixExp=Fixed manufacturing overhead
adjcmscVO.mfgVarExp=Variable manufacturing costs
adjcmscVO.totalAmt=The total amount of
adjcmscVO.createId=Establishment of human
adjcmscVO.lastUpdtId=Last Upd. User
adjcmscVO.lastUpdtDate=Last Upd. Date
adjcmlVO.compId=Accounting company
adjcmlVO.slipNo=Account number
adjcmlVO.acctItem=Account code
adjcmlVO.dataAttriIdx=Treatment of front / rear indicator
adjcmlVO.prodMill=Product production line
adjcmlVO.crntMill=The operation of production line
adjcmlVO.batchNo=Batch number
adjcmlVO.orderNo=Work order number
adjcmlVO.invWgt=Inventory weight
adjcmlVO.invRefWgt=Inventory (note)
adjcmlVO.invQty=The amount of inventory
adjcmlVO.invUnit=Inventory weight metering unit
adjcmlVO.locNo=Library
adjcmlVO.salesOrder=Sales order number
adjcmlVO.soRowNo=Order line item number
adjcmlVO.costCenter=Cost center
adjcmlVO.innerUseCC=Internal use cost center
adjcmlVO.dpd=Fine production of substandard goods code
adjcmlVO.dataDate=Billing date
adjcmlVO.stdAmt=Planned amount
adjcmlVO.mtrlExp=Direct materials
adjcmlVO.laborExp=Direct labor
adjcmlVO.mfgFixExp=Fixed manufacturing overhead
adjcmlVO.mfgVarExp=Variable manufacturing costs
adjcmlVO.totalAmt=The total amount of
adjcmlVO.aaStdDocument=The financial document number (plan)
adjcmlVO.acStdDocument=Cost voucher number (plan)
adjcmlVO.aaDocument=The financial document number (difference)
adjcmlVO.acDocument=Cost voucher number (difference)
adjcmlVO.process=Process
adjcmlVO.createId=Establishment of human
adjcmlVO.lastUpdtId=Last Upd. User
adjcmlVO.lastUpdtDate=Last Upd. Date
adjcPre43Price.1=Please input correct data date!
adjcPre43Price.2=Please input the price index parameter!
adjcPre43Price.3=Please input the target acctitem!
adjcPre43Price.4=Please input price source acctitems!
adjcPre43Price.5=Updated price records count:
adjcPre43Price.6=Source acctitem data not found!  