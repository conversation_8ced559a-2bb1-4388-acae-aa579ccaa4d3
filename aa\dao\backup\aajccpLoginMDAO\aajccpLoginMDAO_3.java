/*----------------------------------------------------------------------------*/
/* aajccpLoginMDAO		DAOTool Ver 10.0112 (INPUT FILE VERSION:2.0)
/*----------------------------------------------------------------------------*/
/* author : InfoChamp
/* system : 普通會計管理系統(AA)
/* target : 用戶授權帳套別及廠別維護作業基本資料文件
/* create : 104/08/25
/* update : 104/08/25
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao;

import java.sql.*;
import java.text.*;
import java.util.*;
import java.math.* ;
import com.icsc.dpms.de.*;
import com.icsc.dpms.de.sql.*;
import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;

/**
 * 用戶授權帳套別及廠別維護作業基本資料文件 DAO.
 * <pre>
 * Table Name        : DB.TBAACPLOGINM
 * Table Description : 用戶授權帳套別及廠別維護作業基本資料文件
 * Value Object Name : aajccpLoginMVO
 * </pre>
 * @version $Id: aajccpLoginMDAO_3.java,v 1.1 2015/09/30 05:13:30 I27368 Exp $
 * @since aajccpLoginMVO - 104/08/25
 * <AUTHOR>
 */
public class aajccpLoginMDAO extends dejcCommonDAO {
	public final static String AppId = "AAJCCPLOGINMDAO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:30 $";

/*----------------------------------------------------------------------------*/
/* Creates new aajccpLoginMDAO
/*----------------------------------------------------------------------------*/

	/**
	 * 建構子
	 * @param dsCom - 共用元件
	 * @since 104/08/25
	 */
	public aajccpLoginMDAO(dsjccom dsCom) {
		super(dsCom, AppId);
	}
	/**
	 * 建構子
	 * @param dsCom - 共用元件
	 * @param con - 交易連接緒
	 * @since 104/08/25
	 */
	public aajccpLoginMDAO(dsjccom dsCom, Connection con) {
		super(dsCom, con) ;
		super.appId = this.AppId;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/
	/**
	 * 組合以主鍵查詢的 sql
	 * @since 104/08/25
	 */
	private String getFindByPKSql(String userId) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGINM");
		sqlStr.append(" WHERE  userId='"+userId+"' ");
		return sqlStr.toString();
	}
	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @return aacpLoginMVO - 單筆資料
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPK(String userId)
			throws SQLException, Exception {
		this.sql = getFindByPKSql(userId) ;
		return (aajccpLoginMVO) this.eQuery(this.sql) ;
	}

	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @return aacpLoginMVO - 單筆資料
	 * @exception dejcNotFoundException - 若查不到資料時
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPKExp(String userId)
			throws dejcNotFoundException, SQLException, Exception {
		aajccpLoginMVO aacpLoginMVO = findByPK(userId) ;
		if (aacpLoginMVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginMVO ;
	}

	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @param aacpLoginMVO - 由使用者所提供的 object, DAO 不另行 new
	 * @return aacpLoginMVO - 單筆資料
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPK(aajccpLoginMVO aacpLoginMVO, String userId)
			throws SQLException,Exception {
		this.sql = getFindByPKSql(userId) ;
		return (aajccpLoginMVO) this.eQuery(aacpLoginMVO, this.sql) ;
	}

	/**
	 * 以主鍵來查詢資料
	 * <p>
	 * @param aacpLoginMVO - 由使用者所提供的 object, DAO 不另行 new
	 * @return aacpLoginMVO - 單筆資料
	 * @exception dejcNotFoundException - 若查不到資料時
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPKExp(aajccpLoginMVO aacpLoginMVO, String userId)
			throws dejcNotFoundException, SQLException, Exception {
		aacpLoginMVO = findByPK(aacpLoginMVO, userId) ;
		if (aacpLoginMVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginMVO ;
	}
	/**
	 * 新增一筆資料 throws
	 * <p>
	 * @param aacpLoginMVO - Value Object
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料新增
	 * @exception dejcDupException - 重複鍵值
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int create(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		if (aacpLoginMVO.verify() == false) {
			throw new dejcEditException(aacpLoginMVO.getMessage()) ;
		}
		if (aacpLoginMVO.isKeyOk() == false) {
			throw new dejcEditException("Value of key["+AppId+"].["+aacpLoginMVO.getMessage()+"] is null or empty!") ;
		}

		
		this.sql = getCreateSql(aacpLoginMVO) ;

		try {
			int rslt = this.executeUpdate(this.sql) ;

			if (rslt == 0) {
				throw new dejcNoUpdateException(this.sql) ;
			}
			return rslt;
		} catch (SQLException sqle) {
            handleDupException(sqle);
            return -1;
		}
	}
	
	public String getCreateSql(aajccpLoginMVO aacpLoginMVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGINM (");
		sqlStr.append("userId,lastCompId");
		sqlStr.append(") VALUES (");
		sqlStr.append("'").append(aacpLoginMVO.getUserIdS()).append("','").append(aacpLoginMVO.getLastCompIdS()).append("'");
		sqlStr.append(")");	
		this.sql = sqlStr.toString() ;
		return this.sql;
	}

	/**
	 * 新增多筆資料 throws
	 * <p>
	 * @param aacpLoginMVOList - Value Object
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料新增
	 * @exception dejcDupException - 重複鍵值
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int createList(List aacpLoginMVOList)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			count = count+this.create(aacpLoginMVO) ;
		}
		return count ;
	}

  	/**
	 * 刪除資料
	 * <p>
	 * @return int - 交易筆數
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int remove(String userId)
			throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGINM");
		sqlStr.append(" WHERE  userId='"+userId+"' " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	

	/**
	 * 刪除資料 throws
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int removeExp(String userId)
			throws dejcNoUpdateException, SQLException, Exception {
		int rslt = remove(userId) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(this.sql) ;
		}
		return rslt ;
	}

	/**
	 * 刪除資料 throws
	 * <p>
	 * @param aacpLoginMVO 欲刪除的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int removeExp(aajccpLoginMVO aacpLoginMVO)
			throws dejcNoUpdateException, SQLException, Exception {
		return removeExp(aacpLoginMVO.getUserId()) ;
	}

	/**
	 * 刪除多筆資料 throws
	 * <p>
	 * @param aacpLoginMVOList 欲刪除的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int removeList(List aacpLoginMVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			count = count+this.remove(aacpLoginMVO.getUserId()) ;
		}
		return count ;
	}

	/**
	 * 刪除資料 throws
	 * <p>
	 * @param aacpLoginMVO 欲刪除的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被刪除
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int remove(aajccpLoginMVO aacpLoginMVO)
			throws SQLException, Exception {
		return remove(aacpLoginMVO.getUserId()) ;
	}

	/**
	 * 修改資料
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int update(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginMVO.verify() == false) {
			throw new dejcEditException(aacpLoginMVO.getMessage()) ;
		}
		if(aacpLoginMVO.hasEditFields()) {
			return updateEditFields(aacpLoginMVO);
		} else {
			return this.executeUpdate(getUpdateSql(aacpLoginMVO) ) ;
		}
	}

	public String getUpdateSql(aajccpLoginMVO aacpLoginMVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGINM");
		sqlStr.append(" SET lastCompId='").append(aacpLoginMVO.getLastCompIdS()).append("' ");
		sqlStr.append(" WHERE  userId='"+aacpLoginMVO.getUserId()+"' ");
		this.sql = sqlStr.toString();
		return this.sql ;	
	}
	
	/**
	 * 若資料與預設值不同，才要修改該欄位
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int updateFldsNotDef(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginMVO.verify() == false) {
			throw new dejcEditException(aacpLoginMVO.getMessage()) ;
		}
		if (aacpLoginMVO.isKeyOk() == false) {
			throw new dejcEditException("primary key["+aacpLoginMVO.getMessage()+"] of ["+AppId+"] is empty!") ;
		}		
		StringBuffer updateFlds = new StringBuffer();
		if (aacpLoginMVO.getLastCompId() !=null  && !aacpLoginMVO.getLastCompId().equals("") ) {
			updateFlds.append("lastCompId='").append( aacpLoginMVO.getLastCompIdS() ).append("'").append(',') ;
		} 
		if(updateFlds.length()==0) {
			return 0;
		}	
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGINM");
		sqlStr.append(" SET ").append( updateFlds.substring(0, updateFlds.length()-1) );
		sqlStr.append(" WHERE  userId='"+aacpLoginMVO.getUserId()+"' ");
		this.sql = sqlStr.toString();
		return this.executeUpdate(this.sql) ;
	}


	/**
	 * 修改資料 throws
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料修改
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int updateExp(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int rslt = update(aacpLoginMVO) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(sql) ;
		}
		return rslt ;
	}

	/**
	 * 修改-新增多筆資料, 如果修改 0 筆的話，即新增該筆資料
	 * <p>
	 * @return int - 交易筆數
	 * @exception dejcEditException - Value Object 資料有誤
	 * @exception dejcNoUpdateException - 無任何資料修改
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int updateCreateList(List aacpLoginMVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			int updateCount = update(aacpLoginMVO) ;
			if ( updateCount==0 ) {
				count += create(aacpLoginMVO) ;
			} else {
				count += updateCount ;
			}
		}
		return count ;
	}

	/**
	 * 修改多筆資料 throws
	 * <p>
	 * @param aacpLoginMVOList 欲修改的資料物件
	 * @return int - 交易筆數
	 * @exception dejcNoUpdateException - 無任何資料被修改
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	public int updateList(List aacpLoginMVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			count = count+this.updateExp(aacpLoginMVO) ;
		}
		return count ;
	}

	/**
	 * 執行 addCreateBatch(Object obj) 時需要用到的 sql
	 * 新增資料的 prepareStatement sql<br>
	 * 此方法是覆寫 commonDAO 的方法
	 */
    protected String getCreatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGINM  (");
		sqlStr.append("userId,lastCompId");
		sqlStr.append(") VALUES (");
		sqlStr.append("?,?");
		sqlStr.append(")");		    
		return sqlStr.toString();
    }

	/**
	 * 執行 addupdateBatch(Object obj) 時需要用到的 sql
	 * 修改資料的 prepareStatement sql<br>
	 * 此方法是覆寫 commonDAO 的方法
	 */
    protected String getUpdatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGINM");
		sqlStr.append(" SET lastCompId=? ");
		sqlStr.append(" WHERE  userId=? ");
		return sqlStr.toString();
    }

	/**
	 * 執行 addDeleteBatch(Object obj) 時需要用到的 sql
	 * 刪除資料的 prepareStatement sql<br>
	 * 此方法是覆寫 commonDAO 的方法
	 */
    protected String getDeletePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGINM");
		sqlStr.append(" WHERE  userId=? " );
		return sqlStr.toString();
    }

	/**
	 * 執行 addCreateBatch(Object obj) 時需要呼叫的方法
	 */
    protected void prepareCreate(Object obj) throws SQLException {
    	aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
		pstmt.setString(1, aacpLoginMVO.getUserId()) ;
		pstmt.setString(2, aacpLoginMVO.getLastCompId()) ;
    }

	/**
	 * 執行 addUpdateBatch(Object obj) 時需要呼叫的方法
	 */
    protected void prepareUpdate(Object obj) throws SQLException {
    	aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
		pstmt.setString(1, aacpLoginMVO.getLastCompId()) ;
		pstmt.setString(2, aacpLoginMVO.getUserId()) ;
    }

	/**
	 * 執行 addDeleteBatch(Object obj) 時需要呼叫的方法
	 */
    protected void prepareDelete(Object obj) throws SQLException {
    	aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
		pstmt.setString(1, aacpLoginMVO.getUserId()) ;
    }

	/**
	 * 實作父類別
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	protected Object getObjFromRS(ResultSet rs) throws SQLException,Exception {
		return aajccpLoginMVO.getInstanceByName(rs) ;
	}

	/**
	 * 實作父類別, 使用 user 傳進來的 object, 不另行 new , 以節省執行時間及記憶體<br>
	 * 適合臨時性的資料運算(通常在運算大量資料時，一些欄位需要暫存在 value object)<br>
	 * 此方法只會在執行 findByPK(Object obj,String userId) 時才被呼叫。
	 * <p>
	 * @exception SQLException - 資料庫錯誤
	 * @since 104/08/25
	 */
	protected Object getObjFromRS(Object obj, ResultSet rs) throws SQLException,Exception {
		return aajccpLoginMVO.getInstanceByName(obj, rs) ;
	}
	
	/**
	  * 備份方法 2009.03.26 新增
	  */
	public int backup(String tableName, aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		this.sql = getCreateSql(aacpLoginMVO).replaceFirst(" DB.TBAACPLOGINM "," "+tableName+" "); 
		return this.executeUpdate(this.sql) ;
	} 
	public int backup(String tableName, ResultSet rs) throws SQLException, Exception {
		return backup(tableName, aajccpLoginMVO.getInstanceByName(rs) ) ;
	} 

	public String getUpdateEditFieldsPartialSql(aajccpLoginMVO aacpLoginMVO)  {
		if( aacpLoginMVO.getEditFields()==null ) {
			return "";
		}
		return dejcSqlUtils.genUpdateFields(  aacpLoginMVO.getEditFields() );		
	}
	/**
	  * 修改有動過的欄位，此方法要有效請確保有先執行  aajccpLoginMVO.monitor() 
	  */
	public String getUpdateEditFieldsSql(aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		String updSql =getUpdateEditFieldsPartialSql(aacpLoginMVO) ;
		if(updSql.equals("")){
			return "";
		}
		return "update DB.TBAACPLOGINM set "+updSql+" where   userId='"+aacpLoginMVO.getUserId()+"' ";
	}
	/**
	  * 修改有動過的欄位，此方法要有效請確保有先執行  aajccpLoginMVO.monitor() 
	  */
	public int updateEditFields(aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		this.sql=getUpdateEditFieldsSql(aacpLoginMVO);
		if(this.sql.equals("")){
			// 2010.03.02 從0改為-1，以識別是否有執行
			return -1;
		}
		return this.executeUpdate(getUpdateEditFieldsSql(aacpLoginMVO)) ;
	}
	/**
	 * 假update的sql，找最後一個欄位來假update
	 */	
	private String getLockSqlPrefix() {
		return "update DB.TBAACPLOGINM set lastCompId=lastCompId ";
	}

	/**
	 * 查詢、鎖定、監控資料
	 */	
	public aajccpLoginMVO loadByPK4Update(String userId) throws SQLException, Exception {
		List aList = loadList4Update("WHERE   userId='"+userId+"' ");
		if(aList.size()>0) {
			return (aajccpLoginMVO)aList.get(0);
		}else {
			throw new dejcNotFoundException(this.sql) ;	
		}	
	}
	
	private String getSelectSqlPrefix() {
		return "select * from DB.TBAACPLOGINM ";
	}
	
	private List loadList4Update(String conditionSql) throws SQLException, Exception {
		this.executeUpdate(getLockSqlPrefix()+conditionSql) ;
		this.sql=getSelectSqlPrefix()+conditionSql ;
		return monitor(this.eQueryAll( this.sql ));
	}
	/**
	  *監控資料
	  */
	private List monitor(List aacpLoginMVOList){
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			aacpLoginMVO.monitor();
		}	
		return aacpLoginMVOList;
	}
	/**
	 * 以 VO 撈出 DB 資料，撈資料同時會鎖定
	 */
	public aajccpLoginMVO loadByVO(aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		return loadByPK4Update(aacpLoginMVO.getUserId());
	}
	/**
	 * 以 VO List 撈出 DB 資料，撈資料同時會鎖定
	 */
	public List loadByList(List aacpLoginMVOList) throws SQLException, Exception {
		List rsltList= new ArrayList(aacpLoginMVOList.size());
		for (Iterator iterator = aacpLoginMVOList.iterator(); iterator.hasNext();) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO) iterator.next();
			rsltList.add( loadByVO(aacpLoginMVO) ) ;
		}
		return rsltList;
	}
	/**
	 * 以動態查詢條件查詢
	 */
	public Vector findByCriteria(dejcCriteria criteria)throws dejcNotFoundException, SQLException, Exception{
		return findByCriteria(criteria,"");
	}
	/**
	 * 以動態查詢條件查詢
	 */
	public Vector findByCriteria(dejcCriteria criteria,String orderby)throws dejcNotFoundException, SQLException, Exception{
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append(getSelectSqlPrefix());
		if(!dejcUtility.isNull(orderby)) {
			orderby="order by "+orderby;
		}
		sqlStr.append(criteria.toSqlWithWhereIfExists()).append(orderby);  
		this.sql = sqlStr.toString();
		return this.eQueryAll(this.sql) ;
	}
	
//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

    public List findAll() throws SQLException, Exception{
    	this.sql = "select * from DB.TBAACPLOGINM ";
    	return this.eQueryAll(this.sql);
    }


} // end of Class aajccpLoginMDAO