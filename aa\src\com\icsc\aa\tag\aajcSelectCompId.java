package com.icsc.aa.tag;

import com.icsc.aa.dei.aajcCommondei;
import com.icsc.aa.util.aajcDeUtil;
import com.icsc.dpms.de.tag.dejcSelectDataSrc;
import com.icsc.dpms.ds.dsjccom;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class aajcSelectCompId extends dejcSelectDataSrc {
	public final static String CLASS_VERSION = "$Id: aajcSelectCompId.java,v 1.7 2017/07/13 11:05:04 I21312 Exp $";
	public void setDataSrc(dsjccom arg0, String arg1) {
		aajcCommondei commondei = new aajcCommondei(arg0);
		TreeMap companies = commondei.getCompSelectionUserCanView(arg0.companyId,true);

		for (Object object : companies.entrySet()) {
			Map.Entry<String, String> entry =(Map.Entry<String, String>) object;
			String key = entry.getKey();
			String value = entry.getValue();
			String name = aajcDeUtil.replace(value, "$", "", 0);
			
			addOption(key, name);
		}
	}

}
