function deHiddenList(selectName) {
	this.values=new Array()
	this.selectName=selectName
	this.father=null 
	this.add=function(value,text) {
		this.values[this.values.length]=new deHiddenItem(value,text) 		
	}	
	this.draw=function(orderNo,group) {
		var html="<select name='"+this.selectName+orderNo+"' onblur=aajth101_recover()>" 
		var selected=""
//		var v=tr(document.getElementById(this.selectName+orderNo).innerText)
		var v=aajth101_getSelectText(this.selectName+orderNo) ;
		for(var i=0;i<this.values.length;i++) {
			if (v==this.values[i].text)
				selected="selected"
			else
				selected=""
			html+="<option value=\""+this.values[i].value+"\" "+selected+" >"+this.values[i].text ;
		}
		html+="</select>"
		return html ;
	}
	this.setFather=function(fath) {
		this.father=fath ;
	}
}
function deHiddenItem(value,text) {
	this.value=value
	this.text=text
}

function deListGroup() {
	this.selectGroup=new Array()
	this.add=function(hiddenList) {
		this.selectGroup[this.selectGroup.length]=hiddenList
		hiddenList.setFather(this) ;
	}
	this.targets=new Array() ;
	
	this.change=function (orderNo) {		
		for(var i=0;i<this.targets.length;i++) {
			var tar=document.getElementById(this.targets[i]+orderNo) 
			if (event.srcElement.checked)
				tar.innerHTML=this.selectGroup[i].draw(orderNo) ;
			else
				tar.innerHTML="<input type='text' style='background:transparent;border:0 none;font-size:12pt;text-align:center' size=8 readonly name='"+this.selectName+orderNo+"' value='"+tar.children[0].options[tar.children[0].selectedIndex].text+"'>" ;
		}
	}
	this.idvChange=function (orderNo) {
		var father=event.srcElement.parentElement ;
		if (event.srcElement.tagName.toLowerCase()=="input") {
			for(var i=0;i<this.selectGroup.length;i++) {
				if (this.selectGroup[i].selectName==aajth101_getName(event.srcElement.parentElement.id)) {				
					event.srcElement.outerHTML=this.selectGroup[i].draw(orderNo,this) ;					
					for(var i=0;i<father.children.length;i++) {
						if (father.children[i].tagName.toLowerCase()=="select") {
							father.children[i].focus() ;
							return ;
						}	
					}
					
				}	
			}	
		}
	}
	this.end=function() {
		for(var i=0;i<this.selectGroup.length;i++) {
			this.targets[this.targets.length]=this.selectGroup[i].selectName ;
		}
	}
}

function aajth101_getName(name) {
	var len=name.length ;
	for(var i=len;i>=0;i--) {
		if (isNaN(name.substr(i,1))) {
			return name.substring(0,i+1) ;
		}
	}	
}
function aajth101_getOrderNo(name) {
	var len=name.length ;
	for(var i=len;i>=0;i--) {
		if (isNaN(name.substr(i,1))) { //alert(parseInt(name.substring(i+1))) ;
			return parseInt(name.substring(i+1)) ;
		}
	}	
}

function aajth101_recover() {
	var obj=event.srcElement ;
	obj.outerHTML="<input type='text' class='transparent-field' size=8 readonly name='name"+obj.parentElement.id+"' value='"+obj.options[obj.selectedIndex].text+"' onfocus=listGroup.idvChange("+aajth101_getOrderNo(obj.parentElement.id)+")>" +
				  "<input type='hidden' class='transparent-field' size=8 readonly name='"+obj.parentElement.id+"' value='"+obj.options[obj.selectedIndex].value+"' onfocus=listGroup.idvChange("+aajth101_getOrderNo(obj.parentElement.id)+")>" ;
}

function aajth101_getSelectText(id) {
	var obj=document.getElementsByName("name"+id)	 ;
	for(var i=0;i<obj.length;i++) {
		if (obj[i].tagName.toLowerCase()=="input") {
			if (obj[i].type) {
				if (obj[i].type.toLowerCase()=="text")
					return obj[i].value ;
			}
		}
	}	
}