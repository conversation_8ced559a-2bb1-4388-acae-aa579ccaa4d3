<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%! public static String _AppId = "ACJJDPDWCE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = request.getParameter("compId") == null ? "" :request.getParameter("compId");
	String workArea = request.getParameter("WA") == null ? "" :request.getParameter("WA");
	String prodCode = request.getParameter("PD") == null ? "" :request.getParameter("PD");
%>

<textarea name="CHILDSELECT" style="display:none">

	<%--<select name="PRODCODE0" onchange="showTN(this.value)" >--%>
	<select name="PRODCODE0" >
		<%=ipjcWorkArea.getPDOptions(_dsCom, compId, workArea, prodCode)%>
	</select>
</textarea>

<script>
	if(parent){
		parent.document.all("prodCode").innerHTML = document.all("CHILDSELECT").value;
	}
</script>

