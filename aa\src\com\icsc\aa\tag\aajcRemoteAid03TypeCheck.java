package com.icsc.aa.tag;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.icsc.aa.dao.aajc20DAO;
import com.icsc.aa.dao.aajc20VO;
import com.icsc.aa.dao.aajcFactoryDAO;
import com.icsc.aa.dao.aajcFactoryVO;
import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.de.dejcUtility;
import com.icsc.dpms.de.tag.dejcTagRemoteField;
import com.icsc.dpms.de.tag.dejcTagRemoteOption;
import com.icsc.dpms.de.tag.dejiWebDataFinder;
import com.icsc.dpms.ds.dsjccom;

public class aajcRemoteAid03TypeCheck implements dejiWebDataFinder {

	public List findFieldValue(dsjccom dsCom, String key, Map params, String rmRtnFlds) {
		return null;
	}
	
	public List findOptions(dsjccom dsCom, String key, String selected,Map params) {
		List OptionList = new ArrayList() ;
		
		String compId = params.get("compId_v1").toString();
		String TypeCode = params.get("aid03TypeDesc_v1").toString();
        
        aajc20DAO aa20DAO = new aajc20DAO(dsCom);
        try {
            aajc20VO aa20VO = aa20DAO.findByPK(compId, TypeCode) == null ? new aajc20VO() : aa20DAO.findByPK(compId, TypeCode);

            dejcTagRemoteOption option = new dejcTagRemoteOption(aa20VO.getRuleType(), aa20VO.getDescription());
            option.setSelected(true);
            option.setHideValue(true);
			OptionList.add(option);
        } catch (Exception e) {
            e.printStackTrace();
        }
		
		return OptionList;
	}

}
