package com.icsc.ap.link;

import com.icsc.ap.dao.apjcDeptFeeTypeDAO;
import com.icsc.ap.dao.apjcDeptFeeTypeVO;
import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.ds.dsjccom;

public class apjcLinkDEPT {

    String AppId;

    dsjccom dsCom = null;

    private dejc318 de318;
    
    public apjcLinkDEPT(dsjccom dsCom) {
        this.dsCom = dsCom;
        this.AppId = "apjcLinkDEPT".toUpperCase();
        de318 = new dejc318(dsCom,AppId);
    }
    
    public String getDeptFeeType(String compId, String deptNo) {
        de318.logs("compId---costCenter:", compId + "---" + deptNo);
        
        apjcDeptFeeTypeDAO apDeptFeeTypeDAO=new apjcDeptFeeTypeDAO(this.dsCom);
        apjcDeptFeeTypeVO apDeptFeeTypeVO = null;
        try {
            apDeptFeeTypeVO = apDeptFeeTypeDAO.findByPK(compId, deptNo);
            
        }  catch (Exception e) {
            de318.logs("getDeptFeeType", "apDeptFeeTypeDAO.findByPK", e);
            e.printStackTrace();
        }

        return apDeptFeeTypeVO==null ? "" : apDeptFeeTypeVO.getDeptTypeS();
    }

}
