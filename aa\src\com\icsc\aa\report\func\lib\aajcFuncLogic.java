/**
 * 
 */
package com.icsc.aa.report.func.lib;

import java.math.BigDecimal;

import com.icsc.aa.report.aajcReportContext;
import com.icsc.aa.report.func.aajcFuntionCommon;

/**
 * <AUTHOR>
 *
 */
public class aajc<PERSON>unc<PERSON>og<PERSON> extends aajcFuntionCommon {

	/**
	 * @param aaReportContext
	 */
	public aajcFuncLogic(aajcReportContext aaReportContext) {
		super(aaReportContext);
	}

	public Object IF(Boolean condition,BigDecimal arg1,BigDecimal arg2){
		return condition.booleanValue() ? arg1 : arg2;
	}
	
	public Object IF(Boolean condition,String arg1,String arg2){
		return condition.booleanValue() ? arg1 : arg2;
	}
	
	public Object IF(Boolean condition,BigDecimal arg1,String arg2){
		return condition.booleanValue() ? arg1.toString() : arg2;
	}
	
	public Object IF(Boolean condition,String arg1,BigDecimal arg2){
		return condition.booleanValue() ? arg1 : arg2.toString();
	}
	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub

	}

}
