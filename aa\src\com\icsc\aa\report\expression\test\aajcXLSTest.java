/**
 * 
 */
package com.icsc.aa.report.expression.test;

import java.sql.Connection;
import java.sql.SQLException;

import junit.framework.TestCase;

import com.icsc.aa.report.gen.aajcReportGenerator;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dejc315;
import com.icsc.dpms.ds.dsjccom;

/**
 * <AUTHOR>
 * 
 */
public class aajcXLSTest extends TestCase {
	private dsjccom dscom;

	/**
	 * @param name
	 */
	public aajcXLSTest(String name) {
		super(name);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see junit.framework.TestCase#setUp()
	 */
	protected void setUp() throws Exception {
		super.setUp();

		dscom = new dsjccom();
        dscom.companyId = "bg";
        dscom.appId = "AA";
        dscom.linkType = 1;
        dscom.db.driver = "COM.ibm.db2.jdbc.app.DB2Driver";
        dscom.db.url = "jdbc:db2:dbbg";
        dscom.db.userId = "db2admin";
        dscom.db.passwd = "db2admin";
        dscom.db.linkFlag = true;
        dscom.user.ID = "ICSCAA";
        dscom.user.department = "FF00";
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see junit.framework.TestCase#tearDown()
	 */
	protected void tearDown() throws Exception {
		super.tearDown();
	}

	public void testXLS() {
		//File templateXLS = new File("D:\\erpHome\\bx.ear\\erp.war\\aa\\doc\\demo.xls");
//	    dejc301 de301 = new dejc301();
//		try {
//			//aajcReportGenerator aaReportGenerator = new aajcReportGenerator(this.dscom,"200801");
//			
//			//String rptFile = aaReportGenerator.render("01");
//			//System.out.println(ftjcValidate.isEmpExist(this.dscom,"8180179"));
//			//System.out.println(new dujc001(dscom).checkUserId("8180179"));
//			//System.out.println(new dujc001(dscom).checkUserId("ICSC01"));
//			//System.out.println("rptFile=" + rptFile);
//            
//            //System.out.println(",BKJLM".indexOf("B"));
//		    
//            //Connection con = de301.getConnection(dscom, "AP");
//           // de301.setAutoCommit(false);
//            
//            
//            dejc315 de315 = new dejc315();
//            String errCode = de315.computeDaysOfDiff("20140302", "20140305");
//            if (errCode.equals("*")){
//                throw new Exception("Error Code = " + errCode + de315.getErrMsg());
//            }
//            int diffDay = de315.getDaysOfDiff();
//            System.out.println(diffDay);
//            
//            de301.commit();
//        } catch (Exception e) {
//            e.printStackTrace();
//            try {
//                de301.rollback();
//            } catch (SQLException e1) {
//                e1.printStackTrace();
//            }
//        } finally {
//            de301.close();
//        }
        dejc315 de315 = new dejc315();
        String errCode = de315.computeDaysOfDiff("20140302", "20140305");

        int diffDay = de315.getDaysOfDiff();
        System.out.println(diffDay);
        System.out.println("20140302".substring(4,6));
        System.out.println("20140302".substring(6,8));
        
	}

}
