package com.icsc.aa.print.xls;

import java.util.List;
import java.util.Map;

import com.icsc.aa.dao.aajcAcctData4Acct16DAO;
import com.icsc.aa.dao.aajcAcctDataDAO;
import com.icsc.dpms.ds.dsjccom;

public class aajcGeneralLedgerAcct16Printer extends aajcGeneralLedgerAcctPrinter{
	public final static String CLASS_VERSION = "$Id: aajcGeneralLedgerAcct16Printer.java,v 1.1 2018/03/26 01:51:57 I21312 Exp $";

    public aajcGeneralLedgerAcct16Printer(dsjccom dsCom, Map map) throws Exception {
    	super(dsCom, map);
    }
    
    @Override
    protected List<Map<String, Object>> getData() throws Exception {
    	aajcAcctDataDAO dao = new aajcAcctData4Acct16DAO(dsCom, map);
    	return dao.queryData();
    }
    
}
