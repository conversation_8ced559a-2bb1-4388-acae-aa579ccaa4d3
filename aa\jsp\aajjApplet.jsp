<%@ page import="java.util.*,
com.icsc.dpms.de.structs.*,
com.icsc.aa.dei.*,
com.icsc.aa.dao.*"%>
<%@ page import="com.icsc.aa.util.*"%>

<% 
	Exception ex = null;

	Map appletMap = (Map) request.getAttribute("appletMap");
	if (appletMap == null) {
		dejcWebInfoOut appletInfoOut = (dejcWebInfoOut)request.getAttribute("infoOut") ;
		if(appletInfoOut != null){
			appletMap = (Map) appletInfoOut.getAttribute("appletMap");	
		}
	}
%>
<!-- 
<%=appletMap%>
 -->
<%		
	if (appletMap != null) {
		try {
			String aajjAppletFmtId = (String) appletMap.get("AA-APPLET-FMTID");
			if (aajjAppletFmtId == null) {
				throw new Exception("AA-APPLET-FMTID");
			}
			
			Object pageObj = appletMap.get("AA-APPLET-TTPAGES");
			if (pageObj == null) {
				throw new Exception("AA-APPLET-TTPAGES");
			}
	        
	        int ttPages = aajcUtil.getInt(pageObj.toString());
	        
			aajcApplet01VO aajjApplet_app01VO = new aajcApplet01DAO(_dsCom).findByPKExp(aajjAppletFmtId);
			List aajjApplet_app02List = new aajcApplet02DAO(_dsCom).findByMasterKey(aajjAppletFmtId);
      
	        String exeClass = "com/icsc/aa/applet/aajaSimplePrint";
	        String jarName = "aajaSimplePrint.jar";
	        
%>
<p align="center">
    <object classid="clsid:8AD9C840-044E-11D1-B3E9-00805F499D93" width="0" height="0" >
        <PARAM NAME="type" VALUE="application/x-java-applet;version=1.2">
        <PARAM NAME="code" VALUE="<%=exeClass%>.class">
        <PARAM NAME="archive" VALUE="<%=jarName %>">
        <PARAM NAME="codebase" VALUE="<%=_de300.applet("aa", exeClass, jarName)%>">
        <PARAM NAME="page_width" VALUE="<%=aajjApplet_app01VO.getW()%>">
        <PARAM NAME="page_height" VALUE="<%=aajjApplet_app01VO.getH()%>">
        <PARAM NAME="portrait" VALUE="<%=aajjApplet_app01VO.getPortrait()%>">
        <PARAM NAME="ttPages" VALUE="<%=ttPages%>">
        <%
	        for (int p=0;p < ttPages;p++) {
	        	int count = 0;
		        for (int i=0;i < aajjApplet_app02List.size();i++) {
		        	aajcApplet02VO aajjApplet_app02VO = (aajcApplet02VO)aajjApplet_app02List.get(i);
		        	int x = aajjApplet_app02VO.getX() + aajjApplet_app01VO.getX();
	        		int y = aajjApplet_app02VO.getY() + aajjApplet_app01VO.getY();
	        		
		        	for (int j=0; j < aajjApplet_app02VO.getColSize(); j++) {
		        		String value = (String)appletMap.get("page"+p+"-"+aajjApplet_app02VO.getItemCode()+j);
		        		//out.println("page"+p+"-"+app02VO.getItemCode()+j+" value=="+value);
		        		//out.println("x1=="+x);
		        		//out.println("y1=="+y);
		        		if (value==null) {
		        			continue;
		        		}
		        		if (j > 0) {
			        		if (aajjApplet_app02VO.getAdjWay().equals("H")) {
			        			x = x + aajjApplet_app02VO.getAdjAmt();
			        			//out.println("x2=="+x);
				        		//out.println("y2=="+y);
			        			if (aajjApplet_app02VO.getAdjCount()>0 && (j+1)%aajjApplet_app02VO.getAdjCount()==0) {
			        				x = x + aajjApplet_app02VO.getSmallAdj();
			        			}
			        		} else if (aajjApplet_app02VO.getAdjWay().equals("V")) {
			        			y = y + aajjApplet_app02VO.getAdjAmt();
			        			//out.println("x2=="+x);
				        		//out.println("y2=="+y);
			        			if (aajjApplet_app02VO.getAdjCount()>0 && (j+1)%aajjApplet_app02VO.getAdjCount()==0) {
			        				y = y + aajjApplet_app02VO.getSmallAdj();
			        			}
			        		}
			        	}
		        		//out.println("x3=="+x);
		        		//out.println("y3=="+y);
		        		
        %>
			<param name="page<%=p%>-value<%=count%>" value="[<%=value%>]">
			<param name="page<%=p%>-fit<%=count%>" value="<%=aajjApplet_app02VO.getFit()%>">
			<param name="page<%=p%>-x<%=count%>" value="<%=x%>">
			<param name="page<%=p%>-y<%=count%>" value="<%=y%>">
			<param name="page<%=p%>-fontType<%=count%>" value="<%=aajjApplet_app02VO.getFontType()%>">
			<param name="page<%=p%>-fontSize<%=count%>" value="<%=aajjApplet_app02VO.getFontSize()%>">
            <param name="page<%=p%>-limit<%=count%>" value="<%=aajjApplet_app02VO.getLimit()%>">
            <param name="page<%=p%>-addition<%=count%>" value="<%=aajjApplet_app02VO.getAddition()%>">
            <param name="page<%=p%>-shortage<%=count%>" value="<%=aajjApplet_app02VO.getShortage()%>">
            <param name="page<%=p%>-underLine<%=count%>" value="<%=aajjApplet_app02VO.getUnderLine()%>">
           
           	<param name="YN" value="<%=appletMap.get("YN")%>">
           
        <%
        				count++;
		        	}
	        	}
	        }
        %>

        <embed type="application/x-java-applet;version=1.2" width="0" height="0"
                    code="<%=exeClass %>.class"
                    pluginspage="http://java.sun.com/products/plugin/1.2/plugin-install.html">
        <noembed>
            No JDK 1.2 support for APPLET!!
        </noembed>
    </object>
<%
		} catch (Exception e) {
			ex = e;
		}
		
		if (ex != null) {
%>
	
<%
		}
	}
%>