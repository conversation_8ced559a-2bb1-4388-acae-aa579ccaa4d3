/**
 * 
 */
package com.icsc.ap.func;

import java.io.File;
import java.lang.reflect.Constructor;

import com.icsc.ap.upload.apjcUploadCom;
import com.icsc.dpms.de.dejc329;
import com.icsc.dpms.de.structs.dejcFunctionalController;
import com.icsc.dpms.ds.dsjccom;

/**
 * 
 */
public class apjcUpload extends dejcFunctionalController {

    /**
     * 
     */
    public apjcUpload() {
        this.AppId = "apjcUpload";

    }

    public void upload() {
        try {
            String type = infoIn.getParameter("type");

            Class c = Class.forName(type);
            Constructor constructor = c.getConstructor(new Class[] { dsjccom.class });
            apjcUploadCom apUploadCom = (apjcUploadCom) constructor.newInstance(new Object[] { this.dsCom});

            String fileName = infoIn.getParameter("fileName") == null ? "" : infoIn.getParameter("fileName");
            dejc329 de329 = new dejc329(fileName, dsCom);
            dejc329[] ret = de329.parse();
            File file = new File(ret[0].getFilePath());

            String msg = apUploadCom.upload(file);

            infoOut.setMessage(msg);
        } catch (Exception e) {
            e.printStackTrace();
            this.handleExp(e);
        }

    }

    /*
     * (non-Javadoc)
     * 
     * @see com.icsc.dpms.de.structs.dejcFunctionalController#init()
     */
    public void init() throws Exception {
        // TODO Auto-generated method stub

    }
}
