<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import="com.icsc.dpms.de.*" %>
<%@ page import="com.icsc.dpms.de.web.*" %>
<%@ page import="com.icsc.dpms.de.structs.web.*" %>
<%@ page import="com.icsc.dpms.de.structs.*" %>
<%@ page import="com.icsc.aa.*" %>
<%@ page import="com.icsc.aa.dao.*" %>
<%@ page import="com.icsc.aa.dei.*" %>
<%@ page import="java.util.*" %>


<%! public static final String _AppId = "AAJJC63"; %>

<%@ include file="../../jsp/dzjjmenu.jsp" %>

<style type="text/css">
div.jqmDialog {
	left: 35%;
  width: 800px;
  margin-left: 0px;
  left: 200px;
}

div.jqmdBC {
  height: 400px;
}
</style>


<% 
	String sessionId = dejcUtility.genWinId(session);
	String target="aajjc63_"+ sessionId;
%>

<de:form name="form1" action="/erp/aa/do?_pageId=aajjc63" target="<%=target%>" >
<table width="100%"  >
</table>

<input type="hidden" name="_action" value="">

<div id="detailtableTab" >
	<iframe name="aajjc63_<%=sessionId %>" id="aajjc63Id" width="100%" height="100%" frameborder=0 src="/erp/aa/do?_pageId=aajjc63&_action=I"></iframe> 
</div>
</de:form>
<Script language="JScript" src="<%=_de300.script("de","/dejtFolderFrame.jss")%>"></script>
<Script language="JScript" src="<%=_de300.script("de","/dejtTemplate1.jss")%>"></script>
<script language="JavaScript" src="<%=_de300.script("de","/dejtcaym.jss")%>"></script>

<script>

$('iframe').css('height',$(window).height()*0.8);
</script>
<de:footer />
