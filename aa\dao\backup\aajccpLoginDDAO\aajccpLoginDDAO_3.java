/*----------------------------------------------------------------------------*/
/* aajccpLoginDDAO		DAOTool Ver 10.0112 (INPUT FILE VERSION:2.0)
/*----------------------------------------------------------------------------*/
/* author : InfoChamp
/* system : 普通會計管理系統(AA)
/* target : 用戶授權帳套別及廠別維護作業基本資料明細文件
/* create : 104/09/01
/* update : 104/09/01
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao;

import java.sql.*;
import java.text.*;
import java.util.*;
import java.math.* ;
import com.icsc.dpms.de.*;
import com.icsc.dpms.de.sql.*;
import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;

/**
 * 用戶授權帳套別及廠別維護作業基本資料明細文件 DAO.
 * <pre>
 * Table Name        : DB.TBAACPLOGIND
 * Table Description : 用戶授權帳套別及廠別維護作業基本資料明細文件
 * Value Object Name : aajccpLoginDVO
 * </pre>
 * @version $Id: aajccpLoginDDAO_3.java,v 1.1 2015/09/30 05:13:30 I27368 Exp $
 * @since aajccpLoginDVO - 104/09/01
 * <AUTHOR>
 */
public class aajccpLoginDDAO extends dejcCommonDAO {
	public final static String AppId = "AAJCCPLOGINDDAO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:30 $";

/*----------------------------------------------------------------------------*/
/* Creates new aajccpLoginDDAO
/*----------------------------------------------------------------------------*/

	/**
	 * 膘凳赽
	 * @param dsCom - 僕蚚啋璃
	 * @since 104/09/01
	 */
	public aajccpLoginDDAO(dsjccom dsCom) {
		super(dsCom, AppId);
	}
	/**
	 * 膘凳赽
	 * @param dsCom - 僕蚚啋璃
	 * @param con - 蝠眢蟀諉唚
	 * @since 104/09/01
	 */
	public aajccpLoginDDAO(dsjccom dsCom, Connection con) {
		super(dsCom, con) ;
		super.appId = this.AppId;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/
	/**
	 * 郪磁眕翋瑩脤戙腔 sql
	 * @since 104/09/01
	 */
	private String getFindByPKSql(String userId,String compId,String factoryId) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId='"+userId+"' and compId='"+compId+"' and factoryId='"+factoryId+"' ");
		return sqlStr.toString();
	}
	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @return aacpLoginDVO - 等捩訧蹋
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public aajccpLoginDVO findByPK(String userId,String compId,String factoryId)
			throws SQLException, Exception {
		this.sql = getFindByPKSql(userId,compId,factoryId) ;
		return (aajccpLoginDVO) this.eQuery(this.sql) ;
	}

	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @return aacpLoginDVO - 等捩訧蹋
	 * @exception dejcNotFoundException - �聹斢輓論岏珅�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public aajccpLoginDVO findByPKExp(String userId,String compId,String factoryId)
			throws dejcNotFoundException, SQLException, Exception {
		aajccpLoginDVO aacpLoginDVO = findByPK(userId,compId,factoryId) ;
		if (aacpLoginDVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginDVO ;
	}

	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @param aacpLoginDVO - 蚕妏蚚氪垀枑鼎腔 object, DAO 祥鍚俴 new
	 * @return aacpLoginDVO - 等捩訧蹋
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public aajccpLoginDVO findByPK(aajccpLoginDVO aacpLoginDVO, String userId,String compId,String factoryId)
			throws SQLException,Exception {
		this.sql = getFindByPKSql(userId,compId,factoryId) ;
		return (aajccpLoginDVO) this.eQuery(aacpLoginDVO, this.sql) ;
	}

	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @param aacpLoginDVO - 蚕妏蚚氪垀枑鼎腔 object, DAO 祥鍚俴 new
	 * @return aacpLoginDVO - 等捩訧蹋
	 * @exception dejcNotFoundException - �聹斢輓論岏珅�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public aajccpLoginDVO findByPKExp(aajccpLoginDVO aacpLoginDVO, String userId,String compId,String factoryId)
			throws dejcNotFoundException, SQLException, Exception {
		aacpLoginDVO = findByPK(aacpLoginDVO, userId,compId,factoryId) ;
		if (aacpLoginDVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginDVO ;
	}

	/**
	 * 葩磁脤戙訧蹋
	 * <p>
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public Vector findByMasterKey(String userId,String compId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId='"+userId+"' and compId='"+compId+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}

	/**
	 * 葩磁脤戙訧蹋
	 * <p>
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public Vector findByMasterKey(String userId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId='"+userId+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}
	/**
	 * 陔崝珨捩訧蹋 throws
	 * <p>
	 * @param aacpLoginDVO - Value Object
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿謹�
	 * @exception dejcDupException - 笭葩瑩硉
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int create(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		if (aacpLoginDVO.verify() == false) {
			throw new dejcEditException(aacpLoginDVO.getMessage()) ;
		}
		if (aacpLoginDVO.isKeyOk() == false) {
			throw new dejcEditException("Value of key["+AppId+"].["+aacpLoginDVO.getMessage()+"] is null or empty!") ;
		}

		
		this.sql = getCreateSql(aacpLoginDVO) ;

		try {
			int rslt = this.executeUpdate(this.sql) ;

			if (rslt == 0) {
				throw new dejcNoUpdateException(this.sql) ;
			}
			return rslt;
		} catch (SQLException sqle) {
            handleDupException(sqle);
            return -1;
		}
	}
	
	public String getCreateSql(aajccpLoginDVO aacpLoginDVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGIND (");
		sqlStr.append("userId,compId,factoryId");
		sqlStr.append(") VALUES (");
		sqlStr.append("'").append(aacpLoginDVO.getUserIdS()).append("','").append(aacpLoginDVO.getCompIdS()).append("','").append(aacpLoginDVO.getFactoryIdS()).append("'");
		sqlStr.append(")");	
		this.sql = sqlStr.toString() ;
		return this.sql;
	}

	/**
	 * 陔崝嗣捩訧蹋 throws
	 * <p>
	 * @param aacpLoginDVOList - Value Object
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿謹�
	 * @exception dejcDupException - 笭葩瑩硉
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int createList(List aacpLoginDVOList)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			count = count+this.create(aacpLoginDVO) ;
		}
		return count ;
	}

  	/**
	 * 刉壺訧蹋
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int remove(String userId,String compId,String factoryId)
			throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId='"+userId+"' and compId='"+compId+"' and factoryId='"+factoryId+"' " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	

	/**
	 * 刉壺訧蹋 throws
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int removeExp(String userId,String compId,String factoryId)
			throws dejcNoUpdateException, SQLException, Exception {
		int rslt = remove(userId,compId,factoryId) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(this.sql) ;
		}
		return rslt ;
	}

	/**
	 * 刉壺訧蹋 throws
	 * <p>
	 * @param aacpLoginDVO 郗刉壺腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int removeExp(aajccpLoginDVO aacpLoginDVO)
			throws dejcNoUpdateException, SQLException, Exception {
		return removeExp(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId()) ;
	}

	/**
	 * 刉壺嗣捩訧蹋 throws
	 * <p>
	 * @param aacpLoginDVOList 郗刉壺腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int removeList(List aacpLoginDVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			count = count+this.remove(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId()) ;
		}
		return count ;
	}

	/**
	 * 刉壺訧蹋 throws
	 * <p>
	 * @param aacpLoginDVO 郗刉壺腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int remove(aajccpLoginDVO aacpLoginDVO)
			throws SQLException, Exception {
		return remove(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId()) ;
	}

	/**
	 * 党蜊訧蹋
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int update(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginDVO.verify() == false) {
			throw new dejcEditException(aacpLoginDVO.getMessage()) ;
		}
		if(aacpLoginDVO.hasEditFields()) {
			return updateEditFields(aacpLoginDVO);
		} else {
			return this.executeUpdate(getUpdateSql(aacpLoginDVO) ) ;
		}
	}

	public String getUpdateSql(aajccpLoginDVO aacpLoginDVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGIND");
		sqlStr.append(" SET userId = '" + aacpLoginDVO.getUserId() + "'");
		sqlStr.append("    ,compId = '" + aacpLoginDVO.getCompId() + "'");
		sqlStr.append("    ,factoryId = '" + aacpLoginDVO.getFactoryId() + "'");
		sqlStr.append(" WHERE  userId='"+aacpLoginDVO.getUserId()+"' and compId='"+aacpLoginDVO.getCompId()+"' and factoryId='"+aacpLoginDVO.getFactoryId()+"' ");
		this.sql = sqlStr.toString();
		return this.sql ;	
	}
	
	/**
	 * �躆岏珃郺升髲結銓派炬籤肱瑏譫藝裕�
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int updateFldsNotDef(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginDVO.verify() == false) {
			throw new dejcEditException(aacpLoginDVO.getMessage()) ;
		}
		if (aacpLoginDVO.isKeyOk() == false) {
			throw new dejcEditException("primary key["+aacpLoginDVO.getMessage()+"] of ["+AppId+"] is empty!") ;
		}		
		StringBuffer updateFlds = new StringBuffer();
		if(updateFlds.length()==0) {
			return 0;
		}	
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGIND");
		sqlStr.append(" SET ").append( updateFlds.substring(0, updateFlds.length()-1) );
		sqlStr.append(" WHERE  userId='"+aacpLoginDVO.getUserId()+"' and compId='"+aacpLoginDVO.getCompId()+"' and factoryId='"+aacpLoginDVO.getFactoryId()+"' ");
		this.sql = sqlStr.toString();
		return this.executeUpdate(this.sql) ;
	}


	/**
	 * 党蜊訧蹋 throws
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿瑏�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int updateExp(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int rslt = update(aacpLoginDVO) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(sql) ;
		}
		return rslt ;
	}

	/**
	 * 党蜊-陔崝嗣捩訧蹋, �蝜�党蜊 0 捩腔趕ㄛ撈陔崝蜆捩訧蹋
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿瑏�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int updateCreateList(List aacpLoginDVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			int updateCount = update(aacpLoginDVO) ;
			if ( updateCount==0 ) {
				count += create(aacpLoginDVO) ;
			} else {
				count += updateCount ;
			}
		}
		return count ;
	}

	/**
	 * 党蜊嗣捩訧蹋 throws
	 * <p>
	 * @param aacpLoginDVOList 郗党蜊腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱鉼瑏�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	public int updateList(List aacpLoginDVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			count = count+this.updateExp(aacpLoginDVO) ;
		}
		return count ;
	}

	/**
	 * 硒俴 addCreateBatch(Object obj) 奀剒猁蚚善腔 sql
	 * 陔崝訧蹋腔 prepareStatement sql<br>
	 * 森源楊岆葡迡 commonDAO 腔源楊
	 */
    protected String getCreatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGIND  (");
		sqlStr.append("userId,compId,factoryId");
		sqlStr.append(") VALUES (");
		sqlStr.append("?,?,?");
		sqlStr.append(")");		    
		return sqlStr.toString();
    }

	/**
	 * 硒俴 addupdateBatch(Object obj) 奀剒猁蚚善腔 sql
	 * 党蜊訧蹋腔 prepareStatement sql<br>
	 * 森源楊岆葡迡 commonDAO 腔源楊
	 */
    protected String getUpdatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGIND");
		sqlStr.append(" SET  ");
		sqlStr.append(" WHERE  userId=? and compId=? and factoryId=? ");
		return sqlStr.toString();
    }

	/**
	 * 硒俴 addDeleteBatch(Object obj) 奀剒猁蚚善腔 sql
	 * 刉壺訧蹋腔 prepareStatement sql<br>
	 * 森源楊岆葡迡 commonDAO 腔源楊
	 */
    protected String getDeletePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId=? and compId=? and factoryId=? " );
		return sqlStr.toString();
    }

	/**
	 * 硒俴 addCreateBatch(Object obj) 奀剒猁網請腔源楊
	 */
    protected void prepareCreate(Object obj) throws SQLException {
    	aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
		pstmt.setString(1, aacpLoginDVO.getUserId()) ;
		pstmt.setString(2, aacpLoginDVO.getCompId()) ;
		pstmt.setString(3, aacpLoginDVO.getFactoryId()) ;
    }

	/**
	 * 硒俴 addUpdateBatch(Object obj) 奀剒猁網請腔源楊
	 */
    protected void prepareUpdate(Object obj) throws SQLException {
    	aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
		pstmt.setString(1, aacpLoginDVO.getUserId()) ;
		pstmt.setString(2, aacpLoginDVO.getCompId()) ;
		pstmt.setString(3, aacpLoginDVO.getFactoryId()) ;
    }

	/**
	 * 硒俴 addDeleteBatch(Object obj) 奀剒猁網請腔源楊
	 */
    protected void prepareDelete(Object obj) throws SQLException {
    	aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
		pstmt.setString(1, aacpLoginDVO.getUserId()) ;
		pstmt.setString(2, aacpLoginDVO.getCompId()) ;
		pstmt.setString(3, aacpLoginDVO.getFactoryId()) ;
    }

	/**
	 * 妗釬虜濬梗
	 * <p>
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	protected Object getObjFromRS(ResultSet rs) throws SQLException,Exception {
		return aajccpLoginDVO.getInstanceByName(rs) ;
	}

	/**
	 * 妗釬虜濬梗, 妏蚚 user 換輛懂腔 object, 祥鍚俴 new , 眕誹吽硒俴奀潔摯暮砪极<br>
	 * 巠磁還奀俶腔訧蹋堍呾(籵都婓堍呾湮講訧蹋奀ㄛ珨虳戲弇剒猁婃湔婓 value object)<br>
	 * 森源楊硐頗婓硒俴 findByPK(Object obj,String userId,String compId,String factoryId) 奀符掩網請﹝
	 * <p>
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/09/01
	 */
	protected Object getObjFromRS(Object obj, ResultSet rs) throws SQLException,Exception {
		return aajccpLoginDVO.getInstanceByName(obj, rs) ;
	}
	
	/**
	  * 掘爺源楊 2009.03.26 陔崝
	  */
	public int backup(String tableName, aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		this.sql = getCreateSql(aacpLoginDVO).replaceFirst(" DB.TBAACPLOGIND "," "+tableName+" "); 
		return this.executeUpdate(this.sql) ;
	} 
	public int backup(String tableName, ResultSet rs) throws SQLException, Exception {
		return backup(tableName, aajccpLoginDVO.getInstanceByName(rs) ) ;
	} 

	public String getUpdateEditFieldsPartialSql(aajccpLoginDVO aacpLoginDVO)  {
		if( aacpLoginDVO.getEditFields()==null ) {
			return "";
		}
		return dejcSqlUtils.genUpdateFields(  aacpLoginDVO.getEditFields() );		
	}
	/**
	  * 党蜊衄雄徹腔戲弇ㄛ森源楊猁衄虴③�楛�衄珂硒俴  aajccpLoginDVO.monitor() 
	  */
	public String getUpdateEditFieldsSql(aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		String updSql =getUpdateEditFieldsPartialSql(aacpLoginDVO) ;
		if(updSql.equals("")){
			return "";
		}
		return "update DB.TBAACPLOGIND set "+updSql+" where   userId='"+aacpLoginDVO.getUserId()+"' and compId='"+aacpLoginDVO.getCompId()+"' and factoryId='"+aacpLoginDVO.getFactoryId()+"' ";
	}
	/**
	  * 党蜊衄雄徹腔戲弇ㄛ森源楊猁衄虴③�楛�衄珂硒俴  aajccpLoginDVO.monitor() 
	  */
	public int updateEditFields(aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		this.sql=getUpdateEditFieldsSql(aacpLoginDVO);
		if(this.sql.equals("")){
			// 2010.03.02 植0蜊峈-1ㄛ眕妎梗岆瘁衄硒俴
			return -1;
		}
		return this.executeUpdate(getUpdateEditFieldsSql(aacpLoginDVO)) ;
	}
	/**
	 * 樑update腔sqlㄛ梑郔綴珨跺戲弇懂樑update
	 */	
	private String getLockSqlPrefix() {
		return "update DB.TBAACPLOGIND set factoryId=factoryId ";
	}

	/**
	 * 脤戙﹜坶隅﹜潼諷訧蹋
	 */	
	public aajccpLoginDVO loadByPK4Update(String userId,String compId,String factoryId) throws SQLException, Exception {
		List aList = loadList4Update("WHERE   userId='"+userId+"' and compId='"+compId+"' and factoryId='"+factoryId+"' ");
		if(aList.size()>0) {
			return (aajccpLoginDVO)aList.get(0);
		}else {
			throw new dejcNotFoundException(this.sql) ;	
		}	
	}
	
	private String getSelectSqlPrefix() {
		return "select * from DB.TBAACPLOGIND ";
	}
	
	private List loadList4Update(String conditionSql) throws SQLException, Exception {
		this.executeUpdate(getLockSqlPrefix()+conditionSql) ;
		this.sql=getSelectSqlPrefix()+conditionSql ;
		return monitor(this.eQueryAll( this.sql ));
	}
	/**
	  *潼諷訧蹋
	  */
	private List monitor(List aacpLoginDVOList){
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			aacpLoginDVO.monitor();
		}	
		return aacpLoginDVOList;
	}
	/**
	 * 眕 VO 檜堤 DB 訧蹋ㄛ檜訧蹋肮奀頗坶隅
	 */
	public aajccpLoginDVO loadByVO(aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		return loadByPK4Update(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId());
	}
	/**
	 * 眕 VO List 檜堤 DB 訧蹋ㄛ檜訧蹋肮奀頗坶隅
	 */
	public List loadByList(List aacpLoginDVOList) throws SQLException, Exception {
		List rsltList= new ArrayList(aacpLoginDVOList.size());
		for (Iterator iterator = aacpLoginDVOList.iterator(); iterator.hasNext();) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO) iterator.next();
			rsltList.add( loadByVO(aacpLoginDVO) ) ;
		}
		return rsltList;
	}
	/**
	 * 眕雄怓脤戙沭璃脤戙
	 */
	public Vector findByCriteria(dejcCriteria criteria)throws dejcNotFoundException, SQLException, Exception{
		return findByCriteria(criteria,"");
	}
	/**
	 * 眕雄怓脤戙沭璃脤戙
	 */
	public Vector findByCriteria(dejcCriteria criteria,String orderby)throws dejcNotFoundException, SQLException, Exception{
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append(getSelectSqlPrefix());
		if(!dejcUtility.isNull(orderby)) {
			orderby="order by "+orderby;
		}
		sqlStr.append(criteria.toSqlWithWhereIfExists()).append(orderby);  
		this.sql = sqlStr.toString();
		return this.eQueryAll(this.sql) ;
	}
	
//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

    public List findAll() throws SQLException, Exception{
    	this.sql = "select * from DB.TBAACPLOGIND ";
    	return this.eQueryAll(this.sql);
    }
    public List findbyCompId(String pCompId) throws SQLException, Exception{
    	this.sql = "select * from db.tbaacploginD where compId = '"+pCompId+"'";
    	return this.eQueryAll(this.sql);
    }
	public int updateCreate(aajccpLoginDVO vo) throws dejcEditException,
			dejcNoUpdateException, SQLException, Exception {
		int count = 0;
		if (update(vo) == 0) {
			count = create(vo);
		}
		return count;
	}
	public int removeByUserId(String userId) throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId='"+userId+"' " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	
	public Vector findByCondition(String userId,String compId,String factoryId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId = '" + userId + "' ");
		if(!(compId==null) && !compId.equals("")){
			sqlStr.append(" AND compId = '" + compId + "' ");
		}
		if(!(factoryId==null) && !factoryId.equals("")){
			sqlStr.append(" AND factoryId = '" + factoryId + "' ");
		}
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}
	
	public int myUpdateList(List aacpLoginDVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			int updateCount = update(aacpLoginDVO) ;
			if ( updateCount==0 ) {
				count += create(aacpLoginDVO) ;
			} else {
				count += updateCount ;
			}
		}
		return count ;
	}
	
	public int myRemove(String userId,String compId,String factoryId)
			throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId='"+userId+"' ");
		if(!(compId==null) && !compId.equals("")){
			sqlStr.append("   and compId='"+compId+"' ");
		}
		if(!(factoryId==null) && !factoryId.equals("")){
			sqlStr.append("   and factoryId='"+factoryId+"' " );
		}
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}


} // end of Class aajccpLoginDDAO