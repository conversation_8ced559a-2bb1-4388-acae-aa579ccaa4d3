package com.icsc.aa.util;

import java.math.BigDecimal;

import com.icsc.dpms.de.dejcMyException;

public class aajcuException extends dejcMyException {

	private static final long serialVersionUID = 1L;

	public final String AppId = "aajcuException".toUpperCase();

	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:20 $";

	public aajcuException(String msg) {
		super(msg);
	}


	public static void isNull(Object obj, String msg) throws aajcuException {
		if (obj == null)
			throw new aajcuException(msg);
	}

	
	public static void isSpace(String s, String msg) throws aajcuException {
		if (s == null || s.trim().equals("")) {
			throw new aajcuException(msg);
		}
	}


	public static void isZero(int i, String msg) throws aajcuException {
		if (i == 0) {
			throw new aajcuException(msg);
		}
	}


	
	public static void isTrue(boolean value, String msg) throws aajcuException {
		if (value) {
			throw new aajcuException(msg);
		}
	}
	
	public static void isStar(String s, String msg) throws aajcuException {
		if (s == null || s.trim().equals("*")) {
			throw new aajcuException(msg);
		}
	}

}
