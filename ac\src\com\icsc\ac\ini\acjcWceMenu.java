package com.icsc.ac.ini;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import com.icsc.dpms.de.tree.dejcLvlNode;
import com.icsc.dpms.de.tree.dejcLvlTree;
import com.icsc.dpms.de.tree.dejcTreeRead;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.util.zafctool;
import com.icsc.ac.dao.*;

public class acjcWceMenu extends dejcTreeRead {

	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:27:13 $";

	public acjcWceMenu() {
		super();
	}

	private dejcLvlNode getNodeFromDept(acjcb3VO b3VO) {

		String nodeNo = b3VO.getWceLevelCode();
		String nodeName = b3VO.getLevelCode() + "_" + b3VO.getLevelName();
		String nodeTypeVale = b3VO.getNodeType();
		return new dejcLvlNode(nodeNo, nodeTypeVale, nodeName, estimate(b3VO));

	}

	private dejcLvlNode getNodeFromDept(acjcb2VO acb2VO) {

		String nodeNo = acb2VO.getWce();
		String nodeName = acb2VO.getWce() + "_" + acb2VO.getWceDesc();
		String nodeTypeVale = "DOC";
		return new dejcLvlNode(nodeNo, nodeTypeVale, nodeName, dejcLvlNode.TYPE_LEAF);

	}

	private int estimate(acjcb3VO b3VO) {
		return dejcLvlNode.TYPE_FOLDER;

	}

	public dejcLvlTree generateRoot(dsjccom dsCom, HttpServletRequest req, String rootName) {
		try {
			String compId = zafctool.trim(req.getParameter("compId"));

			acjcb3DAO setDao = new acjcb3DAO(dsCom);
			List allSorts = setDao.doQueryFirstLayer(compId);

			dejcLvlTree tree = new dejcLvlTree(this.getClass(), rootName);

			for (int i = 0; i < allSorts.size(); i++) {
				acjcb3VO b3VO = (acjcb3VO) allSorts.get(i);
				// /change 2_23?

				String nodeNo = b3VO.getWceLevelCode();
				String nodeName = b3VO.getLevelCode() + "_" + b3VO.getLevelName();

				String nodeTypeVale = b3VO.getNodeType();
				tree.addNode(new dejcLvlNode(nodeNo, nodeTypeVale, nodeName, estimate(b3VO)));
				// tree.addNode(new dejcLvlNode(nodeNo, "value="+nodeNo,
				// nodeName, dejcLvlNode.TYPE_FOLDER)) ;

			}
			return tree;
		} catch (Throwable e) {
			return null;
		}
	}

	public Vector getNodes(dsjccom dsCom, HttpServletRequest req, String id) {

		try {
			int flag = id.lastIndexOf("-");
			if (flag <= 0)
				return new Vector();
			String compId = id.substring(id.lastIndexOf("-") + 1, id.length());
			String idStr = id.substring(0, id.lastIndexOf("-"));

			acjcb3DAO setDao = new acjcb3DAO(dsCom);
			acjcb3VO b3VO = setDao.findByPKExp(compId, id);

			Vector rslts = new Vector();
			if (b3VO.getNodeType().equals("DOC")) {
				acjcb2DAO b2Dao = new acjcb2DAO(dsCom);
				Vector b2 = b2Dao.queryByWceLevelCode(compId, idStr, "");
				for (int i = 0; i < b2.size(); i++) {

					acjcb2VO acb2VO = (acjcb2VO) b2.get(i);

					dejcLvlNode node = getNodeFromDept(acb2VO);
					rslts.add(node);
				}

			} else if (b3VO.getNodeType().equals("DIR")) {
				Vector nodes = setDao.doQueryLayerDir(compId, idStr);
				for (int i = 0; i < nodes.size(); i++) {

					b3VO = (acjcb3VO) nodes.get(i);

					dejcLvlNode node = getNodeFromDept(b3VO);
					rslts.add(node);
				}
			}
			return rslts;
		} catch (Exception e) {
			return new Vector();
		}
	}
}
