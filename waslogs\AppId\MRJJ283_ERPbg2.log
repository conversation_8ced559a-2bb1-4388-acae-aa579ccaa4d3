2025/08/21 23:00:37.064	ERPbg2_265228239	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:37.123	ERPbg2_265228239	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:37.197	ERPbg2_265228239	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:37.250	ERPbg2_265228239	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:39.259	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:39.305	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:39.474	ERPbg2_215878878	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:39.522	ERPbg2_215878878	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:39.543	ERPbg2_215878878	1	bg2	ICSCMR	MRJJ283	dejcQryGrid#com.icsc.dpms.de.web.dejcQryGrid.doFetchData()	sql	[dejcQryGrid] sql:
2025/08/21 23:00:39.980	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	
--------------------------
2025/08/21 23:00:39.980	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	 SELECT a.purposeId,b.purposeIdName  FROM DB.TBMFV022 a join db.tbMFV021 b on (a.purposeId=b.purposeId)  WHERE  a.compId='bg2' and a.issuetype='null'  and b.systemId in ('MF','MR')   order by a.purposeId;1
2025/08/21 23:00:39.980	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	--------------------------

2025/08/21 23:00:40.010	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:40.219	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:40.284	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:40.284	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	DEJCCOMMONDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	
--------------------------
2025/08/21 23:00:40.284	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	DEJCCOMMONDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	 SELECT valueA,valueB,valueC,valueD FROM DB.TBMFC323  WHERE  ParamId='MRInnerDept' and compId='bg2'   order by ParamId,compId,valueA ;1
2025/08/21 23:00:40.284	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	DEJCCOMMONDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	--------------------------

2025/08/21 23:00:40.386	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	
--------------------------
2025/08/21 23:00:40.386	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	 SELECT inventoryType,matrlIndexId ,matrlIndex,matrlindexDesc FROM DB.TBMFV001 WHERE  matrlIndexRel=''  AND inventoryType='R' and matrlIndexId>'' order by matrlIndexId asc ;1
2025/08/21 23:00:40.386	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	--------------------------

2025/08/21 23:00:40.416	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:40.746	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	
--------------------------
2025/08/21 23:00:40.747	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	 SELECT inventoryType,matrlIndexId ,matrlIndex,matrlindexDesc FROM DB.TBMFV001 WHERE  matrlIndexRel='null'  AND inventoryType='' and matrlIndexId>'' order by matrlIndexId asc ;1
2025/08/21 23:00:40.747	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	--------------------------

2025/08/21 23:00:40.779	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:40.809	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	MFJCTBC323DAO#com.icsc.dpms.de.dejcCommonDAO.query()	query	
--------------------------
2025/08/21 23:00:40.809	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	MFJCTBC323DAO#com.icsc.dpms.de.dejcCommonDAO.query()	query	SELECT * FROM db.TBMFC323 WHERE  ParamId='MF091' and  CompId='bg2' and ValueA='011'  order by ParamId,CompId,ValueA  desc;1
2025/08/21 23:00:40.809	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	MFJCTBC323DAO#com.icsc.dpms.de.dejcCommonDAO.query()	query	--------------------------

2025/08/21 23:00:40.891	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:40.925	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	
--------------------------
2025/08/21 23:00:40.925	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	SELECT LAYERINDEX,LayerIndex as LAYERID ,layerDesc FROM DB.TBMFV015 WHERE   LAYERREL = '' and systemId='MR' and compId='bg2' order by layerId asc ;1
2025/08/21 23:00:40.925	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	--------------------------

2025/08/21 23:00:40.974	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
2025/08/21 23:00:41.059	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	
--------------------------
2025/08/21 23:00:41.059	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	SELECT LAYERINDEX,LayerIndex as LAYERID ,layerDesc FROM DB.TBMFV015 WHERE   LAYERREL = 'null' and systemId='MI' and compId='bg2' order by layerId asc ;1
2025/08/21 23:00:41.059	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejcQueryDAO#com.icsc.dpms.de.dejcCommonDAO.queryAll()	queryAll	--------------------------

2025/08/21 23:00:41.105	ERPbg2_329126814	1	bg2	ICSCMR	MRJJ283	dejc301ConSettingAbstract#com.icsc.dpms.de.sql.dejc301ConSettingFactor$1.setInfo()	setInfo	need not to set
