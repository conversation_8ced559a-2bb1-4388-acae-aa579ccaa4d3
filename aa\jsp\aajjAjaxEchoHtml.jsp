<%@page import="com.icsc.aa.util.aajcUtil"%>
<%@ page contentType = "text/xml;charset=GBK"%>

<%@ page import="com.icsc.dpms.de.structs.*" %>
<%@ page import="com.icsc.dpms.de.*" %>
<%@ page import="com.icsc.dpms.ds.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.math.*" %>
<%@ page import="com.icsc.aa.dao.*" %>


<%! public static final String _AppId = "AAJJC1"; %>


<%
dejc300 _de300 = new dejc300();
dsjccom _dsCom = _de300.run(_AppId, this, request, response);
if(_dsCom == null) 
	return;

String action = (String)request.getParameter("action");
System.out.println("action=" + action);

if(action==null || action.equals("")){
    dejcWebInfoOut infoOut = (dejcWebInfoOut)request.getAttribute("infoOut") ;
    String  msg = infoOut==null||infoOut.getAttribute("msg") == null ? "" : (String)infoOut.getAttribute("msg");
    out.print(msg);    
}else if(action.equals("genActiveC1Field")){
	String pCompId = request.getParameter("pCompId");
    String compId = request.getParameter("compId");
    String checkCode = request.getParameter("checkCode");
    boolean isDefault = aajcUtil.convertStoB(request.getParameter("isDefault"));
    //System.out.println("pCompId=" + pCompId);
    //System.out.println("compId=" + compId);
    //System.out.println("checkCode=" + checkCode);
    List aac2List = null;
    if(!checkCode.equals("")){
    	aajcc2DAO aac2DAO = new aajcc2DAO(_dsCom);
    	try{
    		aac2List = aac2DAO.findByFKExp(pCompId,compId,checkCode);
    	}catch(Exception e){
    		aac2List = new ArrayList();
    	}
    }else{
    	aac2List = new ArrayList();
    }
    
    for(int i = 0 ; i < aac2List.size();i++){
      aajcc2VO aac2VO = (aajcc2VO)aac2List.get(i);       
%>
	<tr>
		<td class="subsys-title" width=10%><%=aac2VO.getFieldCodeName()%></td>
		<td class="light-bg-left" colspan='5'><%=aac2VO.getFieldHtml(_dsCom,isDefault)%></td>
	</tr>
<%
    }
%>

<%}%>