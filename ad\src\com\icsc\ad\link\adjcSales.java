package com.icsc.ad.link;

import java.math.BigDecimal;

import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.bp.zafcComBp;
import com.icsc.facc.zaf.util.zafctool;

public class adjcSales extends zafcComBp {
	public final static String CLASS_VERSION = "adjcSales $Revision: 1.1 $ $Date: 2021/02/20 06:35:49 $";

	protected String compId = "";

	protected String dpd = "";

	public adjcSales(dsjccom dsCom) {
		super(dsCom);
	}

	protected void init() {
		this.AppId = CLASS_VERSION;
		this.de318 = new dejc318(dsCom, AppId);
	}

	public BigDecimal getYearRevenue(String year) {
		return zafctool.ZERO;
	}

	public BigDecimal getMonthRevenue(String month) {
		return zafctool.ZERO;
	}

	public BigDecimal getDateRevenue(String date) {
		return zafctool.ZERO;
	}

	public void setCompId(String compId) {
		this.compId = compId;
	}

	public void setDpd(String dpd) {
		this.dpd = dpd;
	}

}
