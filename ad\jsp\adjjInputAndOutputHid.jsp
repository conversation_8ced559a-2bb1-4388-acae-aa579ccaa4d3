<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ADJJINPUTANDOUTPUT";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = zafctool.trim(request.getParameter("compId"));
	String workArea = zafctool.trim(request.getParameter("workArea"));
	String costCenter = zafctool.trim(request.getParameter("costCenter"));
	String startDate = zafctool.trim(request.getParameter("startDate"));

	String yyyy = startDate.length()>4?startDate.substring(0,4):String.valueOf(_de308.getWYear());
%>
<textarea name="CHILDSELECT" style="display:none">
	<select name="COSTCENTER0">
		<%=ipjcWorkArea.getCCOptions(_dsCom, compId, yyyy, workArea, costCenter)%>
	</select>
</textarea>  

<script>
	if(parent){
		parent.document.all("CC").innerHTML = document.all("CHILDSELECT").value;
	}
</script>