CREATE TABLE DB.TBAAW3 (
compId CHAR(10) DEFAULT '' NOT NULL,vchrDate CHAR(8) DEFAULT '' NOT NULL,vchrNo CHAR(15) DEFAULT '' NOT NULL,srlNo NUMBER (4)  DEFAULT 0  NOT NULL,drCr CHAR(1) DEFAULT '',apId CHAR(2) DEFAULT '',pgrmId CHAR(8) DEFAULT '',acctCode CHAR(20) DEFAULT '',idCode CHAR(25) DEFAULT '',refNo CHAR(25) DEFAULT '',crcyUnit CHAR(10) DEFAULT '',qtyFrnAmt NUMBER (18,4)  DEFAULT 0 ,ntAmt NUMBER (18,4)  DEFAULT 0 ,dueDate CHAR(8) DEFAULT '',srlDesc VARCHAR2(500) DEFAULT '',eVchrNo CHAR(8) DEFAULT '',eAcctCode CHAR(1) DEFAULT '',eIdCode CHAR(1) DEFAULT '',eRefNo CHAR(1) DEFAULT '',eQty CHAR(1) DEFAULT '',eFrnAmt CHAR(1) DEFAULT '',eNtAmt CHAR(1) DEFAULT '',eDueDate CHAR(1) DEFAULT '',eDrCr CHAR(1) DEFAULT '',ePrvlg CHAR(1) DEFAULT '',eUnit CHAR(1) DEFAULT '',eCrcy CHAR(1) DEFAULT '',eBalance CHAR(1) DEFAULT '',tradeNo CHAR(10) DEFAULT '',eTradeNo CHAR(1) DEFAULT '',vchrType CHAR(1) DEFAULT '',eStatusCode CHAR(1) DEFAULT '',confirmUserId CHAR(10) DEFAULT '', PRIMARY KEY(compId,vchrDate,vchrNo,srlNo) 
) ;
GRANT ALL ON DB.TBAAW3 TO JAVAUSER ;
