package com.icsc.ac.bat.dtj;

import java.sql.Connection;
import java.util.*;

import com.icsc.ad.dao.*;
import com.icsc.am.amjcdei;
import com.icsc.am.dao.*;
import com.icsc.ac.dao.*;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dejc308;
import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.zafiExecMehod;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.util.zafcDE318;
import com.icsc.ip.dao.ipjcItemacctjDAO;
import com.icsc.ip.dao.ipjcItemacctjVO;

/**
 * 
 * 
 *
 * 
 * 
 * 
 */

public class acjcactransferCopy implements zafiExecMehod {
	public final static String CLASS_VERSION = "acjcactransferCopy  $Revision: 1.5 $ $Date: 2024/07/11 00:51:10 $";

	public final static String APPID = "ACtransferCOPY";

	private dsjccom dsCom;
	private Connection con;
	private dejc308 de308 = new dejc308();	
	
	private dejc301  de301bgnew=new dejc301();
	private dejc318 de318;
	//private dejc318 de318;

	private String compId;
	private String compId_bg;

	private String toYM;

	private String fromYM;

	private String msg;

	public String getMsg() {
		return msg;
	}
	
	public acjcactransferCopy(dsjccom DC) {
		dsCom = DC;
		msg = "";
	}

	public acjcactransferCopy(dsjccom DC, Connection cn) {
		this(DC);
		con = cn;
		de318 = new dejc318(dsCom, CLASS_VERSION); 
	}
	public Object run(Map para) throws Exception {
		compId_bg = "bg";
		compId = String.valueOf(para.get("COMPID"));
		fromYM = String.valueOf(para.get("FROMYM"));
		
		if (fromYM.length() != 6)
			throw new Exception("请输入参考月份");
		toYM = String.valueOf(para.get("TOYM"));
		if (toYM.length() != 6)
			throw new Exception("请输入添加月份");

		doProcess();
		return "";
	}

	private void doProcess() throws Exception {
		copyACtransfertj();
		copyACtransfertj_bg();
		copytbamS0tj();
		copytbamS0tj_bg();
		copyadtctj();
		copyadtctj_bg();
	
		//de318.log("doProcess, copy data complete, MSG: " + msg);
	}
	private void copyACtransfertj() throws Exception {
		try {
			//
			zafcCommonDAO comDAO = new zafcCommonDAO(dsCom, con);
			comDAO.updateBySQL("delete from db." + "tbactransfertj where COMPID in('bg2','bggg') and dataDate='"+ toYM + "'  ");

			acjctransfertjDAO transfertjDAO = new acjctransfertjDAO(dsCom, con);
			List adtcList = transfertjDAO.findMonthData(fromYM);
			//de318.log("copyTransfertj, doProcess " + adtcList.size() + ", SQL: " + transfertjDAO.getSql());
			de318.logs("copytransfer" , transfertjDAO.getSql()); 
			for (int i = 0; i < adtcList.size(); i++) {
				acjctransfertjVO transfertjVO = (acjctransfertjVO) adtcList.get(i);
				transfertjVO.setDataDate(toYM);
				//transfertjVO.setUpdateUserId(dsCom.user.ID);
				//transfertjVO.setUpdateDate(de308.getCrntDateWFmt1());
				transfertjDAO.create(transfertjVO);
			}
			msg += "<br>success copy copyACtransfertj" + adtcList.size() + "0";
		} catch (Exception e) {
			//de318.log("copyTransfertj, error! " + e);
			msg += "<br>fail success copy copyACtransfertj!" + e.getMessage();
			throw e;
		} 
	}
	
	private void copyACtransfertj_bg() throws Exception {
		try {			
		//		
		dejc301 de301BG = new dejc301();
		Connection conBG = de301BG.getConnection(dsCom, "bg",dsCom.appId);	
		acjctransfertjDAO transfertjDAO = new acjctransfertjDAO(dsCom, con); 
		acjctransfertjDAO transfertjDAO_bg = new acjctransfertjDAO(dsCom, conBG); 

		//��ɾ������ϵtbipAcctItemtj
		zafcCommonDAO comDAO = new zafcCommonDAO(dsCom, con);
		comDAO.updateBySQL("delete from db." + "tbactransfertj where compId='" + compId_bg + "' and DATADATE='"+ toYM + "'");
		
		List transferList = transfertjDAO_bg.findMonthData_bg(compId_bg, fromYM);		 			
		for (int i = 0; i < transferList.size(); i++) {
			acjctransfertjVO transferVO = (acjctransfertjVO) transferList.get(i);
			transferVO.setDataDate(toYM);
			int num = transfertjDAO.create(transferVO);
			//de301bgnew.commit();		
		}
		  msg += "<br>tbactransfertj" + transferList.size() + "0";		
	    } catch (Exception e) {
		//de318.logs("copyACAcctltem, error! " + e);
		msg += "<br>tbactransfertj" + e.getMessage();
		throw e;
	    } 	
	}		
  //��������ϵ��̯����߼�
	private void copytbamS0tj() throws Exception {
		try {
			
			// ��ɾ������ϵtbipAcctItemtj
			zafcCommonDAO comDAO = new zafcCommonDAO(dsCom, con);
			comDAO.updateBySQL("delete from db." + "tbamS0tj where COMPID in('bg2','bggg') and dataDate='"+ toYM + "'  ");

			amjcS0tjDAO amS0tjDAO = new amjcS0tjDAO(dsCom, con);
			List adtcList = amS0tjDAO.findMonthData(fromYM);
			//de318.log("copyTransfertj, doProcess " + adtcList.size() + ", SQL: " + transfertjDAO.getSql());
			de318.logs("copytransfer" , amS0tjDAO.getSql()); 
			for (int i = 0; i < adtcList.size(); i++) {
				amjcS0tjVO amS0tjVO = (amjcS0tjVO) adtcList.get(i);
				amS0tjVO.setDataDate(toYM);
				amS0tjVO.setCreateId(dsCom.user.ID);
				amS0tjVO.setCreateDate(de308.getCrntDateWFmt1());
				amS0tjDAO.create(amS0tjVO);
			}
			msg += "<br>tbactransfertj" + adtcList.size() + "0";
		} catch (Exception e) {
			//de318.log("copyTransfertj, error! " + e);
			msg += "<br>tbactransfertj!" + e.getMessage();
			throw e;
		} 
	}
	//��������ϵ��̯����߼�
	private void copytbamS0tj_bg() throws Exception {
		try {			
		//		
		dejc301 de301BG = new dejc301();
		Connection conBG = de301BG.getConnection(dsCom, "bg",dsCom.appId);	
		amjcS0tjDAO amS0tjDAO = new amjcS0tjDAO(dsCom, con); 
		amjcS0tjDAO amS0tjDAO_bg = new amjcS0tjDAO(dsCom, conBG); 

		// ��ɾ������ϵtbipAcctItemtj
		zafcCommonDAO comDAO = new zafcCommonDAO(dsCom, con);
		comDAO.updateBySQL("delete from db." + "tbamS0tj where compId='" + compId_bg + "' and DATADATE='"+ toYM + "'");
		
		List transferList = amS0tjDAO_bg.findMonthData_bg(compId_bg, fromYM);		 			
		for (int i = 0; i < transferList.size(); i++) {
			amjcS0tjVO amS0tjVO = (amjcS0tjVO) transferList.get(i);
			amS0tjVO.setDataDate(toYM);
			amS0tjVO.setCreateId(dsCom.user.ID);
			amS0tjVO.setCreateDate(de308.getCrntDateWFmt1());
			amS0tjDAO.create(amS0tjVO);
			//de301bgnew.commit();		
		}
		  msg += "<br>tbactransfertj" + transferList.size() + "0";		
	    } catch (Exception e) {
		//de318.logs("copyACAcctltem, error! " + e);
		msg += "<br>tbactransfertj!" + e.getMessage();
		throw e;
	    } 	
	}	
	
	//tbactransfertj
		private void copyadtctj() throws Exception {
			try {
				
				// tbactransfertj
				zafcCommonDAO comDAO = new zafcCommonDAO(dsCom, con);
				comDAO.updateBySQL("delete from db." + "tbadtctj where COMPID in('bg2','bggg')  and dataDate='"+ toYM + "'  ");

				adjctctjDAO adtctjDAO = new adjctctjDAO(dsCom, con);
				List adtcList = adtctjDAO.findMonthData(fromYM);
				//de318.log("copyTransfertj, doProcess " + adtcList.size() + ", SQL: " + transfertjDAO.getSql());
				de318.logs("copyadtctj" , adtctjDAO.getSql()); 
				for (int i = 0; i < adtcList.size(); i++) {
					adjctctjVO adtctjVO = (adjctctjVO) adtcList.get(i);
					adtctjVO.setDataDate(toYM);
					adtctjVO.setUpdateUserId(dsCom.user.ID);
					adtctjVO.setUpdateDate(de308.getCrntDateWFmt1());
					adtctjDAO.create(adtctjVO);
				}
				msg += "<br>tbactransfertj" + adtcList.size() + "0";
			} catch (Exception e) {
				//de318.log("copyTransfertj, error! " + e);
				msg += "<br>tbactransfertj!" + e.getMessage();
				throw e;
			} 
		}
		//����������Գɱ����ķ��÷�̯���õ�
		private void copyadtctj_bg() throws Exception {
			try {			
			//		
			dejc301 de301BG = new dejc301();
			Connection conBG = de301BG.getConnection(dsCom, "bg",dsCom.appId);	
			adjctctjDAO adtctjDAO = new adjctctjDAO(dsCom, con); 
			adjctctjDAO adtctjDAO_bg = new adjctctjDAO(dsCom, conBG); 

			// ��ɾ������ϵtbadtctj
			zafcCommonDAO comDAO = new zafcCommonDAO(dsCom, con);
			comDAO.updateBySQL("delete from db." + "tbadtctj where compId='" + compId_bg + "' and DATADATE='"+ toYM + "'");
			
			List transferList = adtctjDAO_bg.findMonthData_bg(compId_bg, fromYM);		 			
			for (int i = 0; i < transferList.size(); i++) {
				adjctctjVO adtctjVO = (adjctctjVO) transferList.get(i);
				adtctjVO.setDataDate(toYM);
				adtctjVO.setUpdateUserId(dsCom.user.ID);
				adtctjVO.setUpdateDate(de308.getCrntDateWFmt1());
				adtctjDAO.create(adtctjVO);
				//de301bgnew.commit();		
			}
			  msg += "<br>tbactransfertj" + transferList.size() + "��";		
		    } catch (Exception e) {
			//de318.logs("copyadtctj, error! " + e);
			msg += "<br>tbactransfertj!" + e.getMessage();
			throw e;
		    } 	
		}
		

}
