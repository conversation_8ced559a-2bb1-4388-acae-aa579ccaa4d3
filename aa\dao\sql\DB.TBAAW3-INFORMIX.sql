CREATE TABLE DB.TBAAW3 (
compId VARCHAR (10) DEFAULT '' NOT NULL,vchrDate VARCHAR (8) DEFAULT '' NOT NULL,vchrNo VARCHAR (15) DEFAULT '' NOT NULL,srlNo DECIMAL (4)  DEFAULT 0  NOT NULL,drCr VARCHAR (1) DEFAULT '',apId VARCHAR (2) DEFAULT '',pgrmId VARCHAR (8) DEFAULT '',acctCode VARCHAR (20) DEFAULT '',idCode VARCHAR (25) DEFAULT '',refNo VARCHAR (25) DEFAULT '',crcyUnit VARCHAR (10) DEFAULT '',qtyFrnAmt DECIMAL (18,4)  DEFAULT 0 ,ntAmt DECIMAL (18,4)  DEFAULT 0 ,dueDate VARCHAR (8) DEFAULT '',srlDesc LVARCHAR(500) DEFAULT '',eVchrNo VARCHAR (8) DEFAULT '',eAcctCode VARCHAR (1) DEFAULT '',eIdCode VARCHAR (1) DEFAULT '',eRefNo VARCHAR (1) DEFAULT '',eQty VARCHAR (1) DEFAULT '',eFrnAmt VARCHAR (1) DEFAULT '',eNtAmt VARCHAR (1) DEFAULT '',eDueDate VARCHAR (1) DEFAULT '',eDrCr VARCHAR (1) DEFAULT '',ePrvlg VARCHAR (1) DEFAULT '',eUnit VARCHAR (1) DEFAULT '',eCrcy VARCHAR (1) DEFAULT '',eBalance VARCHAR (1) DEFAULT '',tradeNo VARCHAR (10) DEFAULT '',eTradeNo VARCHAR (1) DEFAULT '',vchrType VARCHAR (1) DEFAULT '',eStatusCode VARCHAR (1) DEFAULT '',confirmUserId VARCHAR (10) DEFAULT '', PRIMARY KEY(compId,vchrDate,vchrNo,srlNo) 
) ;
GRANT ALL ON DB.TBAAW3 TO JAVAUSER ;
