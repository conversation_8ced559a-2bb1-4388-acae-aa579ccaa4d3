/*----------------------------------------------------------------------------*/
/* aajct1tmpVO		DAOTool Ver 10.0112 (INPUT FILE VERSION:1.0)
/*----------------------------------------------------------------------------*/
/* author : I27368
/* system : 籵頗數奪燴炵苀(AA)
/* target : 痐隴牉煦翹賜醱紫
/* create : 104/08/20
/* update : 104/08/20
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao ;

import java.io.Serializable ;
import java.math.*;
import java.sql.*;
import java.text.DecimalFormat ;
import java.util.*;
import java.util.regex.*;
import javax.servlet.http.HttpServletRequest ;

import com.icsc.dpms.de.*;
import com.icsc.aa.util.*  ;


/**
 * 痐隴牉煦翹賜醱紫 Value Object.
 * <pre>
 * Table Name        : DB.TBAAT1TMP
 * Table Description : 痐隴牉煦翹賜醱紫
 * Data Access Object: aajct1tmpDAO
 * </pre>
 * @version 籵頗數奪燴炵苀(AA)
 * @since aajct1tmpVO - 104/08/20
 * <AUTHOR>
 */
public class aajct1tmpVO extends dejcCommonVO implements Serializable {
	public final static String AppId = "AAJCT1TMPVO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:30 $";
	public final static String ATTRIB_DESC="compId=梛杶梗測鎢,vchrDate=痐,vchrNo=珛昢霜阨瘍鎢,srlNo=煦翹唗瘍,tradeNo=暮梛痐瘍鎢,drCr=質湃梗,apId=纔梛炵苀測瘍,pgrmId=纔梛最唗,acctCode=頗數褪醴,idCode=誧瘍,idDesc=誧瘍笢恅靡備,refNo=統瘍,refDesc=統瘍笢恅靡備,crcyUnit=杅講等弇/啟梗,qtyFrnAm=杅講/俋啟踢塗,ntAmt=暮梛啟踢塗,dueDate=善,srlDesc=煦翹晡猁,statusCo=袨怓,postDate=纔梛,vchrType=痐濬梗,exRate=颯薹,transTradeNo=詩薊痐瘍鎢,transVchrNo=詩薊珛昢霜阨瘍鎢," ; 

	/**
	 * 梛杶梗測鎢 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int COMPID_LEN = 10 ;
	/**
	 * 痐 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int VCHRDATE_LEN = 8 ;
	/**
	 * 珛昢霜阨瘍鎢 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int VCHRNO_LEN = 15 ;
	/**
	 * 煦翹唗瘍 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int SRLNO_LEN = 4 ;
	/**
	 * 暮梛痐瘍鎢 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int TRADENO_LEN = 15 ;
	/**
	 * 質湃梗 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int DRCR_LEN = 1 ;
	/**
	 * 纔梛炵苀測瘍 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int APID_LEN = 3 ;
	/**
	 * 纔梛最唗 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int PGRMID_LEN = 20 ;
	/**
	 * 頗數褪醴 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int ACCTCODE_LEN = 20 ;
	/**
	 * 誧瘍 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int IDCODE_LEN = 100 ;
	/**
	 * 誧瘍笢恅靡備 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int IDDESC_LEN = 100 ;
	/**
	 * 統瘍 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int REFNO_LEN = 100 ;
	/**
	 * 統瘍笢恅靡備 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int REFDESC_LEN = 100 ;
	/**
	 * 杅講等弇/啟梗 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int CRCYUNIT_LEN = 10 ;
	/**
	 * 杅講/俋啟踢塗 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int QTYFRNAM_LEN = 18 ;
	/**
	 * 暮梛啟踢塗 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int NTAMT_LEN = 18 ;
	/**
	 * 善 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int DUEDATE_LEN = 8 ;
	/**
	 * 煦翹晡猁 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int SRLDESC_LEN = 500 ;
	/**
	 * 袨怓 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int STATUSCO_LEN = 1 ;
	/**
	 * 纔梛 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int POSTDATE_LEN = 8 ;
	/**
	 * 痐濬梗 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int VCHRTYPE_LEN = 1 ;
	/**
	 * 颯薹 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int EXRATE_LEN = 18 ;
	/**
	 * 詩薊痐瘍鎢 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int TRANSTRADENO_LEN = 15 ;
	/**
	 * 詩薊珛昢霜阨瘍鎢 輸入欄位長度
	 * @since 104/08/20
	 */
	public final static int TRANSVCHRNO_LEN = 15 ;
	


/*----------------------------------------------------------------------------*/
/* DB.TBAAT1TMP column Name
/*----------------------------------------------------------------------------*/

	/**
	 * 梛杶梗測鎢
	 * @since 104/08/20
	 */
	private String compId;
	/**
	 * 痐
	 * @since 104/08/20
	 */
	private String vchrDate;
	/**
	 * 珛昢霜阨瘍鎢
	 * @since 104/08/20
	 */
	private String vchrNo;
	/**
	 * 煦翹唗瘍
	 * @since 104/08/20
	 */
	private BigDecimal srlNo;
	/**
	 * 暮梛痐瘍鎢
	 * @since 104/08/20
	 */
	private String tradeNo;
	/**
	 * 質湃梗
	 * @since 104/08/20
	 */
	private String drCr;
	/**
	 * 纔梛炵苀測瘍
	 * @since 104/08/20
	 */
	private String apId;
	/**
	 * 纔梛最唗
	 * @since 104/08/20
	 */
	private String pgrmId;
	/**
	 * 頗數褪醴
	 * @since 104/08/20
	 */
	private String acctCode;
	/**
	 * 誧瘍
	 * @since 104/08/20
	 */
	private String idCode;
	/**
	 * 誧瘍笢恅靡備
	 * @since 104/08/20
	 */
	private String idDesc;
	/**
	 * 統瘍
	 * @since 104/08/20
	 */
	private String refNo;
	/**
	 * 統瘍笢恅靡備
	 * @since 104/08/20
	 */
	private String refDesc;
	/**
	 * 杅講等弇/啟梗
	 * @since 104/08/20
	 */
	private String crcyUnit;
	/**
	 * 杅講/俋啟踢塗
	 * @since 104/08/20
	 */
	private BigDecimal qtyFrnAm;
	/**
	 * 暮梛啟踢塗
	 * @since 104/08/20
	 */
	private BigDecimal ntAmt;
	/**
	 * 善
	 * @since 104/08/20
	 */
	private String dueDate;
	/**
	 * 煦翹晡猁
	 * @since 104/08/20
	 */
	private String srlDesc;
	/**
	 * 袨怓
	 * @since 104/08/20
	 */
	private String statusCo;
	/**
	 * 纔梛
	 * @since 104/08/20
	 */
	private String postDate;
	/**
	 * 痐濬梗
	 * @since 104/08/20
	 */
	private String vchrType;
	/**
	 * 颯薹
	 * @since 104/08/20
	 */
	private BigDecimal exRate;
	/**
	 * 詩薊痐瘍鎢
	 * @since 104/08/20
	 */
	private String transTradeNo;
	/**
	 * 詩薊珛昢霜阨瘍鎢
	 * @since 104/08/20
	 */
	private String transVchrNo;
	/**
	 * 執行訊息
	 * @since 104/08/20
	 */
	private String message ;

/*----------------------------------------------------------------------------*/
/* Creates new aajct1tmpVO
/*----------------------------------------------------------------------------*/

	/**
	 * 建構子
	 * @since 104/08/20
	 */
	public aajct1tmpVO() {
		clear() ;
		super.setChild(this) ;
	}
	/**
	 * 主要鍵建構子
	 * @since 104/08/20
	 */
	public aajct1tmpVO(String compId,String vchrDate,String vchrNo,BigDecimal srlNo) {
		this() ;
		this.compId = compId ;	// 梛杶梗測鎢
		this.vchrDate = vchrDate ;	// 痐
		this.vchrNo = vchrNo ;	// 珛昢霜阨瘍鎢
		this.srlNo = srlNo ;	// 煦翹唗瘍
	}
	
	/**
	 * 將所有的欄位 reset 成預設值.
	 * <pre>
	 * 當物件資料不需再被使用時(例如物件被 delete 後仍需放一個空物件到畫面上)
	 * 可以使用此方法，好處是可以重覆使用此物件，不需再 new 一個新的
	 *</pre>
	 */
	public void clear() {
		this.compId = "" ;	// 梛杶梗測鎢
		this.vchrDate = "" ;	// 痐
		this.vchrNo = "" ;	// 珛昢霜阨瘍鎢
		this.srlNo = new BigDecimal("0") ;	// 煦翹唗瘍
		this.tradeNo = "" ;	// 暮梛痐瘍鎢
		this.drCr = "" ;	// 質湃梗
		this.apId = "" ;	// 纔梛炵苀測瘍
		this.pgrmId = "" ;	// 纔梛最唗
		this.acctCode = "" ;	// 頗數褪醴
		this.idCode = "" ;	// 誧瘍
		this.idDesc = "" ;	// 誧瘍笢恅靡備
		this.refNo = "" ;	// 統瘍
		this.refDesc = "" ;	// 統瘍笢恅靡備
		this.crcyUnit = "" ;	// 杅講等弇/啟梗
		this.qtyFrnAm = new BigDecimal("0") ;	// 杅講/俋啟踢塗
		this.ntAmt = new BigDecimal("0") ;	// 暮梛啟踢塗
		this.dueDate = "" ;	// 善
		this.srlDesc = "" ;	// 煦翹晡猁
		this.statusCo = "" ;	// 袨怓
		this.postDate = "" ;	// 纔梛
		this.vchrType = "" ;	// 痐濬梗
		this.exRate = new BigDecimal("1") ;	// 颯薹
		this.transTradeNo = "" ;	// 詩薊痐瘍鎢
		this.transVchrNo = "" ;	// 詩薊珛昢霜阨瘍鎢
		this.message = "";
	}
	/**
	 * 將自己複制一份出來
	 */
	public aajct1tmpVO myClone() {
		aajct1tmpVO aat1tmpVO = new aajct1tmpVO() ;
		aat1tmpVO.setCompId(this.compId);
		aat1tmpVO.setVchrDate(this.vchrDate);
		aat1tmpVO.setVchrNo(this.vchrNo);
		aat1tmpVO.setSrlNo(this.srlNo);
		aat1tmpVO.setTradeNo(this.tradeNo);
		aat1tmpVO.setDrCr(this.drCr);
		aat1tmpVO.setApId(this.apId);
		aat1tmpVO.setPgrmId(this.pgrmId);
		aat1tmpVO.setAcctCode(this.acctCode);
		aat1tmpVO.setIdCode(this.idCode);
		aat1tmpVO.setIdDesc(this.idDesc);
		aat1tmpVO.setRefNo(this.refNo);
		aat1tmpVO.setRefDesc(this.refDesc);
		aat1tmpVO.setCrcyUnit(this.crcyUnit);
		aat1tmpVO.setQtyFrnAm(this.qtyFrnAm);
		aat1tmpVO.setNtAmt(this.ntAmt);
		aat1tmpVO.setDueDate(this.dueDate);
		aat1tmpVO.setSrlDesc(this.srlDesc);
		aat1tmpVO.setStatusCo(this.statusCo);
		aat1tmpVO.setPostDate(this.postDate);
		aat1tmpVO.setVchrType(this.vchrType);
		aat1tmpVO.setExRate(this.exRate);
		aat1tmpVO.setTransTradeNo(this.transTradeNo);
		aat1tmpVO.setTransVchrNo(this.transVchrNo);
		return aat1tmpVO ;
	}

/**
  * toJSON 方法
  * <p>
  * @return JSON String
  * @since 104/08/20
  */
 public String toJSON() {
  StringBuffer sb = new StringBuffer() ;
  sb.append("compId :\""+dejcUtility.parseToJSON(getCompId()+"")+"\"") ;
  sb.append(",");
  sb.append("vchrDate :\""+dejcUtility.parseToJSON(getVchrDate()+"")+"\"") ;
  sb.append(",");
  sb.append("vchrNo :\""+dejcUtility.parseToJSON(getVchrNo()+"")+"\"") ;
  sb.append(",");
  sb.append("srlNo :\""+dejcUtility.parseToJSON(getSrlNo()+"")+"\"") ;
  sb.append(",");
  sb.append("tradeNo :\""+dejcUtility.parseToJSON(getTradeNo()+"")+"\"") ;
  sb.append(",");
  sb.append("drCr :\""+dejcUtility.parseToJSON(getDrCr()+"")+"\"") ;
  sb.append(",");
  sb.append("apId :\""+dejcUtility.parseToJSON(getApId()+"")+"\"") ;
  sb.append(",");
  sb.append("pgrmId :\""+dejcUtility.parseToJSON(getPgrmId()+"")+"\"") ;
  sb.append(",");
  sb.append("acctCode :\""+dejcUtility.parseToJSON(getAcctCode()+"")+"\"") ;
  sb.append(",");
  sb.append("idCode :\""+dejcUtility.parseToJSON(getIdCode()+"")+"\"") ;
  sb.append(",");
  sb.append("idDesc :\""+dejcUtility.parseToJSON(getIdDesc()+"")+"\"") ;
  sb.append(",");
  sb.append("refNo :\""+dejcUtility.parseToJSON(getRefNo()+"")+"\"") ;
  sb.append(",");
  sb.append("refDesc :\""+dejcUtility.parseToJSON(getRefDesc()+"")+"\"") ;
  sb.append(",");
  sb.append("crcyUnit :\""+dejcUtility.parseToJSON(getCrcyUnit()+"")+"\"") ;
  sb.append(",");
  sb.append("qtyFrnAm :\""+dejcUtility.parseToJSON(getQtyFrnAm()+"")+"\"") ;
  sb.append(",");
  sb.append("ntAmt :\""+dejcUtility.parseToJSON(getNtAmt()+"")+"\"") ;
  sb.append(",");
  sb.append("dueDate :\""+dejcUtility.parseToJSON(getDueDate()+"")+"\"") ;
  sb.append(",");
  sb.append("srlDesc :\""+dejcUtility.parseToJSON(getSrlDesc()+"")+"\"") ;
  sb.append(",");
  sb.append("statusCo :\""+dejcUtility.parseToJSON(getStatusCo()+"")+"\"") ;
  sb.append(",");
  sb.append("postDate :\""+dejcUtility.parseToJSON(getPostDate()+"")+"\"") ;
  sb.append(",");
  sb.append("vchrType :\""+dejcUtility.parseToJSON(getVchrType()+"")+"\"") ;
  sb.append(",");
  sb.append("exRate :\""+dejcUtility.parseToJSON(getExRate()+"")+"\"") ;
  sb.append(",");
  sb.append("transTradeNo :\""+dejcUtility.parseToJSON(getTransTradeNo()+"")+"\"") ;
  sb.append(",");
  sb.append("transVchrNo :\""+dejcUtility.parseToJSON(getTransVchrNo()+"")+"\"") ;
  return "{"+sb.toString()+"}";
 }



/*----------------------------------------------------------------------------*/
/* function methods
/*----------------------------------------------------------------------------*/

	/**
	 * 以 ResultSet 來作物件
	 * <p>
	 * @param rs - 單一 ResultSet
	 * @return aajct1tmpVO - 產生 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/08/20
	 */
	public static aajct1tmpVO getInstance(ResultSet rs) throws SQLException,Exception {
		aajct1tmpVO aat1tmpVO = new aajct1tmpVO() ;

  		aat1tmpVO.setCompId(rs.getString(1));
  		aat1tmpVO.setVchrDate(rs.getString(2));
  		aat1tmpVO.setVchrNo(rs.getString(3));
  		aat1tmpVO.setSrlNo(rs.getBigDecimal(4));
  		aat1tmpVO.setTradeNo(rs.getString(5));
  		aat1tmpVO.setDrCr(rs.getString(6));
  		aat1tmpVO.setApId(rs.getString(7));
  		aat1tmpVO.setPgrmId(rs.getString(8));
  		aat1tmpVO.setAcctCode(rs.getString(9));
  		aat1tmpVO.setIdCode(rs.getString(10));
  		aat1tmpVO.setIdDesc(rs.getString(11));
  		aat1tmpVO.setRefNo(rs.getString(12));
  		aat1tmpVO.setRefDesc(rs.getString(13));
  		aat1tmpVO.setCrcyUnit(rs.getString(14));
  		aat1tmpVO.setQtyFrnAm(rs.getBigDecimal(15));
  		aat1tmpVO.setNtAmt(rs.getBigDecimal(16));
  		aat1tmpVO.setDueDate(rs.getString(17));
  		aat1tmpVO.setSrlDesc(rs.getString(18));
  		aat1tmpVO.setStatusCo(rs.getString(19));
  		aat1tmpVO.setPostDate(rs.getString(20));
  		aat1tmpVO.setVchrType(rs.getString(21));
  		aat1tmpVO.setExRate(rs.getBigDecimal(22));
  		aat1tmpVO.setTransTradeNo(rs.getString(23));
  		aat1tmpVO.setTransVchrNo(rs.getString(24));
  		return aat1tmpVO ;
	}

	/**
	 * 以 ResultSet 來作物件
	 * <p>
	 * @param rs - 單一 ResultSet
	 * @return aajct1tmpVO - 產生 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/08/20
	 */
	public static aajct1tmpVO getInstanceByName(ResultSet rs) throws SQLException,Exception {
		return getInstanceByName(new aajct1tmpVO(), rs) ;
	}

	/**
	 * 以 ResultSet 來作物件, 使用由 user 傳進來的物件
	 * <p>
	 * @param rs - 單一 ResultSet
	 * @return aajct1tmpVO - 產生 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/08/20
	 */
	public static aajct1tmpVO getInstanceByName(Object obj, ResultSet rs) throws SQLException {
		aajct1tmpVO aat1tmpVO = (aajct1tmpVO)obj ;
  		aat1tmpVO.setCompId(rs.getString("compId"));
  		aat1tmpVO.setVchrDate(rs.getString("vchrDate"));
  		aat1tmpVO.setVchrNo(rs.getString("vchrNo"));
  		aat1tmpVO.setSrlNo(rs.getBigDecimal("srlNo"));
  		aat1tmpVO.setTradeNo(rs.getString("tradeNo"));
  		aat1tmpVO.setDrCr(rs.getString("drCr"));
  		aat1tmpVO.setApId(rs.getString("apId"));
  		aat1tmpVO.setPgrmId(rs.getString("pgrmId"));
  		aat1tmpVO.setAcctCode(rs.getString("acctCode"));
  		aat1tmpVO.setIdCode(rs.getString("idCode"));
  		aat1tmpVO.setIdDesc(rs.getString("idDesc"));
  		aat1tmpVO.setRefNo(rs.getString("refNo"));
  		aat1tmpVO.setRefDesc(rs.getString("refDesc"));
  		aat1tmpVO.setCrcyUnit(rs.getString("crcyUnit"));
  		aat1tmpVO.setQtyFrnAm(rs.getBigDecimal("qtyFrnAm"));
  		aat1tmpVO.setNtAmt(rs.getBigDecimal("ntAmt"));
  		aat1tmpVO.setDueDate(rs.getString("dueDate"));
  		aat1tmpVO.setSrlDesc(rs.getString("srlDesc"));
  		aat1tmpVO.setStatusCo(rs.getString("statusCo"));
  		aat1tmpVO.setPostDate(rs.getString("postDate"));
  		aat1tmpVO.setVchrType(rs.getString("vchrType"));
  		aat1tmpVO.setExRate(rs.getBigDecimal("exRate"));
  		aat1tmpVO.setTransTradeNo(rs.getString("transTradeNo"));
  		aat1tmpVO.setTransVchrNo(rs.getString("transVchrNo"));
  		return aat1tmpVO ;
	}

	/**
	 * 以 Request 來作物件
	 * <p>
	 * @param request - 網頁要求物件
	 * @return aajct1tmpVO - 產生 Value Object
	 * @since 104/08/20
	 */
	public static aajct1tmpVO getInstance(HttpServletRequest request) {
		return getFromReq(request, -1) ;
	}
	/**
	 * 以 Request 來作物件 (遇到多筆資料時)
	 * <p>
	 * @param request - 網頁要求物件
	 * @param index - 參數流水序號
	 * @return aajct1tmpVO - 產生 Value Object
	 * @since 104/08/20
	 */
	public static aajct1tmpVO getInstance(HttpServletRequest request, int index) {
		return getFromReq(request, index) ;
	}
	/**
	 * 以 Request 來作物件
	 * <p>
	 * @param req - 網頁要求物件
	 * @param index - 參數流水序號
	 * @return aajct1tmpVO - 產生 Value Object
	 * @since 104/08/20
	 */
	private static aajct1tmpVO getFromReq(HttpServletRequest req, int index) {
		String seq = (index >= 0) ? String.valueOf(index) : "" ;
		aajct1tmpVO aat1tmpVO = new aajct1tmpVO() ;

		aat1tmpVO.setCompId(req.getParameter("compId"+seq)) ;
		aat1tmpVO.setVchrDate(req.getParameter("vchrDate"+seq)) ;
		aat1tmpVO.setVchrNo(req.getParameter("vchrNo"+seq)) ;
		aat1tmpVO.setSrlNo(req.getParameter("srlNo"+seq)) ;
		aat1tmpVO.setTradeNo(req.getParameter("tradeNo"+seq)) ;
		aat1tmpVO.setDrCr(req.getParameter("drCr"+seq)) ;
		aat1tmpVO.setApId(req.getParameter("apId"+seq)) ;
		aat1tmpVO.setPgrmId(req.getParameter("pgrmId"+seq)) ;
		aat1tmpVO.setAcctCode(req.getParameter("acctCode"+seq)) ;
		aat1tmpVO.setIdCode(req.getParameter("idCode"+seq)) ;
		aat1tmpVO.setIdDesc(req.getParameter("idDesc"+seq)) ;
		aat1tmpVO.setRefNo(req.getParameter("refNo"+seq)) ;
		aat1tmpVO.setRefDesc(req.getParameter("refDesc"+seq)) ;
		aat1tmpVO.setCrcyUnit(req.getParameter("crcyUnit"+seq)) ;
		aat1tmpVO.setQtyFrnAm(req.getParameter("qtyFrnAm"+seq)) ;
		aat1tmpVO.setNtAmt(req.getParameter("ntAmt"+seq)) ;
		aat1tmpVO.setDueDate(req.getParameter("dueDate"+seq)) ;
		aat1tmpVO.setSrlDesc(req.getParameter("srlDesc"+seq)) ;
		aat1tmpVO.setStatusCo(req.getParameter("statusCo"+seq)) ;
		aat1tmpVO.setPostDate(req.getParameter("postDate"+seq)) ;
		aat1tmpVO.setVchrType(req.getParameter("vchrType"+seq)) ;
		aat1tmpVO.setExRate(req.getParameter("exRate"+seq)) ;
		aat1tmpVO.setTransTradeNo(req.getParameter("transTradeNo"+seq)) ;
		aat1tmpVO.setTransVchrNo(req.getParameter("transVchrNo"+seq)) ;

		return aat1tmpVO ;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/

	/**
	 * 覆寫 toString , 以利 Debug
	 * <p>
	 * @return 物件內容值
	 * @since 104/08/20
	 */
	public String toString() {
		StringBuffer sb = new StringBuffer() ;

		sb.append("<aajct1tmpVO>");
		sb.append(super.toString());
		sb.append("\r\n\tcompId = " + getCompId()) ;
		sb.append("\r\n\tvchrDate = " + getVchrDate()) ;
		sb.append("\r\n\tvchrNo = " + getVchrNo()) ;
		sb.append("\r\n\tsrlNo = " + getSrlNo()) ;
		sb.append("\r\n\ttradeNo = " + getTradeNo()) ;
		sb.append("\r\n\tdrCr = " + getDrCr()) ;
		sb.append("\r\n\tapId = " + getApId()) ;
		sb.append("\r\n\tpgrmId = " + getPgrmId()) ;
		sb.append("\r\n\tacctCode = " + getAcctCode()) ;
		sb.append("\r\n\tidCode = " + getIdCode()) ;
		sb.append("\r\n\tidDesc = " + getIdDesc()) ;
		sb.append("\r\n\trefNo = " + getRefNo()) ;
		sb.append("\r\n\trefDesc = " + getRefDesc()) ;
		sb.append("\r\n\tcrcyUnit = " + getCrcyUnit()) ;
		sb.append("\r\n\tqtyFrnAm = " + getQtyFrnAm()) ;
		sb.append("\r\n\tntAmt = " + getNtAmt()) ;
		sb.append("\r\n\tdueDate = " + getDueDate()) ;
		sb.append("\r\n\tsrlDesc = " + getSrlDesc()) ;
		sb.append("\r\n\tstatusCo = " + getStatusCo()) ;
		sb.append("\r\n\tpostDate = " + getPostDate()) ;
		sb.append("\r\n\tvchrType = " + getVchrType()) ;
		sb.append("\r\n\texRate = " + getExRate()) ;
		sb.append("\r\n\ttransTradeNo = " + getTransTradeNo()) ;
		sb.append("\r\n\ttransVchrNo = " + getTransVchrNo()) ;
		sb.append("\r\n</aajct1tmpVO>");

		return sb.toString();
	}
	/**
	 * 比較兩個物件的 Key 屬性
	 * <pre>
	 * 只比較 Key 部分，有一項 Key 不同，則 return false
	 * 並非全部的屬性都比較
	 * </pre>
	 * @param object - 欲比較之物件
	 * @return true - the same; false - diff
	 * @since 104/08/20
	 */
	public boolean keyEquals(Object object) {
		if (object instanceof aajct1tmpVO == false) {
			return false ;
		}

		aajct1tmpVO aat1tmpVO = (aajct1tmpVO) object ;
		if (this.compId.equals(aat1tmpVO.getCompId()) == false) {
			return false ;
		}
		if (this.vchrDate.equals(aat1tmpVO.getVchrDate()) == false) {
			return false ;
		}
		if (this.vchrNo.equals(aat1tmpVO.getVchrNo()) == false) {
			return false ;
		}
		if (this.srlNo.equals(aat1tmpVO.getSrlNo()) == false) {
			return false ;
		}
		return true ;
	}
	/**
	 * 檢查 key 值是否不等於 null 或 空字串
	 * @return true - ok; false - hasErr
	 * @since 104/08/20
	 */
	public boolean isKeyOk() {
		if (dejcUtility.isNull(this.compId)) {
			return hasErr("compId") ;
		}
		if (dejcUtility.isNull(this.vchrDate)) {
			return hasErr("vchrDate") ;
		}
		if (dejcUtility.isNull(this.vchrNo)) {
			return hasErr("vchrNo") ;
		}
		if (dejcUtility.isNull(this.srlNo)) {
			return hasErr("srlNo") ;
		}
		return true ;
	}
	/**
	 * 檢查資料的正確性 (包括長度，型態)
	 * @return true - ok; false - hasErr
	 * @since 104/08/20
	 */
	public boolean verify() {
		if (compId!=null) { 
			if (compId.getBytes().length>COMPID_LEN) {
				return hasErr("梛杶梗測鎢的資料長度["+compId.getBytes().length+"]不得超過["+COMPID_LEN+"]") ; 
			}
		}
		if (vchrDate!=null) { 
			if (vchrDate.length()>VCHRDATE_LEN) {
				return hasErr("痐的資料長度["+vchrDate.length()+"]不得超過["+VCHRDATE_LEN+"]") ; 
			}
		}
		if (vchrNo!=null) { 
			if (vchrNo.getBytes().length>VCHRNO_LEN) {
				return hasErr("珛昢霜阨瘍鎢的資料長度["+vchrNo.getBytes().length+"]不得超過["+VCHRNO_LEN+"]") ; 
			}
		}
		if (srlNo!=null) { 
			if (srlNo.compareTo(new BigDecimal("10000"))>=0)  {
				return hasErr("煦翹唗瘍的總額["+srlNo.toString()+"]不得超過 10000 ") ;
			}
		}

		if (tradeNo!=null) { 
			if (tradeNo.getBytes().length>TRADENO_LEN) {
				return hasErr("暮梛痐瘍鎢的資料長度["+tradeNo.getBytes().length+"]不得超過["+TRADENO_LEN+"]") ; 
			}
		}
		if (drCr!=null) { 
			if (drCr.getBytes().length>DRCR_LEN) {
				return hasErr("質湃梗的資料長度["+drCr.getBytes().length+"]不得超過["+DRCR_LEN+"]") ; 
			}
		}
		if (apId!=null) { 
			if (apId.getBytes().length>APID_LEN) {
				return hasErr("纔梛炵苀測瘍的資料長度["+apId.getBytes().length+"]不得超過["+APID_LEN+"]") ; 
			}
		}
		if (pgrmId!=null) { 
			if (pgrmId.getBytes().length>PGRMID_LEN) {
				return hasErr("纔梛最唗的資料長度["+pgrmId.getBytes().length+"]不得超過["+PGRMID_LEN+"]") ; 
			}
		}
		if (acctCode!=null) { 
			if (acctCode.getBytes().length>ACCTCODE_LEN) {
				return hasErr("頗數褪醴的資料長度["+acctCode.getBytes().length+"]不得超過["+ACCTCODE_LEN+"]") ; 
			}
		}
		if (idCode!=null) { 
			if (idCode.getBytes().length>IDCODE_LEN) {
				return hasErr("誧瘍的資料長度["+idCode.getBytes().length+"]不得超過["+IDCODE_LEN+"]") ; 
			}
		}
		if (idDesc!=null) { 
			if (idDesc.getBytes().length>IDDESC_LEN) {
				return hasErr("誧瘍笢恅靡備的資料長度["+idDesc.getBytes().length+"]不得超過["+IDDESC_LEN+"]") ; 
			}
		}
		if (refNo!=null) { 
			if (refNo.getBytes().length>REFNO_LEN) {
				return hasErr("統瘍的資料長度["+refNo.getBytes().length+"]不得超過["+REFNO_LEN+"]") ; 
			}
		}
		if (refDesc!=null) { 
			if (refDesc.getBytes().length>REFDESC_LEN) {
				return hasErr("統瘍笢恅靡備的資料長度["+refDesc.getBytes().length+"]不得超過["+REFDESC_LEN+"]") ; 
			}
		}
		if (crcyUnit!=null) { 
			if (crcyUnit.getBytes().length>CRCYUNIT_LEN) {
				return hasErr("杅講等弇/啟梗的資料長度["+crcyUnit.getBytes().length+"]不得超過["+CRCYUNIT_LEN+"]") ; 
			}
		}
		if (qtyFrnAm!=null) { 
			if (qtyFrnAm.compareTo(new BigDecimal("100000000000000"))>=0)  {
				return hasErr("杅講/俋啟踢塗的總額["+qtyFrnAm.toString()+"]不得超過 100000000000000 ") ;
			}
		}

		if (ntAmt!=null) { 
			if (ntAmt.compareTo(new BigDecimal("100000000000000"))>=0)  {
				return hasErr("暮梛啟踢塗的總額["+ntAmt.toString()+"]不得超過 100000000000000 ") ;
			}
		}

		if (dueDate!=null) { 
			if (dueDate.length()>DUEDATE_LEN) {
				return hasErr("善的資料長度["+dueDate.length()+"]不得超過["+DUEDATE_LEN+"]") ; 
			}
		}
		if (srlDesc!=null) { 
			if (srlDesc.getBytes().length>500) 
				return hasErr("煦翹晡猁的資料長度["+srlDesc.getBytes().length+"]不得超過["+SRLDESC_LEN+"]") ; 
		}
		if (statusCo!=null) { 
			if (statusCo.getBytes().length>STATUSCO_LEN) {
				return hasErr("袨怓的資料長度["+statusCo.getBytes().length+"]不得超過["+STATUSCO_LEN+"]") ; 
			}
		}
		if (postDate!=null) { 
			if (postDate.getBytes().length>POSTDATE_LEN) {
				return hasErr("纔梛的資料長度["+postDate.getBytes().length+"]不得超過["+POSTDATE_LEN+"]") ; 
			}
		}
		if (vchrType!=null) { 
			if (vchrType.getBytes().length>VCHRTYPE_LEN) {
				return hasErr("痐濬梗的資料長度["+vchrType.getBytes().length+"]不得超過["+VCHRTYPE_LEN+"]") ; 
			}
		}
		if (exRate!=null) { 
			if (exRate.compareTo(new BigDecimal("1000000000000"))>=0)  {
				return hasErr("颯薹的總額["+exRate.toString()+"]不得超過 1000000000000 ") ;
			}
		}

		if (transTradeNo!=null) { 
			if (transTradeNo.getBytes().length>TRANSTRADENO_LEN) {
				return hasErr("詩薊痐瘍鎢的資料長度["+transTradeNo.getBytes().length+"]不得超過["+TRANSTRADENO_LEN+"]") ; 
			}
		}
		if (transVchrNo!=null) { 
			if (transVchrNo.getBytes().length>TRANSVCHRNO_LEN) {
				return hasErr("詩薊珛昢霜阨瘍鎢的資料長度["+transVchrNo.getBytes().length+"]不得超過["+TRANSVCHRNO_LEN+"]") ; 
			}
		}
		return true ;
	}	

/*----------------------------------------------------------------------------*/
/* private methods
/*----------------------------------------------------------------------------*/

	/**
	 * 設定執行訊息
	 * @param msg - 訊息
	 * @return false - always false
	 */
	private boolean hasErr(String msg) {
		this.message = msg;
		return false ;
	}

	/**
	 * 以欄位名稱取出資料(像 Map 一樣，
	 * @throws RuntimeException thrown when the field not exists
	 */
	public Object get(String field) {
	    return super.get(field) ;
	}


/*----------------------------------------------------------------------------*/
/* get and set methods for the instance variables
/*----------------------------------------------------------------------------*/

	/**
	 * 回傳執行訊息
	 * @return message - 取得執行訊息
	 * @since 104/08/20
     */
	public String getMessage() {
		return this.message ;
	}
	/**
	  *側錄flag，為true時才開始側錄
	  */
	private boolean monitoring ;
	/**
	 * 編輯欄位名稱
	 */
	private Map editFields;
	/**
	 * 欄位舊資料名稱
	 */
	private Map oldFieldValues;

	/**
	  * 取得修改欄位資
	  * @return null if no editFields
	  */
	public Map getEditFields() {
		return this.editFields;
	}
	/**
	  *開始側錄 set進來的 fieldName,fieldValue
	  */
	public void monitor(){
		this.monitoring=true;
		this.editFields=new HashMap();
		this.oldFieldValues=new HashMap();
	}
	/**
	  *取消側錄
	  */
	public void stopMonitor(){
		this.monitoring=false;
	}
	/**
	  * 每個 set method 被呼叫時都會呼叫此方法
	  */
	public void onSetField(String field,Object value){
		if(this.monitoring){
			oldFieldValues.put(field, get(field));
			editFields.put(field,value);
		}
	}
	
	/** 
	 * 設定梛杶梗測鎢
	 */ 
	public void setCompId(String compId)  { 
		onSetField("compId",compId); 
		this.compId = (compId==null)?"":compId; 
	}

	/** 
	 * 取得梛杶梗測鎢
	 */ 
	public String getCompId()  { 
		return this.compId ; 
	}
	/** 
	 * 取得梛杶梗測鎢 ( 處理單引號的問題 )
	 */ 
	public String getCompIdS()  { 
		return dejcUtility.replaceS(this.compId) ; 
	}

	/** 
	 * 設定痐
	 */ 
	public void setVchrDate(String vchrDate)  { 
		onSetField("vchrDate",vchrDate); 
		this.vchrDate=vchrDate==null?"":dejcUtility.getWFmt1(vchrDate) ; 
	}

	/** 
	 * 取得痐
	 */ 
	public String getVchrDate()  { 
		return this.vchrDate ; 
	}
	/** 
	 * 取得痐的中式日期
	 */ 
	public String getVchrDateCF()  { 
		return dejcUtility.getDateF(this.vchrDate,"c") ; 
	}
	/** 
	 * 取得痐的西式日期
	 */ 
	public String getVchrDateWF()  { 
		return dejcUtility.getDateF(this.vchrDate,"w2") ; 
	}
	/** 
	 * 取得痐 ( 處理單引號的問題 )
	 */ 
	public String getVchrDateS()  { 
		return dejcUtility.replaceS(this.vchrDate) ; 
	}

	/** 
	 * 設定珛昢霜阨瘍鎢
	 */ 
	public void setVchrNo(String vchrNo)  { 
		onSetField("vchrNo",vchrNo); 
		this.vchrNo = (vchrNo==null)?"":vchrNo; 
	}

	/** 
	 * 取得珛昢霜阨瘍鎢
	 */ 
	public String getVchrNo()  { 
		return this.vchrNo ; 
	}
	/** 
	 * 取得珛昢霜阨瘍鎢 ( 處理單引號的問題 )
	 */ 
	public String getVchrNoS()  { 
		return dejcUtility.replaceS(this.vchrNo) ; 
	}

	/** 
	 * 設定煦翹唗瘍金額(拿掉逗點)
	 */ 
	public void setSrlNo(String srlNo)  { 
		if ( !dejcUtility.isNull(srlNo) ) {
			String s = dejcUtility.removeComma(srlNo.trim()) ;  
			if (!dejcUtility.isNaN(s)) {  
			    setSrlNo(new BigDecimal(s)) ; 
			    return ; 
         } 
	    }
     setSrlNo( new BigDecimal("0")) ; 
	}
	public void setSrlNo(BigDecimal srlNo)  { 
		onSetField("srlNo",srlNo); 
		this.srlNo = (srlNo==null)?new BigDecimal("0"):srlNo ;   
	}

	/** 
	 * 取得煦翹唗瘍金額
	 */ 
	public BigDecimal getSrlNo()  { 
		return this.srlNo ; 
	}
	public String getSrlNoMF()  { 
		if (this.srlNo==null) return null ;
		DecimalFormat fmt=new DecimalFormat("#,###,##0");
		return fmt.format(this.srlNo) ; 
	}
	public String getSrlNoMF(int decimal)  { 
		if (this.srlNo==null) return null ;
     StringBuffer tmp = new StringBuffer(".") ; 
     for(int i=0; i<decimal; i++)  {
         tmp.append( "0") ;
     } 
 		DecimalFormat fmt=new DecimalFormat("#,###,##0"+tmp.toString()) ;
		return fmt.format(this.srlNo) ; 
	}

	/** 
	 * 設定暮梛痐瘍鎢
	 */ 
	public void setTradeNo(String tradeNo)  { 
		onSetField("tradeNo",tradeNo); 
		this.tradeNo = (tradeNo==null)?"":tradeNo; 
	}

	/** 
	 * 取得暮梛痐瘍鎢
	 */ 
	public String getTradeNo()  { 
		return this.tradeNo ; 
	}
	/** 
	 * 取得暮梛痐瘍鎢 ( 處理單引號的問題 )
	 */ 
	public String getTradeNoS()  { 
		return dejcUtility.replaceS(this.tradeNo) ; 
	}

	/** 
	 * 設定質湃梗
	 */ 
	public void setDrCr(String drCr)  { 
		onSetField("drCr",drCr); 
		this.drCr = (drCr==null)?"":drCr; 
	}

	/** 
	 * 取得質湃梗
	 */ 
	public String getDrCr()  { 
		return this.drCr ; 
	}
	/** 
	 * 取得質湃梗 ( 處理單引號的問題 )
	 */ 
	public String getDrCrS()  { 
		return dejcUtility.replaceS(this.drCr) ; 
	}

	/** 
	 * 設定纔梛炵苀測瘍
	 */ 
	public void setApId(String apId)  { 
		onSetField("apId",apId); 
		this.apId = (apId==null)?"":apId; 
	}

	/** 
	 * 取得纔梛炵苀測瘍
	 */ 
	public String getApId()  { 
		return this.apId ; 
	}
	/** 
	 * 取得纔梛炵苀測瘍 ( 處理單引號的問題 )
	 */ 
	public String getApIdS()  { 
		return dejcUtility.replaceS(this.apId) ; 
	}

	/** 
	 * 設定纔梛最唗
	 */ 
	public void setPgrmId(String pgrmId)  { 
		onSetField("pgrmId",pgrmId); 
		this.pgrmId = (pgrmId==null)?"":pgrmId; 
	}

	/** 
	 * 取得纔梛最唗
	 */ 
	public String getPgrmId()  { 
		return this.pgrmId ; 
	}
	/** 
	 * 取得纔梛最唗 ( 處理單引號的問題 )
	 */ 
	public String getPgrmIdS()  { 
		return dejcUtility.replaceS(this.pgrmId) ; 
	}

	/** 
	 * 設定頗數褪醴
	 */ 
	public void setAcctCode(String acctCode)  { 
		onSetField("acctCode",acctCode); 
		this.acctCode = (acctCode==null)?"":acctCode; 
	}

	/** 
	 * 取得頗數褪醴
	 */ 
	public String getAcctCode()  { 
		return this.acctCode ; 
	}
	/** 
	 * 取得頗數褪醴 ( 處理單引號的問題 )
	 */ 
	public String getAcctCodeS()  { 
		return dejcUtility.replaceS(this.acctCode) ; 
	}

	/** 
	 * 設定誧瘍
	 */ 
	public void setIdCode(String idCode)  { 
		onSetField("idCode",idCode); 
		this.idCode = (idCode==null)?"":idCode; 
	}

	/** 
	 * 取得誧瘍
	 */ 
	public String getIdCode()  { 
		return this.idCode ; 
	}
	/** 
	 * 取得誧瘍 ( 處理單引號的問題 )
	 */ 
	public String getIdCodeS()  { 
		return dejcUtility.replaceS(this.idCode) ; 
	}

	/** 
	 * 設定誧瘍笢恅靡備
	 */ 
	public void setIdDesc(String idDesc)  { 
		onSetField("idDesc",idDesc); 
		this.idDesc = (idDesc==null)?"":idDesc; 
	}

	/** 
	 * 取得誧瘍笢恅靡備
	 */ 
	public String getIdDesc()  { 
		return this.idDesc ; 
	}
	/** 
	 * 取得誧瘍笢恅靡備 ( 處理單引號的問題 )
	 */ 
	public String getIdDescS()  { 
		return dejcUtility.replaceS(this.idDesc) ; 
	}

	/** 
	 * 設定統瘍
	 */ 
	public void setRefNo(String refNo)  { 
		onSetField("refNo",refNo); 
		this.refNo = (refNo==null)?"":refNo; 
	}

	/** 
	 * 取得統瘍
	 */ 
	public String getRefNo()  { 
		return this.refNo ; 
	}
	/** 
	 * 取得統瘍 ( 處理單引號的問題 )
	 */ 
	public String getRefNoS()  { 
		return dejcUtility.replaceS(this.refNo) ; 
	}

	/** 
	 * 設定統瘍笢恅靡備
	 */ 
	public void setRefDesc(String refDesc)  { 
		onSetField("refDesc",refDesc); 
		this.refDesc = (refDesc==null)?"":refDesc; 
	}

	/** 
	 * 取得統瘍笢恅靡備
	 */ 
	public String getRefDesc()  { 
		return this.refDesc ; 
	}
	/** 
	 * 取得統瘍笢恅靡備 ( 處理單引號的問題 )
	 */ 
	public String getRefDescS()  { 
		return dejcUtility.replaceS(this.refDesc) ; 
	}

	/** 
	 * 設定杅講等弇/啟梗
	 */ 
	public void setCrcyUnit(String crcyUnit)  { 
		onSetField("crcyUnit",crcyUnit); 
		this.crcyUnit = (crcyUnit==null)?"":crcyUnit; 
	}

	/** 
	 * 取得杅講等弇/啟梗
	 */ 
	public String getCrcyUnit()  { 
		return this.crcyUnit ; 
	}
	/** 
	 * 取得杅講等弇/啟梗 ( 處理單引號的問題 )
	 */ 
	public String getCrcyUnitS()  { 
		return dejcUtility.replaceS(this.crcyUnit) ; 
	}

	/** 
	 * 設定杅講/俋啟踢塗金額(拿掉逗點)
	 */ 
	public void setQtyFrnAm(String qtyFrnAm)  { 
		if ( !dejcUtility.isNull(qtyFrnAm) ) {
			String s = dejcUtility.removeComma(qtyFrnAm.trim()) ;  
			if (!dejcUtility.isNaN(s)) {  
			    setQtyFrnAm(new BigDecimal(s)) ; 
			    return ; 
         } 
	    }
     setQtyFrnAm( new BigDecimal("0")) ; 
	}
	public void setQtyFrnAm(BigDecimal qtyFrnAm)  { 
		onSetField("qtyFrnAm",qtyFrnAm); 
		this.qtyFrnAm = (qtyFrnAm==null)?new BigDecimal("0"):qtyFrnAm ;   
	}

	/** 
	 * 取得杅講/俋啟踢塗金額
	 */ 
	public BigDecimal getQtyFrnAm()  { 
		return this.qtyFrnAm ; 
	}
	public String getQtyFrnAmMF()  { 
		if (this.qtyFrnAm==null) return null ;
		DecimalFormat fmt=new DecimalFormat("#,###,##0.0000");
		return fmt.format(this.qtyFrnAm) ; 
	}
	public String getQtyFrnAmMF(int decimal)  { 
		if (this.qtyFrnAm==null) return null ;
     StringBuffer tmp = new StringBuffer(".") ; 
     for(int i=0; i<decimal; i++)  {
         tmp.append( "0") ;
     } 
 		DecimalFormat fmt=new DecimalFormat("#,###,##0"+tmp.toString()) ;
		return fmt.format(this.qtyFrnAm) ; 
	}

	/** 
	 * 設定暮梛啟踢塗金額(拿掉逗點)
	 */ 
	public void setNtAmt(String ntAmt)  { 
		if ( !dejcUtility.isNull(ntAmt) ) {
			String s = dejcUtility.removeComma(ntAmt.trim()) ;  
			if (!dejcUtility.isNaN(s)) {  
			    setNtAmt(new BigDecimal(s)) ; 
			    return ; 
         } 
	    }
     setNtAmt( new BigDecimal("0")) ; 
	}
	public void setNtAmt(BigDecimal ntAmt)  { 
		onSetField("ntAmt",ntAmt); 
		this.ntAmt = (ntAmt==null)?new BigDecimal("0"):ntAmt ;   
	}

	/** 
	 * 取得暮梛啟踢塗金額
	 */ 
	public BigDecimal getNtAmt()  { 
		return this.ntAmt ; 
	}
	public String getNtAmtMF()  { 
		if (this.ntAmt==null) return null ;
		DecimalFormat fmt=new DecimalFormat("#,###,##0.0000");
		return fmt.format(this.ntAmt) ; 
	}
	public String getNtAmtMF(int decimal)  { 
		if (this.ntAmt==null) return null ;
     StringBuffer tmp = new StringBuffer(".") ; 
     for(int i=0; i<decimal; i++)  {
         tmp.append( "0") ;
     } 
 		DecimalFormat fmt=new DecimalFormat("#,###,##0"+tmp.toString()) ;
		return fmt.format(this.ntAmt) ; 
	}

	/** 
	 * 設定善
	 */ 
	public void setDueDate(String dueDate)  { 
		onSetField("dueDate",dueDate); 
		this.dueDate=dueDate==null?"":dejcUtility.getWFmt1(dueDate) ; 
	}

	/** 
	 * 取得善
	 */ 
	public String getDueDate()  { 
		return this.dueDate ; 
	}
	/** 
	 * 取得善的中式日期
	 */ 
	public String getDueDateCF()  { 
		return dejcUtility.getDateF(this.dueDate,"c") ; 
	}
	/** 
	 * 取得善的西式日期
	 */ 
	public String getDueDateWF()  { 
		return dejcUtility.getDateF(this.dueDate,"w2") ; 
	}
	/** 
	 * 取得善 ( 處理單引號的問題 )
	 */ 
	public String getDueDateS()  { 
		return dejcUtility.replaceS(this.dueDate) ; 
	}

	/** 
	 * 設定煦翹晡猁
	 */ 
	public void setSrlDesc(String srlDesc)  { 
		this.srlDesc = (srlDesc==null)?"":srlDesc  ; 
	}

	/** 
	 * 取得煦翹晡猁
	 */ 
	public String getSrlDesc()  { 
		return this.srlDesc ; 
	}
	/** 
	 * 取得煦翹晡猁 ( 處理單引號的問題 )
	 */ 
	public String getSrlDescS()  { 
		return dejcUtility.replaceS(this.srlDesc) ; 
	}

	/** 
	 * 設定袨怓
	 */ 
	public void setStatusCo(String statusCo)  { 
		onSetField("statusCo",statusCo); 
		this.statusCo = (statusCo==null)?"":statusCo; 
	}

	/** 
	 * 取得袨怓
	 */ 
	public String getStatusCo()  { 
		return this.statusCo ; 
	}
	/** 
	 * 取得袨怓 ( 處理單引號的問題 )
	 */ 
	public String getStatusCoS()  { 
		return dejcUtility.replaceS(this.statusCo) ; 
	}

	/** 
	 * 設定纔梛
	 */ 
	public void setPostDate(String postDate)  { 
		onSetField("postDate",postDate); 
		this.postDate = (postDate==null)?"":postDate; 
	}

	/** 
	 * 取得纔梛
	 */ 
	public String getPostDate()  { 
		return this.postDate ; 
	}
	/** 
	 * 取得纔梛 ( 處理單引號的問題 )
	 */ 
	public String getPostDateS()  { 
		return dejcUtility.replaceS(this.postDate) ; 
	}

	/** 
	 * 設定痐濬梗
	 */ 
	public void setVchrType(String vchrType)  { 
		onSetField("vchrType",vchrType); 
		this.vchrType = (vchrType==null)?"":vchrType; 
	}

	/** 
	 * 取得痐濬梗
	 */ 
	public String getVchrType()  { 
		return this.vchrType ; 
	}
	/** 
	 * 取得痐濬梗 ( 處理單引號的問題 )
	 */ 
	public String getVchrTypeS()  { 
		return dejcUtility.replaceS(this.vchrType) ; 
	}

	/** 
	 * 設定颯薹金額(拿掉逗點)
	 */ 
	public void setExRate(String exRate)  { 
		if ( !dejcUtility.isNull(exRate) ) {
			String s = dejcUtility.removeComma(exRate.trim()) ;  
			if (!dejcUtility.isNaN(s)) {  
			    setExRate(new BigDecimal(s)) ; 
			    return ; 
         } 
	    }
     setExRate( new BigDecimal("1")) ; 
	}
	public void setExRate(BigDecimal exRate)  { 
		onSetField("exRate",exRate); 
		this.exRate = (exRate==null)?new BigDecimal("1"):exRate ;   
	}

	/** 
	 * 取得颯薹金額
	 */ 
	public BigDecimal getExRate()  { 
		return this.exRate ; 
	}
	public String getExRateMF()  { 
		if (this.exRate==null) return null ;
		DecimalFormat fmt=new DecimalFormat("#,###,##0.000000");
		return fmt.format(this.exRate) ; 
	}
	public String getExRateMF(int decimal)  { 
		if (this.exRate==null) return null ;
     StringBuffer tmp = new StringBuffer(".") ; 
     for(int i=0; i<decimal; i++)  {
         tmp.append( "0") ;
     } 
 		DecimalFormat fmt=new DecimalFormat("#,###,##0"+tmp.toString()) ;
		return fmt.format(this.exRate) ; 
	}

	/** 
	 * 設定詩薊痐瘍鎢
	 */ 
	public void setTransTradeNo(String transTradeNo)  { 
		onSetField("transTradeNo",transTradeNo); 
		this.transTradeNo = (transTradeNo==null)?"":transTradeNo; 
	}

	/** 
	 * 取得詩薊痐瘍鎢
	 */ 
	public String getTransTradeNo()  { 
		return this.transTradeNo ; 
	}
	/** 
	 * 取得詩薊痐瘍鎢 ( 處理單引號的問題 )
	 */ 
	public String getTransTradeNoS()  { 
		return dejcUtility.replaceS(this.transTradeNo) ; 
	}

	/** 
	 * 設定詩薊珛昢霜阨瘍鎢
	 */ 
	public void setTransVchrNo(String transVchrNo)  { 
		onSetField("transVchrNo",transVchrNo); 
		this.transVchrNo = (transVchrNo==null)?"":transVchrNo; 
	}

	/** 
	 * 取得詩薊珛昢霜阨瘍鎢
	 */ 
	public String getTransVchrNo()  { 
		return this.transVchrNo ; 
	}
	/** 
	 * 取得詩薊珛昢霜阨瘍鎢 ( 處理單引號的問題 )
	 */ 
	public String getTransVchrNoS()  { 
		return dejcUtility.replaceS(this.transVchrNo) ; 
	}

	/**
	  *以欄位英文名稱取出中文名稱
	  */	
	public String getFieldDesc(String fieldName){
		Matcher matcher = Pattern.compile(fieldName+"=[^,]+").matcher(ATTRIB_DESC);
		if(matcher.find()){
			return matcher.group().replaceAll(fieldName+"=", "");
		}
		return null;
	}
	public String[] getAllFieldNames(){
		return ATTRIB_DESC.split("=[^,]+,");
	}
	/**
	 *是否有編輯過的欄位
	 */
	public boolean hasEditFields() {
		return getEditFields()!=null && !getEditFields().isEmpty();
	}
  	/**
	 * 列出所有被修改過的欄位名稱
	 * @return
	 */
	public String[] listEditFieldNames() {
		if(!hasEditFields()) {
			return new String[0];
		}
		Set keySet = getEditFields().keySet();
		String[] fieldNames=new String[keySet.size()];
		keySet.toArray(fieldNames);
		return fieldNames;
	}
	/**
	 * 取出修改前的資料
	 * @param field
	 * @return
	 */
	public Object getOldFieldValue(String field) {
		if(oldFieldValues==null) {
			return null ;
		}
		return oldFieldValues.get(field);
	}
	/**
	 * 取出所有 KEY 欄位
	 * @param field
	 * @return
	 */	
	public String[] getKeys() {
	    String key = "compId,vchrDate,vchrNo,srlNo," ;
	    if(!key.equals("")){
	    	return key.substring(0,key.length()-1).split(",");
	    }else{
	    	return new String[0];
	    }
    	}
 

/*----------------------------------------------------------------------------*/
/* Customize methods
/*----------------------------------------------------------------------------*/

//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

	/** 
	 * validate every attribute (ex. empty, date format...)
	 * method need to be implemented..
	 */ 
	public dejcVoValidater validate() { 
		dejcVoValidater v = null ; 
		 // v = new dejcVoValidater(this); 
		 // validating logic here...
		return v ; 
	} ; 


} // end of Class aajct1tmpDAO