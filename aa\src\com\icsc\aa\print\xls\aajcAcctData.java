package com.icsc.aa.print.xls;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.icsc.aa.util.aajcUtil;

public class aajcAcctData {
	public BigDecimal bgnAmt;
	public BigDecimal bgnDrAmt;
	public BigDecimal bgnCrAmt;

	public BigDecimal drAmt;
	public BigDecimal crAmt;
	public BigDecimal yearDrAmt;
	public BigDecimal yearCrAmt;
	public BigDecimal endAmt;
	public BigDecimal endDrAmt;
	public BigDecimal endCrAmt;

	public BigDecimal bgnQty;
	public BigDecimal bgnDrQty;
	public BigDecimal bgnCrQty;

	public BigDecimal drQty;
	public BigDecimal crQty;
	public BigDecimal yearDrQty;
	public BigDecimal yearCrQty;
	public BigDecimal endQty;
	public BigDecimal endDrQty;
	public BigDecimal endCrQty;
	
	public aajcAcctData() {
		bgnAmt = new BigDecimal("0");
	    bgnDrAmt = new BigDecimal("0");
	    bgnCrAmt = new BigDecimal("0");
	    
	    drAmt	=	new	BigDecimal("0");	
	    crAmt	 =	new	BigDecimal("0");
	    yearDrAmt	=	new	BigDecimal("0");
	    yearCrAmt	=	new	BigDecimal("0");
	    endAmt	=	new	BigDecimal("0");	
	    endDrAmt	=	new	BigDecimal("0");	
	    endCrAmt	=	new	BigDecimal("0");	
	    				
	    bgnQty	=	new	BigDecimal("0");	
	    bgnDrQty	=	new	BigDecimal("0");	
	    bgnCrQty	=	new	BigDecimal("0");	
	    
	    drQty	=	new	BigDecimal("0");	
	    crQty	=	new	BigDecimal("0");	
	    yearDrQty	=	new	BigDecimal("0");
	    yearCrQty	=	new	BigDecimal("0");
	    endQty	=	new	BigDecimal("0");	
	    endDrQty	=	new	BigDecimal("0");	
	    endCrQty	=	new	BigDecimal("0");	
	}
	
	public void add(aajcAcctData src) {
		bgnAmt	=	bgnAmt.add(src.bgnAmt);
		bgnDrAmt	=	bgnDrAmt.add(src.bgnDrAmt);
		bgnCrAmt	=	bgnCrAmt.add(src.bgnCrAmt);
		drAmt	=	drAmt.add(src.drAmt);
		crAmt	=	crAmt.add(src.crAmt);
		endAmt	=	endAmt.add(src.endAmt);
		endDrAmt	=	endDrAmt.add(src.endDrAmt);
		endCrAmt	=	endCrAmt.add(src.endCrAmt);
				
		bgnQty	=	bgnQty.add(src.bgnQty);
		bgnDrQty	=	bgnDrQty.add(src.bgnDrQty);
		bgnCrQty	=	bgnCrQty.add(src.bgnCrQty);
		drQty	=	drQty.add(src.drQty);
		crQty	=	crQty.add(src.crQty);
		endQty	=	endQty.add(src.endQty);
		endDrQty	=	endDrQty.add(src.endDrQty);
		endCrQty	=	endCrQty.add(src.endCrQty);
				
		yearDrAmt	=	yearDrAmt.add(src.yearDrAmt);
		yearCrAmt	=	yearCrAmt.add(src.yearCrAmt);
		yearDrQty	=	yearDrQty.add(src.yearDrQty);
		yearCrQty	=	yearCrQty.add(src.yearCrQty);
	}
    
	public Map<String, String> getMap() {
    	Map<String, String> map = new HashMap<String, String>();
        map.put("BGNQTY", formatQty(bgnQty));
        map.put("BGNDRQTY", formatQty(bgnDrQty));
        map.put("BGNCRQTY", formatQty(bgnCrQty));

        map.put("BGNAMT", formatAmt(bgnAmt));
        map.put("BGNDRAMT", formatAmt(bgnDrAmt));
        map.put("BGNCRAMT", formatAmt(bgnCrAmt));

        map.put("DRQTY", formatQty(drQty));
        map.put("DRAMT", formatAmt(drAmt));

        map.put("CRQTY", formatQty(crQty));
        map.put("CRAMT", formatAmt(crAmt));

        map.put("YEARDRQTY", formatQty(yearDrQty));
        map.put("YEARDRAMT", formatAmt(yearDrAmt));
        map.put("YEARCRQTY", formatQty(yearCrQty));
        map.put("YEARCRAMT", formatAmt(yearCrAmt));

        map.put("ENDQTY", formatQty(endQty));
        map.put("ENDAMT", formatAmt(endAmt));
        map.put("ENDDRQTY", formatQty(endDrQty));
        map.put("ENDDRAMT", formatAmt(endDrAmt));
        map.put("ENDCRQTY", formatQty(endCrQty));
        map.put("ENDCRAMT", formatAmt(endCrAmt));
        
        return map;
    }
	
	public Map<String, String> getTotalMap() {
    	Map<String, String> map = new HashMap<String, String>();
    	map.put("bgnDrQtyTotal", bgnDrQty.toString());
    	map.put("bgnDrAmtTotal", bgnDrAmt.toString());
    	map.put("bgnCrQtyTotal", bgnCrQty.toString());
    	map.put("bgnCrAmtTotal", bgnCrAmt.toString());
    	map.put("drQtyTotal", drQty.toString());
        map.put("drAmtTotal", drAmt.toString());
        map.put("crQtyTotal", crQty.toString());
        map.put("crAmtTotal", crAmt.toString());
        map.put("yearDrQtyTotal", yearDrQty.toString());
        map.put("yearDrAmtTotal", yearDrAmt.toString());
        map.put("yearCrQtyTotal", yearCrQty.toString());
        map.put("yearCrAmtTotal", yearCrAmt.toString());
        map.put("endDrQtyTotal", endDrQty.toString());
        map.put("endDrAmtTotal", endDrAmt.toString());
        map.put("endCrQtyTotal", endCrQty.toString());
        map.put("endCrAmtTotal", endCrAmt.toString());	
        return map;
    }

    protected String formatAmt(BigDecimal amt) {
        if (amt.compareTo(new BigDecimal("0")) == 0) {
            return "";
        }
        NumberFormat formater = new DecimalFormat("#,##0.00");
        String amtString = formater.format(amt);

        return amtString;
    }

    protected String formatQty(BigDecimal amt) {
        if (amt.compareTo(new BigDecimal("0")) == 0) {
            return "";
        }
        NumberFormat formater = new DecimalFormat("#,##0.0000");
        String amtString = formater.format(amt);

        return amtString;
    }
    
    public boolean isZeroData() {    	
                                               	    
    	return bgnAmt.compareTo(BigDecimal.ZERO) == 0 && drAmt.compareTo(BigDecimal.ZERO) == 0
                && crAmt.compareTo(BigDecimal.ZERO) == 0 && yearDrAmt.compareTo(BigDecimal.ZERO) == 0
                && yearCrAmt.compareTo(BigDecimal.ZERO) == 0 && bgnQty.compareTo(BigDecimal.ZERO) == 0
                && drQty.compareTo(BigDecimal.ZERO) == 0 && crQty.compareTo(BigDecimal.ZERO) == 0
                && yearDrQty.compareTo(BigDecimal.ZERO) == 0 && yearCrQty.compareTo(BigDecimal.ZERO) == 0
                && bgnDrAmt.compareTo(BigDecimal.ZERO) == 0 && bgnCrAmt.compareTo(BigDecimal.ZERO) == 0
                && endDrAmt.compareTo(BigDecimal.ZERO) == 0 && endCrAmt.compareTo(BigDecimal.ZERO) == 0
                && bgnDrQty.compareTo(BigDecimal.ZERO) == 0 && bgnCrQty.compareTo(BigDecimal.ZERO) == 0
                && endDrQty.compareTo(BigDecimal.ZERO) == 0 && endCrQty.compareTo(BigDecimal.ZERO) == 0;	
    }
}
