/*----------------------------------------------------------------------------*/
/* aajccpLoginDDAO		DAOTool Ver 10.0112 (INPUT FILE VERSION:2.0)
/*----------------------------------------------------------------------------*/
/* author : InfoChamp
/* system : ���q�|�p�޲z�t��(AA)
/* target : �Τ���v�b�<PERSON>�μt�O���@�@�~�򥻸�Ʃ��Ӥ��
/* create : 104/09/07
/* update : 104/09/07
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao;

import java.sql.*;
import java.text.*;
import java.util.*;
import java.math.* ;
import com.icsc.dpms.de.*;
import com.icsc.dpms.de.sql.*;
import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;

/**
 * �Τ���v�b�M�O�μt�O���@�@�~�򥻸�Ʃ��Ӥ�� DAO.
 * <pre>
 * Table Name        : DB.TBAACPLOGIND
 * Table Description : �Τ���v�b�M�O�μt�O���@�@�~�򥻸�Ʃ��Ӥ��
 * Value Object Name : aajccpLoginDVO
 * </pre>
 * @version $Id: aajccpLoginDDAO_1.java,v 1.1 2015/09/30 05:13:30 I27368 Exp $
 * @since aajccpLoginDVO - 104/09/07
 * <AUTHOR>
 */
public class aajccpLoginDDAO extends dejcCommonDAO {
	public final static String AppId = "AAJCCPLOGINDDAO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:30 $";

/*----------------------------------------------------------------------------*/
/* Creates new aajccpLoginDDAO
/*----------------------------------------------------------------------------*/

	/**
	 * ������
	 * @param dsCom - ����Ԫ��
	 * @since 104/09/07
	 */
	public aajccpLoginDDAO(dsjccom dsCom) {
		super(dsCom, AppId);
	}
	/**
	 * ������
	 * @param dsCom - ����Ԫ��
	 * @param con - ����������
	 * @since 104/09/07
	 */
	public aajccpLoginDDAO(dsjccom dsCom, Connection con) {
		super(dsCom, con) ;
		super.appId = this.AppId;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/
	/**
	 * �����������ѯ�� sql
	 * @since 104/09/07
	 */
	private String getFindByPKSql(String userId,String compId,String factoryId) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId='"+userId+"' and compId='"+compId+"' and factoryId='"+factoryId+"' ");
		return sqlStr.toString();
	}
	/**
	 * ����������ѯ����
	 * <p>
	 * @return aacpLoginDVO - ��������
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public aajccpLoginDVO findByPK(String userId,String compId,String factoryId)
			throws SQLException, Exception {
		this.sql = getFindByPKSql(userId,compId,factoryId) ;
		return (aajccpLoginDVO) this.eQuery(this.sql) ;
	}

	/**
	 * ����������ѯ����
	 * <p>
	 * @return aacpLoginDVO - ��������
	 * @exception dejcNotFoundException - ���鲻������ʱ
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public aajccpLoginDVO findByPKExp(String userId,String compId,String factoryId)
			throws dejcNotFoundException, SQLException, Exception {
		aajccpLoginDVO aacpLoginDVO = findByPK(userId,compId,factoryId) ;
		if (aacpLoginDVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginDVO ;
	}

	/**
	 * ����������ѯ����
	 * <p>
	 * @param aacpLoginDVO - ��ʹ�������ṩ�� object, DAO ������ new
	 * @return aacpLoginDVO - ��������
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public aajccpLoginDVO findByPK(aajccpLoginDVO aacpLoginDVO, String userId,String compId,String factoryId)
			throws SQLException,Exception {
		this.sql = getFindByPKSql(userId,compId,factoryId) ;
		return (aajccpLoginDVO) this.eQuery(aacpLoginDVO, this.sql) ;
	}

	/**
	 * ����������ѯ����
	 * <p>
	 * @param aacpLoginDVO - ��ʹ�������ṩ�� object, DAO ������ new
	 * @return aacpLoginDVO - ��������
	 * @exception dejcNotFoundException - ���鲻������ʱ
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public aajccpLoginDVO findByPKExp(aajccpLoginDVO aacpLoginDVO, String userId,String compId,String factoryId)
			throws dejcNotFoundException, SQLException, Exception {
		aacpLoginDVO = findByPK(aacpLoginDVO, userId,compId,factoryId) ;
		if (aacpLoginDVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginDVO ;
	}

	/**
	 * ���ϲ�ѯ����
	 * <p>
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public Vector findByMasterKey(String userId,String compId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId='"+userId+"' and compId='"+compId+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}

	/**
	 * ���ϲ�ѯ����
	 * <p>
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public Vector findByMasterKey(String userId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId='"+userId+"' ");
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}
	/**
	 * ����һ������ throws
	 * <p>
	 * @param aacpLoginDVO - Value Object
	 * @return int - ���ױ���
	 * @exception dejcEditException - Value Object ��������
	 * @exception dejcNoUpdateException - ���κ���������
	 * @exception dejcDupException - �ظ���ֵ
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int create(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		if (aacpLoginDVO.verify() == false) {
			throw new dejcEditException(aacpLoginDVO.getMessage()) ;
		}
		if (aacpLoginDVO.isKeyOk() == false) {
			throw new dejcEditException("Value of key["+AppId+"].["+aacpLoginDVO.getMessage()+"] is null or empty!") ;
		}

		
		this.sql = getCreateSql(aacpLoginDVO) ;

		try {
			int rslt = this.executeUpdate(this.sql) ;

			if (rslt == 0) {
				throw new dejcNoUpdateException(this.sql) ;
			}
			return rslt;
		} catch (SQLException sqle) {
            handleDupException(sqle);
            return -1;
		}
	}
	
	public String getCreateSql(aajccpLoginDVO aacpLoginDVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGIND (");
		sqlStr.append("userId,compId,factoryId");
		sqlStr.append(") VALUES (");
		sqlStr.append("'").append(aacpLoginDVO.getUserIdS()).append("','").append(aacpLoginDVO.getCompIdS()).append("','").append(aacpLoginDVO.getFactoryIdS()).append("'");
		sqlStr.append(")");	
		this.sql = sqlStr.toString() ;
		return this.sql;
	}

	/**
	 * ����������� throws
	 * <p>
	 * @param aacpLoginDVOList - Value Object
	 * @return int - ���ױ���
	 * @exception dejcEditException - Value Object ��������
	 * @exception dejcNoUpdateException - ���κ���������
	 * @exception dejcDupException - �ظ���ֵ
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int createList(List aacpLoginDVOList)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			count = count+this.create(aacpLoginDVO) ;
		}
		return count ;
	}

  	/**
	 * ɾ������
	 * <p>
	 * @return int - ���ױ���
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int remove(String userId,String compId,String factoryId)
			throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId='"+userId+"' and compId='"+compId+"' and factoryId='"+factoryId+"' " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	

	/**
	 * ɾ������ throws
	 * <p>
	 * @return int - ���ױ���
	 * @exception dejcNoUpdateException - ���κ����ϱ�ɾ��
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int removeExp(String userId,String compId,String factoryId)
			throws dejcNoUpdateException, SQLException, Exception {
		int rslt = remove(userId,compId,factoryId) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(this.sql) ;
		}
		return rslt ;
	}

	/**
	 * ɾ������ throws
	 * <p>
	 * @param aacpLoginDVO ��ɾ�����������
	 * @return int - ���ױ���
	 * @exception dejcNoUpdateException - ���κ����ϱ�ɾ��
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int removeExp(aajccpLoginDVO aacpLoginDVO)
			throws dejcNoUpdateException, SQLException, Exception {
		return removeExp(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId()) ;
	}

	/**
	 * ɾ��������� throws
	 * <p>
	 * @param aacpLoginDVOList ��ɾ�����������
	 * @return int - ���ױ���
	 * @exception dejcNoUpdateException - ���κ����ϱ�ɾ��
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int removeList(List aacpLoginDVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			count = count+this.remove(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId()) ;
		}
		return count ;
	}

	/**
	 * ɾ������ throws
	 * <p>
	 * @param aacpLoginDVO ��ɾ�����������
	 * @return int - ���ױ���
	 * @exception dejcNoUpdateException - ���κ����ϱ�ɾ��
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int remove(aajccpLoginDVO aacpLoginDVO)
			throws SQLException, Exception {
		return remove(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId()) ;
	}

	/**
	 * �޸�����
	 * <p>
	 * @return int - ���ױ���
	 * @exception dejcEditException - Value Object ��������
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int update(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginDVO.verify() == false) {
			throw new dejcEditException(aacpLoginDVO.getMessage()) ;
		}
		if(aacpLoginDVO.hasEditFields()) {
			return updateEditFields(aacpLoginDVO);
		} else {
			return this.executeUpdate(getUpdateSql(aacpLoginDVO) ) ;
		}
	}

	public String getUpdateSql(aajccpLoginDVO aacpLoginDVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGIND");
		sqlStr.append(" SET  ");
		sqlStr.append(" WHERE  userId='"+aacpLoginDVO.getUserId()+"' and compId='"+aacpLoginDVO.getCompId()+"' and factoryId='"+aacpLoginDVO.getFactoryId()+"' ");
		this.sql = sqlStr.toString();
		return this.sql ;	
	}
	
	/**
	 * ��������Ԥ��ֵ��ͬ����Ҫ�޸ĸ���λ
	 * <p>
	 * @return int - ���ױ���
	 * @exception dejcEditException - Value Object ��������
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int updateFldsNotDef(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginDVO.verify() == false) {
			throw new dejcEditException(aacpLoginDVO.getMessage()) ;
		}
		if (aacpLoginDVO.isKeyOk() == false) {
			throw new dejcEditException("primary key["+aacpLoginDVO.getMessage()+"] of ["+AppId+"] is empty!") ;
		}		
		StringBuffer updateFlds = new StringBuffer();
		if(updateFlds.length()==0) {
			return 0;
		}	
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGIND");
		sqlStr.append(" SET ").append( updateFlds.substring(0, updateFlds.length()-1) );
		sqlStr.append(" WHERE  userId='"+aacpLoginDVO.getUserId()+"' and compId='"+aacpLoginDVO.getCompId()+"' and factoryId='"+aacpLoginDVO.getFactoryId()+"' ");
		this.sql = sqlStr.toString();
		return this.executeUpdate(this.sql) ;
	}


	/**
	 * �޸����� throws
	 * <p>
	 * @return int - ���ױ���
	 * @exception dejcEditException - Value Object ��������
	 * @exception dejcNoUpdateException - ���κ������޸�
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int updateExp(aajccpLoginDVO aacpLoginDVO)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int rslt = update(aacpLoginDVO) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(sql) ;
		}
		return rslt ;
	}

	/**
	 * �޸�-�����������, ����޸� 0 �ʵĻ����������ñ�����
	 * <p>
	 * @return int - ���ױ���
	 * @exception dejcEditException - Value Object ��������
	 * @exception dejcNoUpdateException - ���κ������޸�
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int updateCreateList(List aacpLoginDVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			int updateCount = update(aacpLoginDVO) ;
			if ( updateCount==0 ) {
				count += create(aacpLoginDVO) ;
			} else {
				count += updateCount ;
			}
		}
		return count ;
	}

	/**
	 * �޸Ķ������ throws
	 * <p>
	 * @param aacpLoginDVOList ���޸ĵ��������
	 * @return int - ���ױ���
	 * @exception dejcNoUpdateException - ���κ����ϱ��޸�
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	public int updateList(List aacpLoginDVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			count = count+this.updateExp(aacpLoginDVO) ;
		}
		return count ;
	}

	/**
	 * ִ�� addCreateBatch(Object obj) ʱ��Ҫ�õ��� sql
	 * �������ϵ� prepareStatement sql<br>
	 * �˷����Ǹ�д commonDAO �ķ���
	 */
    protected String getCreatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGIND  (");
		sqlStr.append("userId,compId,factoryId");
		sqlStr.append(") VALUES (");
		sqlStr.append("?,?,?");
		sqlStr.append(")");		    
		return sqlStr.toString();
    }

	/**
	 * ִ�� addupdateBatch(Object obj) ʱ��Ҫ�õ��� sql
	 * �޸����ϵ� prepareStatement sql<br>
	 * �˷����Ǹ�д commonDAO �ķ���
	 */
    protected String getUpdatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGIND");
		sqlStr.append(" SET  ");
		sqlStr.append(" WHERE  userId=? and compId=? and factoryId=? ");
		return sqlStr.toString();
    }

	/**
	 * ִ�� addDeleteBatch(Object obj) ʱ��Ҫ�õ��� sql
	 * ɾ�����ϵ� prepareStatement sql<br>
	 * �˷����Ǹ�д commonDAO �ķ���
	 */
    protected String getDeletePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId=? and compId=? and factoryId=? " );
		return sqlStr.toString();
    }

	/**
	 * ִ�� addCreateBatch(Object obj) ʱ��Ҫ���еķ���
	 */
    protected void prepareCreate(Object obj) throws SQLException {
    	aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
		pstmt.setString(1, aacpLoginDVO.getUserId()) ;
		pstmt.setString(2, aacpLoginDVO.getCompId()) ;
		pstmt.setString(3, aacpLoginDVO.getFactoryId()) ;
    }

	/**
	 * ִ�� addUpdateBatch(Object obj) ʱ��Ҫ���еķ���
	 */
    protected void prepareUpdate(Object obj) throws SQLException {
    	aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
		pstmt.setString(1, aacpLoginDVO.getUserId()) ;
		pstmt.setString(2, aacpLoginDVO.getCompId()) ;
		pstmt.setString(3, aacpLoginDVO.getFactoryId()) ;
    }

	/**
	 * ִ�� addDeleteBatch(Object obj) ʱ��Ҫ���еķ���
	 */
    protected void prepareDelete(Object obj) throws SQLException {
    	aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
		pstmt.setString(1, aacpLoginDVO.getUserId()) ;
		pstmt.setString(2, aacpLoginDVO.getCompId()) ;
		pstmt.setString(3, aacpLoginDVO.getFactoryId()) ;
    }

	/**
	 * ʵ�������
	 * <p>
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	protected Object getObjFromRS(ResultSet rs) throws SQLException,Exception {
		return aajccpLoginDVO.getInstanceByName(rs) ;
	}

	/**
	 * ʵ�������, ʹ�� user �������� object, ������ new , �Խ�ʡִ��ʱ�估������<br>
	 * �ʺ���ʱ�Ե���������(ͨ���������������ʱ��һЩ��λ��Ҫ�ݴ��� value object)<br>
	 * �˷���ֻ����ִ�� findByPK(Object obj,String userId,String compId,String factoryId) ʱ�ű����С�
	 * <p>
	 * @exception SQLException - ���Ͽ����
	 * @since 104/09/07
	 */
	protected Object getObjFromRS(Object obj, ResultSet rs) throws SQLException,Exception {
		return aajccpLoginDVO.getInstanceByName(obj, rs) ;
	}
	
	/**
	  * ���ݷ��� 2009.03.26 ����
	  */
	public int backup(String tableName, aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		this.sql = getCreateSql(aacpLoginDVO).replaceFirst(" DB.TBAACPLOGIND "," "+tableName+" "); 
		return this.executeUpdate(this.sql) ;
	} 
	public int backup(String tableName, ResultSet rs) throws SQLException, Exception {
		return backup(tableName, aajccpLoginDVO.getInstanceByName(rs) ) ;
	} 

	public String getUpdateEditFieldsPartialSql(aajccpLoginDVO aacpLoginDVO)  {
		if( aacpLoginDVO.getEditFields()==null ) {
			return "";
		}
		return dejcSqlUtils.genUpdateFields(  aacpLoginDVO.getEditFields() );		
	}
	/**
	  * �޸��ж�������λ���˷���Ҫ��Ч��ȷ������ִ��  aajccpLoginDVO.monitor() 
	  */
	public String getUpdateEditFieldsSql(aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		String updSql =getUpdateEditFieldsPartialSql(aacpLoginDVO) ;
		if(updSql.equals("")){
			return "";
		}
		return "update DB.TBAACPLOGIND set "+updSql+" where   userId='"+aacpLoginDVO.getUserId()+"' and compId='"+aacpLoginDVO.getCompId()+"' and factoryId='"+aacpLoginDVO.getFactoryId()+"' ";
	}
	/**
	  * �޸��ж�������λ���˷���Ҫ��Ч��ȷ������ִ��  aajccpLoginDVO.monitor() 
	  */
	public int updateEditFields(aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		this.sql=getUpdateEditFieldsSql(aacpLoginDVO);
		if(this.sql.equals("")){
			// 2010.03.02 ��0��Ϊ-1����ʶ���Ƿ���ִ��
			return -1;
		}
		return this.executeUpdate(getUpdateEditFieldsSql(aacpLoginDVO)) ;
	}
	/**
	 * ��update��sql�������һ����λ����update
	 */	
	private String getLockSqlPrefix() {
		return "update DB.TBAACPLOGIND set factoryId=factoryId ";
	}

	/**
	 * ��ѯ���������������
	 */	
	public aajccpLoginDVO loadByPK4Update(String userId,String compId,String factoryId) throws SQLException, Exception {
		List aList = loadList4Update("WHERE   userId='"+userId+"' and compId='"+compId+"' and factoryId='"+factoryId+"' ");
		if(aList.size()>0) {
			return (aajccpLoginDVO)aList.get(0);
		}else {
			throw new dejcNotFoundException(this.sql) ;	
		}	
	}
	
	private String getSelectSqlPrefix() {
		return "select * from DB.TBAACPLOGIND ";
	}
	
	private List loadList4Update(String conditionSql) throws SQLException, Exception {
		this.executeUpdate(getLockSqlPrefix()+conditionSql) ;
		this.sql=getSelectSqlPrefix()+conditionSql ;
		return monitor(this.eQueryAll( this.sql ));
	}
	/**
	  *�������
	  */
	private List monitor(List aacpLoginDVOList){
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			aacpLoginDVO.monitor();
		}	
		return aacpLoginDVOList;
	}
	/**
	 * �� VO �̳� DB ���ϣ�������ͬʱ������
	 */
	public aajccpLoginDVO loadByVO(aajccpLoginDVO aacpLoginDVO) throws SQLException, Exception {
		return loadByPK4Update(aacpLoginDVO.getUserId(), aacpLoginDVO.getCompId(), aacpLoginDVO.getFactoryId());
	}
	/**
	 * �� VO List �̳� DB ���ϣ�������ͬʱ������
	 */
	public List loadByList(List aacpLoginDVOList) throws SQLException, Exception {
		List rsltList= new ArrayList(aacpLoginDVOList.size());
		for (Iterator iterator = aacpLoginDVOList.iterator(); iterator.hasNext();) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO) iterator.next();
			rsltList.add( loadByVO(aacpLoginDVO) ) ;
		}
		return rsltList;
	}
	/**
	 * �Զ�̬��ѯ������ѯ
	 */
	public Vector findByCriteria(dejcCriteria criteria)throws dejcNotFoundException, SQLException, Exception{
		return findByCriteria(criteria,"");
	}
	/**
	 * �Զ�̬��ѯ������ѯ
	 */
	public Vector findByCriteria(dejcCriteria criteria,String orderby)throws dejcNotFoundException, SQLException, Exception{
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append(getSelectSqlPrefix());
		if(!dejcUtility.isNull(orderby)) {
			orderby="order by "+orderby;
		}
		sqlStr.append(criteria.toSqlWithWhereIfExists()).append(orderby);  
		this.sql = sqlStr.toString();
		return this.eQueryAll(this.sql) ;
	}
	
//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

    public List findAll() throws SQLException, Exception{
    	this.sql = "select * from DB.TBAACPLOGIND ";
    	return this.eQueryAll(this.sql);
    }
    public List findbyCompId(String pCompId) throws SQLException, Exception{
    	this.sql = "select * from db.tbaacploginD where compId = '"+pCompId+"'";
    	return this.eQueryAll(this.sql);
    }
	public int updateCreate(aajccpLoginDVO vo) throws dejcEditException,
			dejcNoUpdateException, SQLException, Exception {
		int count = 0;
		if (update(vo) == 0) {
			count = create(vo);
		}
		return count;
	}
	public int removeByUserId(String userId) throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE  userId='"+userId+"' " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	
	public Vector findByCondition(String userId,String compId,String factoryId)
			throws dejcNotFoundException, SQLException, Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGIND");
		sqlStr.append(" WHERE userId = '" + userId + "' ");
		if(!(compId==null) && !compId.equals("")){
			sqlStr.append(" AND compId = '" + compId + "' ");
		}
		if(!(factoryId==null) && !factoryId.equals("")){
			sqlStr.append(" AND factoryId = '" + factoryId + "' ");
		}
		this.sql = sqlStr.toString();
	  	return this.eQueryAll(this.sql) ;
	}
	
	public int myUpdateList(List aacpLoginDVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
	
		int count = 0 ;
		
		for (int i=0; i<aacpLoginDVOList.size(); i++) {
			aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)aacpLoginDVOList.get(i) ;
			
			StringBuffer sqlStr = new StringBuffer();
			
			sqlStr.append("UPDATE DB.TBAACPLOGIND");
			sqlStr.append("   SET userId = '" + aacpLoginDVO.getUserId() + "'");
			sqlStr.append("      ,compId = '" + aacpLoginDVO.getCompId() + "'");
			sqlStr.append("      ,factoryId = '" + aacpLoginDVO.getFactoryId() + "'");
			sqlStr.append(" WHERE userId='"+aacpLoginDVO.getUserIdTemp()+"'");
			sqlStr.append("   AND compId='"+aacpLoginDVO.getCompIdTemp()+"'");
			sqlStr.append("   AND factoryId='"+aacpLoginDVO.getFactoryIdTemp()+"'");			
			
			if (aacpLoginDVO.verify() == false) {
				throw new dejcEditException(aacpLoginDVO.getMessage()) ;
			}
			if(aacpLoginDVO.hasEditFields()) {
				count = count + updateEditFields(aacpLoginDVO);
			} else {
				count = count + this.executeUpdate(sqlStr.toString()) ;
			}	
		}
		return count ;
	}


} // end of Class aajccpLoginDDAO