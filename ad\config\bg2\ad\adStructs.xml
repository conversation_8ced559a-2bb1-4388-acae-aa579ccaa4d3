<?xml version="1.0" encoding="BIG5"?>
<pages>
	<page pageID="adjjpba" path="adjjpba.jsp">
		<controller>com.icsc.ad.func.adjcpbaFunc</controller>
		<action flag="I" method="query" />
		<action flag="R" method="update" validate="updateCheck" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjcmlVO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjjpbb" path="adjjpbb.jsp">
		<controller>com.icsc.ad.func.adjcpbbFunc</controller>
		<action flag="I" method="query" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjcmlVO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjjpbc" path="adjjpbc.jsp">
		<controller>com.icsc.ad.func.adjcpbaFunc</controller>
		<action flag="I" method="queryIO" forward="adjjpbc.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjcmlVO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjj0101PdFactor" path="adjj0101PdFactor.jsp">
		<controller>com.icsc.ad.func.adjc0101Func</controller>
		<action flag="I" method="doQueryList"
			forward="adjj0101PdFactor.jsp" />
		<action flag="N" method="doCreateList"
			forward="adjj0101PdFactor.jsp" />
		<action flag="R" method="doUpdateList"
			forward="adjj0101PdFactor.jsp" />
		<action flag="D" method="doRemoveList"
			forward="adjj0101PdFactor.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcpdFactorVO" type="sequence" />
		</converter>
	</page>
	<page pageID="adjj0101PdChSet" path="adjj0101PdChSet.jsp">
		<controller>com.icsc.ad.func.adjc0101CHSFunc</controller>
		<action flag="I" method="doQueryList"
			forward="adjj0101PdChSet.jsp" />
		<action flag="N" method="doCreateList"
			forward="adjj0101PdChSet.jsp" />
		<action flag="R" method="doUpdByFactor"
			forward="adjj0101PdChSet.jsp" />
		<action flag="D" method="doRemoveList"
			forward="adjj0101PdChSet.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcPdCHSVO" type="sequence" />
		</converter>
	</page>
	<page pageID="adjjPlanPriceEdit" path="adjjPlanPriceEdit.jsp">
		<controller>com.icsc.ad.func.adjcPlanPriceEditFunc</controller>
		<action flag="I" method="doQuery"
			forward="adjjPlanPriceEdit.jsp" />
		<action flag="R" method="doUpdate"
			forward="adjjPlanPriceEdit.jsp" />
		<action flag="D" method="doRemove"
			forward="adjjPlanPriceEdit.jsp" />
		<action flag="A" method="doAUpdate"
			forward="adjjPlanPriceEdit.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcpdTimeAttriVO" type="sequence" />
		</converter>
	</page>

	<page pageID="adjjDpdFactor" path="adjjDpdFactor.jsp">
		<controller>com.icsc.ad.func.adjc0101Func</controller>
		<action flag="I" method="doQueryList"
			forward="adjjDpdFactor.jsp" />

		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcpdTimeAttriVO" type="sequence" />
		</converter>
	</page>

	<page pageID="adjj0301A" path="adjj0301A.jsp">
		<controller>com.icsc.ad.func.adjc0301Func</controller>
		<action flag="I" method="doInquire" forward="adjj0301A.jsp" />
		<action flag="N" method="doInsert" validate="do_Validate"
			forward="adjj0301A.jsp" />
		<action flag="R" method="doUpdate" validate="do_Validate"
			forward="adjj0301A.jsp" />
		<action flag="D" method="doDelete" forward="adjj0301A.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjctcVO"
				type="sequence" />
		</converter>
	</page>

	<page pageID="adjj0301B" path="adjj0301B.jsp">
		<controller>com.icsc.ad.func.adjc0301BFunc</controller>
		<action flag="I" method="doInquireB" forward="adjj0301B.jsp" />
		<action flag="N" method="doInsertB" validate="do_ValidateB"
			forward="adjj0301B.jsp" />
		<action flag="D" method="doDeleteB" forward="adjj0301B.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjcteVO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjj0301C" path="adjj0301C.jsp">
		<controller>com.icsc.ad.func.adjc0301CFunc</controller>
		<action flag="I" method="doInquire" forward="adjj0301C.jsp" />
		<action flag="N" method="doInsert" validate="do_Validate"
			forward="adjj0301C.jsp" />
		<action flag="R" method="doUpdate" validate="do_Validate"
			forward="adjj0301C.jsp" />
		<action flag="D" method="doDelete" forward="adjj0301C.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjctc2VO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjj0302Menu" path="adjj0302Menu.jsp">
		<controller>com.icsc.ad.func.adjc0302Func</controller>
		<action flag="I" method="doInquire" forward="adjj0302Menu.jsp" />
		<converter>
			<valueObject objectID="v2" class="com.icsc.ad.dao.adjctcVO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjj0302Input" path="adjj0302Input.jsp">
		<controller>com.icsc.ad.func.adjc0302Func</controller>
		<action flag="II" method="doInputInquire"
			forward="adjj0302Input.jsp" />
		<action flag="R" method="doInputUpdate"
			validate="doUpdate_validate" forward="adjj0302Input.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjctdVO"
				type="sequence" />
		</converter>
	</page>
	<page pageID="adjj0302Print" path="adjj0302Print.jsp">
		<controller>com.icsc.ad.func.adjc0302Func</controller>
		<action flag="P" method="doPrintInquire"
			forward="adjj0302Print.jsp" />
		<converter>
			<valueObject objectID="v2" class="com.icsc.ad.dao.adjctdVO"
				type="sequence" />
		</converter>
	</page>

	<page pageID="adjjPDFactor" path="adjjPDFactorM.jsp">
		<controller>com.icsc.ad.func.adjcPdFactorFunc</controller>
		<action flag="I" method="doQuery" forward="adjjPDFactorM.jsp" />
		<action flag="N" method="doCreate" forward="adjjPDFactorM.jsp" />
		<action flag="R" method="doUpdate" forward="adjjPDFactorM.jsp" />
		<action flag="D" method="doRemove" forward="adjjPDFactorM.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcpdFactorVO" type="sequence" />
		</converter>
	</page>

	<page pageID="adjjDayMccM" path="adjjDayMccM.jsp">
		<controller>com.icsc.ad.func.adjcDayMccFunc</controller>
		<action flag="I" method="doQuery" forward="adjjDayMccM.jsp" />
		<action flag="N" method="doCreate" validate="doCreate_validate"
			forward="adjjDayMccM.jsp" />
		<action flag="D" method="doRemove" forward="adjjDayMccM.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcDayMccVO" type="sequence" />
		</converter>
	</page>

	<page pageID="adjjDayMccReport" path="adjjDayMccReport.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="adjjDayMccReport.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="adjjRptProfitDetail" path="adjjRptProfitDetail.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun"
			forward="adjjRptProfitDetail.jsp" />
		<converter></converter>
	</page>

	<page pageID="adjjGrossProfit" path="adjjGrossProfit.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="adjjGrossProfit.jsp" />
		<converter></converter>
	</page>

	<page pageID="adjjInitDel" path="adjjInitDel.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="adjjInitDel.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="adjjRptMccIO" path="adjjRptMccIO.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="adjjRptMccIO.jsp" />
		<converter></converter>
	</page>

	<page pageID="adjjInputAndOutput" path="adjjInputAndOutput.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun"
			forward="adjjInputAndOutput.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="adjjRptInnerDetail" path="adjjRptInnerDetail.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun"
			forward="adjjRptInnerDetail.jsp" />
		<converter></converter>
	</page>

	<page pageID="adjjRelTraRpt" path="adjjRelTraRpt.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="adjjRelTraRpt.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="adjjMccReport" path="adjjMccReport.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="adjjMccReport.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="adjjProfitList" path="adjjProfitList.jsp"
		uiCtrl="true">
		<controller>com.icsc.ad.func.adjcProfitListFunc</controller>
		<action flag="I" method="query" />
		<action flag="N" method="create" validate="create_validate" />
		<action flag="R" method="update" validate="update_validate" />
		<action flag="D" method="delete" validate="delete_validate" />
		<action flag="T" method="cancel" validate="cancel_validate" />
		<converter>
			<valueObject objectID="v"
				class="com.icsc.ad.dao.adjcProfitVO" type="sequence" />
		</converter>
	</page>


	<page pageID="adjjMccM" path="adjjMccM.jsp">
		<controller>com.icsc.ad.func.adjcMccFunc</controller>
		<action flag="N" method="doCreate" forward="adjjMccM.jsp"
			validate="doCreate_validate" />
		<action flag="R" method="doUpdate" forward="adjjMccM.jsp"
			validate="doUpdate_validate" />
		<action flag="D" method="doRemove" forward="adjjMccM.jsp"
			validate="doRemove_validate" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjcmlVO"
				type="sequence" />
		</converter>
	</page>

	<page pageID="adjjDpdChgM" path="adjjDpdChgM.jsp">
		<controller>com.icsc.ad.func.adjcDpdChgFunc</controller>
		<action flag="N" method="doCreate" forward="adjjDpdChgM.jsp" />
		<action flag="R" method="doUpdate" forward="adjjDpdChgM.jsp" />
		<action flag="D" method="doRemove" forward="adjjDpdChgM.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ad.dao.adjcDpdChgVO" type="sequence" />
		</converter>
	</page>

	<page pageID="adjjtcList" path="adjjtcList.jsp">
		<controller>com.icsc.ad.func.adjc0301Func</controller>
		<action flag="I" method="doInquire" forward="adjjtcList.jsp" />
		<action flag="C" method="doCopy" validate="doCopy_Validate"
			forward="adjjtcList.jsp" />
		<converter></converter>
	</page>

	<page pageID="adjj0803TDList" path="adjj0803TDList.jsp">
		<controller>com.icsc.ad.func.adjc0803Func</controller>
		<action flag="I" method="doQueryList" forward="adjj0803TDList.jsp" />
		<action flag="I2" method="doQueryList" forward="adjj0803TDListB.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ad.dao.adjcmlVO" type="sequence" />
		</converter>
	</page>
	
	<page pageID="adjj0803TDDetail" path="adjj0803TDDetail.jsp">
		<controller>com.icsc.ad.func.adjc0803DetailFunc</controller>
		<action flag="I" method="doQuery" forward="adjj0803TDDetail.jsp" />
		<converter>
			<valueObject objectID="v2" class="com.icsc.ad.dao.adjcmlVO" type="unique" />
		</converter>
	</page>	
	
</pages>