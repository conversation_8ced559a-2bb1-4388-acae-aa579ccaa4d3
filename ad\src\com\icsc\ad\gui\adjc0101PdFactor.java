package com.icsc.ad.gui;

import java.util.*;

import com.icsc.ad.bp.adjcINI;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.api.zafcFactory;
import com.icsc.facc.zaf.dao.zafcrdDAO;
import com.icsc.facc.zaf.dao.zafcrdVO;

public class adjc0101PdFactor {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:35:50 $";

	public static Map getFactorMap(dsjccom dsCom, String prodTypeNo) {

		try {
			zafcrdDAO rdDAO = new zafcrdDAO(dsCom); 
			Map mp = new LinkedHashMap();
			
			List rdList = rdDAO.queryByIA(dsCom.companyId, adjcINI.getSetting(dsCom, "PDFACTOR_LEAFID"), prodTypeNo);
	
			for (Iterator iter = rdList.iterator(); iter.hasNext();) {
				zafcrdVO rdVO = (zafcrdVO) iter.next();
				mp.put(rdVO.getDataB(), rdVO);
			}
			return mp;
		} catch (Exception e) {
			return new HashMap();
		}
	}

	public static String getFactorOptions(zafcFactory fac, zafcrdVO rdVO, String factor) {
		try {
			Map para = new HashMap();
			para.put("CHARNAME", rdVO.getItemB());
			para.put("FACTOR", factor);
			return fac.newZafiOptions(rdVO.getDataC()).get(para);
		} catch (Exception e) {
			return "<option>" + e + "</option>";
		}
	}
}
