<%@ page contentType = "text/html;charset=GBK"%>
<%! public static final String _AppId = "MRJJLYGSTU"; %>
<%@ include file="../../jsp/dzjjmain.jsp" %>
<%@ page import="com.icsc.dpms.de.web.*" %>
<%@ page import="com.icsc.dpms.de.structs.*" %>
<%@ page import="com.icsc.dpms.de.structs.web.*" %>
<%@ page import="com.icsc.mr.dao.*" %>
<%@ page import="java.util.*" %>
<%
	dejcWebInfoOut infoOut = request.getAttribute("infoOut") == null ? new dejcWebInfoOut() : (dejcWebInfoOut)request.getAttribute("infoOut");
	List studentList = (List) infoOut.getResultVO("studentList");
	if (studentList == null) {
		studentList = new ArrayList();
	}
%>

<de:form name="form1" action="/erp/mr/do?_pageId=mrjjlygstuList" lock="true">
<table width="100%">
	<tr>
		<td class="subsys-title" width="10%">功能</td>
		<td class="function-bar-left" width=40%>
        	<de:btn value="查询" action="I"/>
        	<de:btn value="新增" action="N" onclick="openEdit('')"/>
		</td>
		<td width=10% class="subsys-title">讯 息</td>
		<td width=50% class="msg" id="msg"><%=infoOut.getMessage() %></td>
	</tr>
</table>

<!-- 查询条件 -->
<table border="0" cellspacing="1" cellpadding="0" width='100%'>
	<tr class='light-bg-left'> 
		<td width=15% class='subsys-title'>学生编号</td>
		<td width=20%>
			<de:text name="stuId_qry" size="10" maxlength="10"/>
		</td> 
	    <td width=15% class='subsys-title'>学生名字</td>
		<td width=20%>
	        <de:text name="stuName_qry" size="15" maxlength="8"/>
		</td>
		<td width=30%></td>
	</tr> 
</table>

<!-- 学生列表 -->
<table border="1" cellspacing="0" cellpadding="2" width='100%' style="border-collapse:collapse;">
	<tr class='subsys-title' style="background-color:#E0E0E0;">
		<td width=15% align="center">学生编号</td>
		<td width=20% align="center">学生名字</td>
		<td width=15% align="center">学生年龄</td>
		<td width=20% align="center">创建日期</td>
		<td width=15% align="center">最后修改人</td>
		<td width=15% align="center">操作</td>
	</tr>
	<%
	if (studentList.size() == 0) {
	%>
	<tr>
		<td colspan="6" align="center" style="padding:20px;">暂无数据</td>
	</tr>
	<%
	} else {
		for (int i = 0; i < studentList.size(); i++) {
			LygStudentVO student = (LygStudentVO) studentList.get(i);
	%>
	<tr class='light-bg-left' onmouseover="this.style.backgroundColor='#FFFFCC'" onmouseout="this.style.backgroundColor=''">
		<td align="center"><%=student.getStuId()%></td>
		<td align="center"><%=student.getStuName()%></td>
		<td align="center"><%=student.getStuAge()%></td>
		<td align="center"><%=student.getCreDateCF()%></td>
		<td align="center"><%=student.getUpdEmpNo()%></td>
		<td align="center">
			<a href="javascript:openEdit('<%=student.getStuId()%>')">编辑</a> |
			<a href="javascript:deleteStudent('<%=student.getStuId()%>')">删除</a>
		</td>
	</tr>
	<%
		}
	}
	%>
</table>

<div style="margin-top:10px;">
	共 <%=studentList.size()%> 条记录
</div>

</de:form>

<de:script src="dejtag"/>
<script>
// 打开编辑页面
function openEdit(stuId) {
    var url = "/erp/mr/do?_pageId=mrjjlygstuEdit&_action=I&stuId_qry=" + stuId;
    window.open(url, "studentEdit", "width=800,height=600,scrollbars=yes,resizable=yes");
}

// 删除学生
function deleteStudent(stuId) {
    if (confirm("确定要删除学生编号为 " + stuId + " 的记录吗？")) {
        form1._action.value = "D";
        form1.stuId_qry.value = stuId;
        form1.submit();
    }
}

// 刷新页面
function refreshList() {
    form1._action.value = "I";
    form1.submit();
}
</script>

<de:footer/>
