<%@ page import="java.io.*"%>
<%@ page import="java.util.*"%>
<%@ page import="com.icsc.dpms.ds.*"%>
<%@ page import="com.icsc.dpms.de.*"%>
<%@ page import="com.icsc.aa.*"%>
<%@ page import="com.icsc.aa.dao.*"%>
<%@ page import="com.icsc.aa.dei.*"%>

<%
String aaCompId = null; 
dejc318 de318 = new dejc318(_dsCom,"AAJJCOMPID"); 
try{        
    String pCompId = _dsCom.companyId;    
    if (pCompId==null) 
		response.sendRedirect("../../ds/jsp/dsjjl01.jsp");
    
    aajccpDAO aacpDAO = new aajccpDAO(_dsCom);
    Vector aacpVOVec = aacpDAO.getCompanies(_dsCom);
    aaCompId = _dsCom.companyId;
    if (aacpVOVec.size()>0){
		for (int i=0;i<aacpVOVec.size();i++){
			String compStr[] = (String[])aacpVOVec.get(i);
			if (compStr[0].trim().equals(aaCompId.trim()))
				out.println("<option value="+compStr[0].trim()+" selected>" + compStr[1] + "</optiion>");
			else
				out.println("<option value="+compStr[0].trim()+" >" + compStr[1] + "</optiion>");
		}//end for
    }
}catch(Exception e){
    de318.logs("AAJJCOMPID","GET COMPID EXCEPTION:" + e.getMessage(),e);
}
%>
