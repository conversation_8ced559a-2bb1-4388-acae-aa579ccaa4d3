/*----------------------------------------------------------------------------*/
/* aajccpLoginDVO		DAOTool Ver 10.0112 (INPUT FILE VERSION:2.0)
/*----------------------------------------------------------------------------*/
/* author : InfoChamp
/* system : 普通會計管理系統(AA)
/* target : 用戶授權帳套別及廠別維護作業基本資料明細文件
/* create : 104/09/07
/* update : 104/09/07
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao ;

import java.io.Serializable ;
import java.math.*;
import java.sql.*;
import java.text.DecimalFormat ;
import java.util.*;
import java.util.regex.*;
import javax.servlet.http.HttpServletRequest ;

import com.icsc.dpms.de.*;


/**
 * 用戶授權帳套別及廠別維護作業基本資料明細文件 Value Object.
 * <pre>
 * Table Name        : DB.TBAACPLOGIND
 * Table Description : 用戶授權帳套別及廠別維護作業基本資料明細文件
 * Data Access Object: aajccpLoginDDAO
 * </pre>
 * @version 普通會計管理系統(AA)
 * @since aajccpLoginDVO - 104/09/07
 * <AUTHOR>
 */
public class aajccpLoginDVO extends dejcCommonVO implements Serializable {
	public final static String AppId = "AAJCCPLOGINDVO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:29 $";
	public final static String ATTRIB_DESC="userId=使用者代號,compId=公司帳套別,factoryId=廠別," ; 

	/**
	 * 使用者代號 怀�蹀裕輒介�
	 * @since 104/09/07
	 */
	public final static int USERID_LEN = 10 ;
	/**
	 * 公司帳套別 怀�蹀裕輒介�
	 * @since 104/09/07
	 */
	public final static int COMPID_LEN = 10 ;
	/**
	 * 廠別 怀�蹀裕輒介�
	 * @since 104/09/07
	 */
	public final static int FACTORYID_LEN = 10 ;
	


/*----------------------------------------------------------------------------*/
/* DB.TBAACPLOGIND column Name
/*----------------------------------------------------------------------------*/

	/**
	 * 使用者代號
	 * @since 104/09/07
	 */
	private String userId;
	/**
	 * 公司帳套別
	 * @since 104/09/07
	 */
	private String compId;
	/**
	 * 廠別
	 * @since 104/09/07
	 */
	private String factoryId;
	/**
	 * 硒俴捅洘
	 * @since 104/09/07
	 */
	private String message ;

/*----------------------------------------------------------------------------*/
/* Creates new aajccpLoginDVO
/*----------------------------------------------------------------------------*/

	/**
	 * 膘凳赽
	 * @since 104/09/07
	 */
	public aajccpLoginDVO() {
		clear() ;
		super.setChild(this) ;
	}
	/**
	 * 翋猁瑩膘凳赽
	 * @since 104/09/07
	 */
	public aajccpLoginDVO(String userId,String compId,String factoryId) {
		this() ;
		this.userId = userId ;	// 使用者代號
		this.compId = compId ;	// 公司帳套別
		this.factoryId = factoryId ;	// 廠別
	}
	
	/**
	 * 蔚垀衄腔戲弇 reset 傖啎扢硉.
	 * <pre>
	 * 絞昜璃訧蹋祥剒婬掩妏蚚奀(瞰�覢嚗�掩 delete 綴�婕頝籤遘鶹梉嚗�善賒醱奻)
	 * 褫眕妏蚚森源楊ㄛ疑揭岆褫眕笭葡妏蚚森昜璃ㄛ祥剒婬 new 珨跺陔腔
	 *</pre>
	 */
	public void clear() {
		this.userId = "" ;	// 使用者代號
		this.compId = "" ;	// 公司帳套別
		this.factoryId = "" ;	// 廠別
		this.message = "";
	}
	/**
	 * 蔚赻撩葩秶珨爺堤懂
	 */
	public aajccpLoginDVO myClone() {
		aajccpLoginDVO aacpLoginDVO = new aajccpLoginDVO() ;
		aacpLoginDVO.setUserId(this.userId);
		aacpLoginDVO.setCompId(this.compId);
		aacpLoginDVO.setFactoryId(this.factoryId);
		return aacpLoginDVO ;
	}

/**
  * toJSON 源楊
  * <p>
  * @return JSON String
  * @since 104/09/07
  */
 public String toJSON() {
  StringBuffer sb = new StringBuffer() ;
  sb.append("userId :\""+dejcUtility.parseToJSON(getUserId()+"")+"\"") ;
  sb.append(",");
  sb.append("compId :\""+dejcUtility.parseToJSON(getCompId()+"")+"\"") ;
  sb.append(",");
  sb.append("factoryId :\""+dejcUtility.parseToJSON(getFactoryId()+"")+"\"") ;
  return "{"+sb.toString()+"}";
 }



/*----------------------------------------------------------------------------*/
/* function methods
/*----------------------------------------------------------------------------*/

	/**
	 * 眕 ResultSet 懂釬昜璃
	 * <p>
	 * @param rs - 等珨 ResultSet
	 * @return aajccpLoginDVO - 莉汜 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/09/07
	 */
	public static aajccpLoginDVO getInstance(ResultSet rs) throws SQLException,Exception {
		aajccpLoginDVO aacpLoginDVO = new aajccpLoginDVO() ;

  		aacpLoginDVO.setUserId(rs.getString(1));
  		aacpLoginDVO.setCompId(rs.getString(2));
  		aacpLoginDVO.setFactoryId(rs.getString(3));
  		return aacpLoginDVO ;
	}

	/**
	 * 眕 ResultSet 懂釬昜璃
	 * <p>
	 * @param rs - 等珨 ResultSet
	 * @return aajccpLoginDVO - 莉汜 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/09/07
	 */
	public static aajccpLoginDVO getInstanceByName(ResultSet rs) throws SQLException,Exception {
		return getInstanceByName(new aajccpLoginDVO(), rs) ;
	}

	/**
	 * 眕 ResultSet 懂釬昜璃, 妏蚚蚕 user 換輛懂腔昜璃
	 * <p>
	 * @param rs - 等珨 ResultSet
	 * @return aajccpLoginDVO - 莉汜 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/09/07
	 */
	public static aajccpLoginDVO getInstanceByName(Object obj, ResultSet rs) throws SQLException {
		aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO)obj ;
  		aacpLoginDVO.setUserId(rs.getString("userId"));
  		aacpLoginDVO.setCompId(rs.getString("compId"));
  		aacpLoginDVO.setFactoryId(rs.getString("factoryId"));
  		return aacpLoginDVO ;
	}

	/**
	 * 眕 Request 懂釬昜璃
	 * <p>
	 * @param request - 厙珜猁⑴昜璃
	 * @return aajccpLoginDVO - 莉汜 Value Object
	 * @since 104/09/07
	 */
	public static aajccpLoginDVO getInstance(HttpServletRequest request) {
		return getFromReq(request, -1) ;
	}
	/**
	 * 眕 Request 懂釬昜璃 (郣善嗣捩訧蹋奀)
	 * <p>
	 * @param request - 厙珜猁⑴昜璃
	 * @param index - 統杅霜阨唗瘍
	 * @return aajccpLoginDVO - 莉汜 Value Object
	 * @since 104/09/07
	 */
	public static aajccpLoginDVO getInstance(HttpServletRequest request, int index) {
		return getFromReq(request, index) ;
	}
	/**
	 * 眕 Request 懂釬昜璃
	 * <p>
	 * @param req - 厙珜猁⑴昜璃
	 * @param index - 統杅霜阨唗瘍
	 * @return aajccpLoginDVO - 莉汜 Value Object
	 * @since 104/09/07
	 */
	private static aajccpLoginDVO getFromReq(HttpServletRequest req, int index) {
		String seq = (index >= 0) ? String.valueOf(index) : "" ;
		aajccpLoginDVO aacpLoginDVO = new aajccpLoginDVO() ;

		aacpLoginDVO.setUserId(req.getParameter("userId"+seq)) ;
		aacpLoginDVO.setCompId(req.getParameter("compId"+seq)) ;
		aacpLoginDVO.setFactoryId(req.getParameter("factoryId"+seq)) ;

		return aacpLoginDVO ;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/

	/**
	 * 葡迡 toString , 眕瞳 Debug
	 * <p>
	 * @return 昜璃囀�楺�
	 * @since 104/09/07
	 */
	public String toString() {
		StringBuffer sb = new StringBuffer() ;

		sb.append("<aajccpLoginDVO>");
		sb.append(super.toString());
		sb.append("\r\n\tuserId = " + getUserId()) ;
		sb.append("\r\n\tcompId = " + getCompId()) ;
		sb.append("\r\n\tfactoryId = " + getFactoryId()) ;
		sb.append("\r\n</aajccpLoginDVO>");

		return sb.toString();
	}
	/**
	 * 掀誕謗跺昜璃腔 Key 扽俶
	 * <pre>
	 * 硐掀誕 Key 窒煦ㄛ衄珨砐 Key 祥肮ㄛ寀 return false
	 * 甜準�垓艙騫譝堈摹�誕
	 * </pre>
	 * @param object - 郗掀誕眳昜璃
	 * @return true - the same; false - diff
	 * @since 104/09/07
	 */
	public boolean keyEquals(Object object) {
		if (object instanceof aajccpLoginDVO == false) {
			return false ;
		}

		aajccpLoginDVO aacpLoginDVO = (aajccpLoginDVO) object ;
		if (this.userId.equals(aacpLoginDVO.getUserId()) == false) {
			return false ;
		}
		if (this.compId.equals(aacpLoginDVO.getCompId()) == false) {
			return false ;
		}
		if (this.factoryId.equals(aacpLoginDVO.getFactoryId()) == false) {
			return false ;
		}
		return true ;
	}
	/**
	 * 潰脤 key 硉岆瘁祥脹衾 null 麼 諾趼揹
	 * @return true - ok; false - hasErr
	 * @since 104/09/07
	 */
	public boolean isKeyOk() {
		if (dejcUtility.isNull(this.userId)) {
			return hasErr("userId") ;
		}
		if (dejcUtility.isNull(this.compId)) {
			return hasErr("compId") ;
		}
		if (dejcUtility.isNull(this.factoryId)) {
			return hasErr("factoryId") ;
		}
		return true ;
	}
	/**
	 * 潰脤訧蹋腔淏�煩� (婦嬤酗僅ㄛ倰怓)
	 * @return true - ok; false - hasErr
	 * @since 104/09/07
	 */
	public boolean verify() {
		if (userId!=null) { 
			if (userId.getBytes().length>USERID_LEN) {
				return hasErr("使用者代號的資料長度["+userId.getBytes().length+"]不得超過["+USERID_LEN+"]") ; 
			}
		}
		if (compId!=null) { 
			if (compId.getBytes().length>COMPID_LEN) {
				return hasErr("公司帳套別的資料長度["+compId.getBytes().length+"]不得超過["+COMPID_LEN+"]") ; 
			}
		}
		if (factoryId!=null) { 
			if (factoryId.getBytes().length>FACTORYID_LEN) {
				return hasErr("廠別的資料長度["+factoryId.getBytes().length+"]不得超過["+FACTORYID_LEN+"]") ; 
			}
		}
		return true ;
	}	

/*----------------------------------------------------------------------------*/
/* private methods
/*----------------------------------------------------------------------------*/

	/**
	 * 扢隅硒俴捅洘
	 * @param msg - 捅洘
	 * @return false - always false
	 */
	private boolean hasErr(String msg) {
		this.message = msg;
		return false ;
	}

	/**
	 * 眕戲弇靡備�○囆岏�(砉 Map 珨欴ㄛ
	 * @throws RuntimeException thrown when the field not exists
	 */
	public Object get(String field) {
	    return super.get(field) ;
	}


/*----------------------------------------------------------------------------*/
/* get and set methods for the instance variables
/*----------------------------------------------------------------------------*/

	/**
	 * 隙換硒俴捅洘
	 * @return message - �△譆棣倳剿�
	 * @since 104/09/07
     */
	public String getMessage() {
		return this.message ;
	}
	/**
	  *耜翹flagㄛ峈true奀符羲宎耜翹
	  */
	private boolean monitoring ;
	/**
	 * 晤憮戲弇靡備
	 */
	private Map editFields;
	/**
	 * 戲弇導訧蹋靡備
	 */
	private Map oldFieldValues;

	/**
	  * �△譜瑏釋裕閤�
	  * @return null if no editFields
	  */
	public Map getEditFields() {
		return this.editFields;
	}
	/**
	  *羲宎耜翹 set輛懂腔 fieldName,fieldValue
	  */
	public void monitor(){
		this.monitoring=true;
		this.editFields=new HashMap();
		this.oldFieldValues=new HashMap();
	}
	/**
	  *�＋�耜翹
	  */
	public void stopMonitor(){
		this.monitoring=false;
	}
	/**
	  * 藩跺 set method 掩網請奀飲頗網請森源楊
	  */
	public void onSetField(String field,Object value){
		if(this.monitoring){
			oldFieldValues.put(field, get(field));
			editFields.put(field,value);
		}
	}
	
	/** 
	 * 設定使用者代號
	 */ 
	public void setUserId(String userId)  { 
		onSetField("userId",userId); 
		this.userId = (userId==null)?"":userId; 
	}

	/** 
	 * 取得使用者代號
	 */ 
	public String getUserId()  { 
		return this.userId ; 
	}
	/** 
	 * 取得使用者代號 ( 處理單引號的問題 )
	 */ 
	public String getUserIdS()  { 
		return dejcUtility.replaceS(this.userId) ; 
	}

	/** 
	 * 設定公司帳套別
	 */ 
	public void setCompId(String compId)  { 
		onSetField("compId",compId); 
		this.compId = (compId==null)?"":compId; 
	}

	/** 
	 * 取得公司帳套別
	 */ 
	public String getCompId()  { 
		return this.compId ; 
	}
	/** 
	 * 取得公司帳套別 ( 處理單引號的問題 )
	 */ 
	public String getCompIdS()  { 
		return dejcUtility.replaceS(this.compId) ; 
	}

	/** 
	 * 設定廠別
	 */ 
	public void setFactoryId(String factoryId)  { 
		onSetField("factoryId",factoryId); 
		this.factoryId = (factoryId==null)?"":factoryId; 
	}

	/** 
	 * 取得廠別
	 */ 
	public String getFactoryId()  { 
		return this.factoryId ; 
	}
	/** 
	 * 取得廠別 ( 處理單引號的問題 )
	 */ 
	public String getFactoryIdS()  { 
		return dejcUtility.replaceS(this.factoryId) ; 
	}

	/**
	  *眕戲弇荎恅靡備�○亹倛鏽�備
	  */	
	public String getFieldDesc(String fieldName){
		Matcher matcher = Pattern.compile(fieldName+"=[^,]+").matcher(ATTRIB_DESC);
		if(matcher.find()){
			return matcher.group().replaceAll(fieldName+"=", "");
		}
		return null;
	}
	public String[] getAllFieldNames(){
		return ATTRIB_DESC.split("=[^,]+,");
	}
	/**
	 *岆瘁衄晤憮徹腔戲弇
	 */
	public boolean hasEditFields() {
		return getEditFields()!=null && !getEditFields().isEmpty();
	}
  	/**
	 * 蹈堤垀衄掩党蜊徹腔戲弇靡備
	 * @return
	 */
	public String[] listEditFieldNames() {
		if(!hasEditFields()) {
			return new String[0];
		}
		Set keySet = getEditFields().keySet();
		String[] fieldNames=new String[keySet.size()];
		keySet.toArray(fieldNames);
		return fieldNames;
	}
	/**
	 * �○鼚瑏饒做儷岏�
	 * @param field
	 * @return
	 */
	public Object getOldFieldValue(String field) {
		if(oldFieldValues==null) {
			return null ;
		}
		return oldFieldValues.get(field);
	}
	/**
	 * �○黫齾� KEY 戲弇
	 * @param field
	 * @return
	 */	
	public String[] getKeys() {
	    String key = "userId,compId,factoryId," ;
	    if(!key.equals("")){
	    	return key.substring(0,key.length()-1).split(",");
	    }else{
	    	return new String[0];
	    }
    	}
 

/*----------------------------------------------------------------------------*/
/* Customize methods
/*----------------------------------------------------------------------------*/

//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

	/** 
	 * validate every attribute (ex. empty, date format...)
	 * method need to be implemented..
	 */ 
	public dejcVoValidater validate() { 
		dejcVoValidater v = null ; 
		 // v = new dejcVoValidater(this); 
		 // validating logic here...
		return v ; 
	} ; 
	private String cname;
	public String getCname() {
		return cname;
	}
	public void setCname(String cname) {
		this.cname = cname;
	}
	private String userIdTemp;
	public String getUserIdTemp() {
		return userIdTemp;
	}
	public void setUserIdTemp(String s) {
		this.userIdTemp = s;
	}
	private String compIdTemp;
	public String getCompIdTemp() {
		return compIdTemp;
	}
	public void setCompIdTemp(String s) {
		this.compIdTemp = s;
	}
	private String factoryIdTemp;
	public String getFactoryIdTemp() {
		return factoryIdTemp;
	}
	public void setFactoryIdTemp(String s) {
		this.factoryIdTemp = s;
	}
	private String seq;
	public String getSeq() {
		return seq;
	}
	public void setSeq(String s) {
		this.seq = s;
	}


} // end of Class aajccpLoginDDAO