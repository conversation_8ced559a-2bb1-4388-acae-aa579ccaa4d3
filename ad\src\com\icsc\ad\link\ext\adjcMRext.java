package com.icsc.ad.link.ext;

import java.util.*;

import com.icsc.ad.link.adjiMR;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.facc.zaf.bp.zafcComBp;
import com.icsc.facc.zaf.dao.zafcCommonDAO;
import com.icsc.facc.zaf.util.zafcMapUtil;
import com.icsc.mr.api.mrjcAPI;

public class adjcMRext extends zafcComBp implements adjiMR {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:35:46 $";

	public adjcMRext(dsjccom dsCom) {
		super(dsCom);
	}

	// public adjcMRext(dsjccom dsCom, Connection con) {
	// super(dsCom, con);
	// }

	public String getMtrlWce(String compId, String mtrlNo) {
//		try {
//			mrjcMtrlVO mtrlVO = new mrjcMtrlDAO(dsCom).findByPK(compId, mtrlNo);
//			if (mtrlVO == null)
//				return "";
//			return mtrlVO.getWceNo();
//		} catch (Exception e) {
			return "";
//		}
	}

	// public Map[] getProdBuyData(String compId, String yyyymm) throws
	// Exception {
	// //mrjcAPI.getAXInstance(dsCom).getProdCodeQtyByMonth(compId,yyyymm);
	// return new ;
	// }

	public Map[] getProdIOData(String compId, String fromDate, String toDate) throws Exception {
		return mrjcAPI.getAXInstance(dsCom).getSelfProdData(compId, fromDate, toDate) ;
	}

	public List getSellData(String compId, String fromDate, String toDate, String sellType) throws Exception {
		// mrjcAPI.getAXInstance(dsCom).getMRSellData(compId,yyyymm,sellType);
		// TODO
		return new ArrayList();
	}
	
	private Map tranRules;
	
	public String trans2ProdCode(String compId, String mtrlNo, String costCenter) throws Exception {
		if (tranRules == null)
			this.fetchTranRules(compId);
		
		if (tranRules.containsKey(mtrlNo+costCenter))
			return (String) tranRules.get(mtrlNo+costCenter);
		else 
			return "";
	}
	
	private void fetchTranRules(String compId) throws Exception {
		String ruleLeafId = "020312"; 
		zafcCommonDAO comDAO = new zafcCommonDAO(dsCom);
		
		String sql1 = "Select itemA || itemB as KEY, dataA as VALUE From DB" + ".TBZAFRD Where compId = '" + compId + "' and leafID = '" + ruleLeafId + "'";
		
		tranRules = zafcMapUtil.fetchMapArray(comDAO.queryVOs(sql1), "KEY", "VALUE", zafcMapUtil.TYPE_UNCHANGE);
	}
}
