<%@ page contentType = "text/html;charset=UTF-8"%>

<%@ page import="java.io.*,java.util.*,com.icsc.dpms.de.*"%>
<%@ page import="org.apache.commons.fileupload.*"%>
<%@ page import="com.icsc.dpms.ds.*" %>

<%! final static String _AppId = "DEJJ328"; %>

<%!
private String getSize(long l){
    double d = (double)(l / 100L + 1L) / 10D;
    return String.valueOf(d);
}

private String doIstFile(String fileHome, FileItem fileitem) throws Exception{
			String fileName = fileitem.getName().replace('\\', '/');
			int i = fileName.lastIndexOf("/");
			if(i != -1)
			    fileName = fileName.substring(i + 1);
			
			File tmpDRSYSTEMPATH = new File(fileHome);
			if (!tmpDRSYSTEMPATH.exists())
				tmpDRSYSTEMPATH.mkdirs();
			
			if(fileitem != null){
			    File file = new File(fileHome, fileName);
			    fileitem.write(file);
			    
			    String s1 = fileName + " (" + getSize(file.length()) + " k); ";
			    
			    return s1;
			} else{
			    return "\u65B0\u589E\u201C" + fileName + "\u201D\u5931\u8D25, \u8BF7\u518D\u8BD5\u4E00\u6B21\uFF01\uFF01";
			}
}
%>
 

<%

dejc300 _de300 = new dejc300();
dsjccom _dsCom = _de300.run(_AppId, this, request, response);
if(_dsCom == null) return;

request.setCharacterEncoding("UTF-8");

DiskFileUpload diskfileupload = new DiskFileUpload();
diskfileupload.setHeaderEncoding("UTF-8");

List list = diskfileupload.parseRequest(request);

Iterator iterator = list.iterator();

FileItem fileitem = null;
do
{
    if(!iterator.hasNext())
        break;
    
    FileItem fileitem1 = (FileItem)iterator.next();
    if(fileitem1.isFormField())
    {
        String s3 = fileitem1.getFieldName();
 				System.out.println(s3 + "  " + fileitem1.getString());
    } else if(fileitem == null){
        fileitem = fileitem1;
    }
} while(true);


String fileHome = dejc329.DE_TMP_HOME + _dsCom.user.ID;
String fileName = doIstFile(fileHome,fileitem);

out.print(fileName);


%>
