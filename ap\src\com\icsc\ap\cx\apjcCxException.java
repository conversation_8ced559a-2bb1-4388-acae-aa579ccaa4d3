package com.icsc.ap.cx;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Classes in cx package throw this exception
 * <AUTHOR> I27640 $
 * @since $Date: 2016/04/26 09:10:14 $
 * @version $Revision: 1.1 $
 *
 */
public class apjcCxException extends Exception {
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2016/04/26 09:10:14 $ $Author: I27640 $";
	
	protected String system;
	protected String function;
	protected List<String> para;

	public apjcCxException(String msg, String system, String function) {
		super(msg);
		this.system = system;
		this.function = function;
		para = new ArrayList<String>();
	}
	
	public apjcCxException(Exception e, String system, String function) {
		super(e);
		this.system = system;
		this.function = function;
		para = new ArrayList<String>();
	}
	
	public apjcCxException(String msg, String system, String function, String[] para) {
		super(msg);
		this.system = system;
		this.function = function;
		this.para = new ArrayList<String>();
		this.para.addAll(Arrays.asList(para));
	}
	
	public apjcCxException(Exception e, String system, String function, String[] para) {
		super(e);
		this.system = system;
		this.function = function;
		this.para = new ArrayList<String>();
		this.para.addAll(Arrays.asList(para));
	}
	
	public void setFuncPara(String[] para) {
		this.para.addAll(Arrays.asList(para));
	}
	
	@Override
	public String toString() {
		return system + ":" + function + ":" + super.toString();
	}
	
	public String getDetail() {
		StringBuilder msg = new StringBuilder();
		for(String v : para)
			msg.append(v + "\n");
		msg.append(toString());
		return msg.toString();
	}
}
