<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ADJJPLANPRICE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String compId = zafctool.trim(request.getParameter("compId"));
	String workArea = zafctool.trim(request.getParameter("workArea"));
%>
<textarea name="CHILDSELECT" style="display:none">
	<select name="workArea_qry" onchange="showPD(this.value)">
		<%=ipjcWorkArea.getAllWAOptions(_dsCom, compId, workArea)%>
	</select>
</textarea>  

<script>
	if(parent){
		parent.document.all("WA").innerHTML = document.all("CHILDSELECT").value;
	}
</script>