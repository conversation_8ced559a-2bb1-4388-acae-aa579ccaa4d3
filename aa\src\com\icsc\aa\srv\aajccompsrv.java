package com.icsc.aa.srv;

import com.icsc.dpms.de.*;
import com.icsc.dpms.ds.*;
import com.icsc.aa.api.aajcCompapi;
//import com.icsc.aa.dei.aajcCommondei;

public class aajccompsrv {

    private dsjccom dsCom;

    private dejc318 de318;

    private String AppId = "AAJCCOMPSRV";

    public aajccompsrv(dsjccom dsCom) throws Exception {
        this.dsCom = dsCom;
        this.de318 = new dejc318(dsCom, AppId);
    }

    public aajcCompapi getAAClass() throws Exception{
        aajcCompapi aacompapi = null;
	    try {
	        Class[] aaAry = new Class[1];
	        aaAry[0] = this.dsCom.getClass();
	        Object[] aaObject = new Object[1];
	        aaObject[0] = this.dsCom;
	        //Class aa = null;
	        //aajcCommondei aaComdei = new aajcCommondei(dsCom);
	        Class aaapi = Class.forName("com.icsc.aa.dei.aajccompdei");
	        aacompapi = (aajcCompapi)aaapi.getConstructor(aaAry).newInstance(aaObject);
        } catch (Exception e) {
            de318.logs(AppId,"getAAClass Exception:" + e.getMessage(),e);
            throw e;
        } 
        return aacompapi;
    }

}