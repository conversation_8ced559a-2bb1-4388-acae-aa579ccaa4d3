/**
 * 
 */
package com.icsc.aa.dei;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import com.icsc.aa.dao.aajct1DAO;
import com.icsc.aa.dao.aajct2DAO;
import com.icsc.aa.dao.aajct2VO;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.ds.dsjccom;


public class aajcU8 {
    private aajct1DAO aat1DAO;
    private aajct2DAO aat2DAO;
    private Connection conU8 ;
    dejc301 de301U8 = new dejc301();
    
    public aajcU8(dsjccom dscom) throws SQLException {
        super();
        conU8 = de301U8.getConnection(dscom,"u8", "AAJCU8");
        de301U8.setAutoCommit(false);
        
        this.aat1DAO = new aajct1DAO(dscom,conU8);
        this.aat2DAO = new aajct2DAO(dscom,conU8);
    }
 
    public void postU8Batch(List aat1VOList,aajct2VO aat2VO) throws Exception {
        try {
            aat2DAO.deleteU8(aat2VO);
            aat1DAO.deleteU8(aat2VO);
            
            aat2DAO.createU8(aat2VO);
            aat1DAO.createU8(aat1VOList);
            
            de301U8.commit();
        } catch (Exception e) {
            try {
                de301U8.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            
            throw e;
        }
    }
    
    public void close(){
        de301U8.close();
        de301U8 = null;
    }
    
    public void postU8(List aat1VOList,aajct2VO aat2VO) throws Exception {
        try {
            aat2DAO.deleteU8(aat2VO);
            aat1DAO.deleteU8(aat2VO);
            
            aat2DAO.createU8(aat2VO);
            aat1DAO.createU8(aat1VOList);
            
            de301U8.commit();
        } catch (Exception e) {
            try {
                de301U8.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            
            throw e;
        }finally {
            de301U8.close();
            de301U8 = null;
        }
    }
    
    /**
     * @param args
     */
    public static void main(String[] args) {
        // TODO Auto-generated method stub

    }

    /**
     * @param aat2VO
     * @throws Exception 
     */
    public void deleteU8(aajct2VO aat2VO) throws Exception {
        try {
            aat2DAO.deleteU8(aat2VO);
            aat1DAO.deleteU8(aat2VO);

            de301U8.commit();
        } catch (Exception e) {
            try {
                de301U8.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            
            throw e;
        }finally {
            de301U8.close();
            de301U8 = null;
        }
    }

}
