package com.icsc.ac.bp;

import java.math.BigDecimal;
import java.util.*;

import com.icsc.facc.zaf.util.zafctool;

public class acjcVarChkFmtVO {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:26:56 $";

	// public static List IN_ACCTITEMS;
	//
	// static {
	// IN_ACCTITEMS = new ArrayList();
	// IN_ACCTITEMS.add("00");
	// IN_ACCTITEMS.add("02");
	// IN_ACCTITEMS.add("03");
	// IN_ACCTITEMS.add("11");
	// IN_ACCTITEMS.add("15");
	// }

	public acjcVarChkFmtVO() {

	}

	public acjcVarChkFmtVO(Map mp) {
		cc = zafctool.trim(mp.get("COSTCENTER"));
		acctItem = zafctool.trim(mp.get("ACCTITEM"));
		qty = zafctool.parseBD(mp.get("QTY"));
		amt = zafctool.parseBD(mp.get("AMT"));
		var = zafctool.parseBD(mp.get("VAR"));
		amtPrice = zafctool.divide(amt, qty, 4);
		varPrice = zafctool.divide(var, qty, 4);
		realPrice = amtPrice.add(varPrice);
	}

	public String cc;

	public String acctItem;

	public BigDecimal qty;

	public BigDecimal amt;

	public BigDecimal var;

	public BigDecimal amtPrice;

	public BigDecimal varPrice;

	public BigDecimal realPrice;
}
