package com.icsc.ac.gui;

import com.icsc.dpms.de.dejc308;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.ip.util.ipjcgetSelectValue;

public class acjcCCOptions {
	public static final String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:27:13 $";

	public static String getSelectOptions(dsjccom dsCom, String compId, String fiscalYear, String ccType, String target) throws Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select costCenter,costCenterDesc from db." + "tbacb1 ");
		sqlStr.append(" where compId='" + compId + "' and CCTYPE='" + ccType + "' and fiscalYear = '" + fiscalYear + "' ");
		sqlStr.append(" and stusCode<>'D' order by costCenter");
		return new ipjcgetSelectValue(dsCom).getOptionsShowValue(sqlStr.toString(), target).toString();
	}
	
	public static String getSACCOptions(dsjccom dsCom, String compId, String fiscalYear, String target) throws Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select costCenter,costCenterDesc from db." + "tbacb1 X ");
		sqlStr.append(" where compId='" + compId + "' and CCTYPE in ('S', 'A') and fiscalYear = '" + fiscalYear + "' and stusCode<>'D' ");
		sqlStr.append(" and EXISTS (Select 1 From DB.TBAMBA Where compId = '" + compId + "' and fiscalYear = '" + fiscalYear + "' and costCenter = X.costCenter) ");
		sqlStr.append(" order by costCenter");
		return new ipjcgetSelectValue(dsCom).getOptionsShowValue(sqlStr.toString(), target).toString();
	}
	
	public static String getProdCCOptions(dsjccom dsCom, String compId, String fiscalYear, String target) throws Exception {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select costCenter,costCenterDesc from db." + "tbacb1 ");
		sqlStr.append(" where compId='" + compId + "' and CCTYPE in ('P') and fiscalYear = '" + fiscalYear + "' ");
		sqlStr.append(" and stusCode<>'D' order by costCenter");
		return new ipjcgetSelectValue(dsCom).getOptionsShowValue(sqlStr.toString(), target).toString();
	}

	public static String getOptionsByCompId(dsjccom dsCom, String compId, String fiscalYear, String CC) {
		String sql = "select costCenter,costCenterDesc from db." + "tbACB1 where compId='" + compId + "'";
		sql += (" and fiscalYear='" + fiscalYear + "'");
		sql += " and stusCode<>'D' order by COSTCENTER";
		return new ipjcgetSelectValue(dsCom).getOptionsShowValue(sql, CC).toString();
	}

	public static String getOptionsByFactory(dsjccom dsCom, String compId, String fiscalYear, String factory, String CC) {
		String sql = "select a.costCenter,a.costCenterDesc from db." + "tbACB1 a ";
		sql += (" where a.factory='" + factory + "' and a.compId='" + compId + "'");
		if (fiscalYear.length() == 0)
			fiscalYear = String.valueOf(new dejc308().getWYear());
		sql += (" and a.fiscalYear='" + fiscalYear + "'");
		sql += " and a.stusCode<>'D' order by a.COSTCENTER";
		return new ipjcgetSelectValue(dsCom).getOptionsShowValue(sql, CC).toString();
	}
}
