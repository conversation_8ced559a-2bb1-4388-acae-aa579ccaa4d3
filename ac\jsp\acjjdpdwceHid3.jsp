<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.ip.gui.*" %>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%! public static String _AppId = "ACJJDPDWCE";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	String prodCode = zafctool.trim(request.getParameter("PD"));
	String workArea = zafctool.trim(request.getParameter("WA")); 
	//String[] alloyNos = zafctool.split(request.getParameter("alloyNos"), ","); 
%>
<textarea name="CHILDSELECT" style='width:100%' rows=3>
<%=ipjcAlloyNo.getCheckBoxes(_dsCom, prodCode, workArea, new String[0], "ALLOYNO0", "&nbsp;\n") %>

</textarea>

<textarea name="SPEC" style="display:none">		
<%=ipjcSpec.getCheckBoxes(_dsCom, prodCode, workArea, new String[0], "SPEC0", "&nbsp;\n") %>
</textarea>  

<script>
	if(parent){
		parent.document.all("alloyNo").innerHTML = document.all("CHILDSELECT").value;
		parent.document.all("SPEC").innerHTML = document.all("SPEC").value;
	}
</script>

