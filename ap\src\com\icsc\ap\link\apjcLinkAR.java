package com.icsc.ap.link;

import java.math.BigDecimal;
import java.sql.Connection;

import com.icsc.ar.arjcbxAPI;
import com.icsc.dpms.ds.dsjccom;

public class apjcLinkAR {
    private dsjccom dscom;
    private Connection con;

    private String message;

    private arjcbxAPI arAPI;

    public apjcLinkAR(dsjccom dscom, Connection con) throws Exception {
        super();
        this.dscom = dscom;
        this.con = con;
        arAPI = new arjcbxAPI(dscom, con);
    }

    /**
     * @param args
     */
    public static void main(String[] args) {
        // TODO Auto-generated method stub

    }

    public boolean setARAP(String compId, String cxNo, String identifyS, BigDecimal ntAmt, BigDecimal cxFrnAmt, String crcyUnit, String vchrDate) {
        boolean rtn = arAPI.setARAP(compId, cxNo, identifyS, ntAmt, cxFrnAmt, crcyUnit, vchrDate);
        this.message = arAPI.getMessage();
        
        return rtn;
    }

    public String getMessage() {
        return message;
    }
}
