<%@ page contentType = "text/html;charset=GBK"%>
<%@ page import = "com.icsc.facc.zaf.util.*" %>
<%@ page import = "com.icsc.ip.gui.*" %>

<%! public static String _AppId = "ACJJMAKECOST";%>
<%@ include file="../../jsp/dzjjmain.jsp"%>

<%
	//String compId = zafctool.trim(request.getParameter("compId_qry"));
	String factory = zafctool.trim(request.getParameter("factory_qry"));
	//String costCenter = zafctool.trim(request.getParameter("costCenter_qry"));
%>

<textarea name="CHILDSELECT" >
	<select name="WORKAREA0" onchange="showCC(this.value)">
		<%=ipjcWorkArea.getWorkAreaByFactoryA(_dsCom, factory, "") %>
	</select>
</textarea>

<script>
	if(parent){
		parent.document.all("WA").innerHTML = document.all("CHILDSELECT").value;
	}
</script>

