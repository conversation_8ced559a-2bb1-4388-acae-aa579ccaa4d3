package com.icsc.aa.print.xls;

import java.util.List;
import java.util.Map;

import com.icsc.aa.dao.aajcAcctData4Aid0416DAO;
import com.icsc.aa.dao.aajcAcctDataDAO;
import com.icsc.aa.util.aajcUtil;
import com.icsc.dpms.ds.dsjccom;

public class aajcGeneralLedgerAid0416Printer extends aajcGeneralLedgerAid04Printer{
	public final static String CLASS_VERSION = "$Id: aajcGeneralLedgerAid0416Printer.java,v 1.3 2018/03/26 05:33:26 I21312 Exp $";

	protected String showGpAttr;
	
	public aajcGeneralLedgerAid0416Printer(dsjccom dsCom, Map map) throws Exception {
		super(dsCom, map);
		showGpAttr = aajcUtil.getString(map.get("showGpAttr"));
	}
	
	@Override
    protected List<Map<String, Object>> getData() throws Exception {
    	aajcAcctDataDAO dao = new aajcAcctData4Aid0416DAO(dsCom, map);
    	return dao.queryData();
    }
	
}
