<page APPID="AAJJCPLOGIN" menuBar="true" layout="fit" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.ezoui.com/prod/schema"   xsi:schemaLocation="http://www.ezoui.com/prod/schema http://www.ezoui.com/prod/schema/gul-structs.xsd">
	<panel layout='vrow'>
		<row data='1,-1,0'>
			<panel>
				<form headervisible="false">
					<formrow widthrate="20%,0:10%,100:20%,5%,5%,5%,35%">
						<field type="trigger" label="使用者代号" list='du.UserZ:,this,userName'  id='userId_qry' />
      					<field type="label" id='userName'></field>
						<field type="comboBox" label="可使用帐套别"  datasrc='aa.allComp'  id='compId_qry' />
						<field type='btn' label='查询' id='query' icon='read' onClick='class:aajccpLogin.query:userId_qry,compId_qry'/>
						<field type='btn' label='更新' id='update' icon='edit' onClick='class:aajccpLogin.update:g1.select,g2.select,userId_qry,compId_qry'/>
						<field type='btn' label='删除' id='delete' icon='delete' onClick='js:deleteUser'/>
						<toolbar id='toolBar' msgwidth="95%"></toolbar>
					</formrow>
				</form>
			</panel>
		</row>
		<row data='1,1,0'>
			<panel layout='hrow'>
				<row data='.5,1,5'>
					<grid id='g1' type='edit' heading='使用者资料' autonewrow="true" onRow="class:aajccpLogin.queryCompIdbyUser:g1.row" pagesize='10' page='true'>
						<field type='trigger' label='使用者代号' list='du.UserZ:,this,cname' name='g1.userId' width='80%'/>
						<field type='label' label='使用者中文名称' id='cname' name='g1.cname'/>
						<field type='comboBox' label='上次使用帐套别' name='g1.lastCompId' datasrc='aa.ver' showValue="false"/>
					</grid>
				</row>
				<row data='.5,1,5'>
					<grid id='g2' type='edit' heading="帐套别资料" autonewrow="true" onRow="class:aajccpLogin.queryUserbyCompId:g2.row" pagesize="10" page='true'>
						<field type='trigger' label='使用者代号' name='g2.userId' list='du.UserZ:,this,cname2'/>
						<field type='label' label='使用者中文名称' id='cname2' name='g2.cname'/>
						<field type='comboBox' label='可使用帐套别' name='g2.compId' datasrc='aa.AllComp' showValue='false'/>
					</grid>
				</row>
			
			</panel>
		</row>
	</panel>
	<js id='deleteUser'><![CDATA[
    var data = gk.get('g2.select');
  if(Object.keys(data).length==0){
    alert("资料为空");
  } else {
     gk.event('bean:_class_aajccpLogin.delete:g1.select,g2.select,userId_qry,compId_qry',function(){gk.fire("query","onClick");});
  }
  ]]>
  </js>
</page>