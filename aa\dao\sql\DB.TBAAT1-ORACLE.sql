CREATE TABLE DB.TBAAT1 (
compId CHAR(10) DEFAULT '' NOT NULL,vchrDate CHAR(8) DEFAULT '' NOT NULL,vchrNo CHAR(15) DEFAULT '' NOT NULL,srlNo NUMBER (4,0)  DEFAULT 0  NOT NULL,tradeNo CHAR(15) DEFAULT '',drCr CHAR(1) DEFAULT '',apId CHAR(3) DEFAULT '',pgrmId CHAR(20) DEFAULT '',acctCode CHAR(20) DEFAULT '',idCode CHAR(50) DEFAULT '',idDesc CHAR(50) DEFAULT '',refNo CHAR(50) DEFAULT '',refDesc CHAR(50) DEFAULT '',crcyUnit CHAR(10) DEFAULT '',qtyFrnAmt NUMBER (18,4)  DEFAULT 0 ,ntAmt NUMBER (18,4)  DEFAULT 0 ,dueDate CHAR(8) DEFAULT '',srlDesc VARCHAR2(500) DEFAULT '',statusCode CHAR(1) DEFAULT '',postDate CHAR(8) DEFAULT '',vchrType CHAR(1) DEFAULT '',exRate NUMBER (18,6)  DEFAULT 1 , PRIMARY KEY(compId,vchrDate,vchrNo,srlNo) 
) ;
GRANT ALL ON DB.TBAAT1 TO JAVAUSER ;
