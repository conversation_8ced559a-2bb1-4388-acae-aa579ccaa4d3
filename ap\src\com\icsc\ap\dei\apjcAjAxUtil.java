/**
 * 
 */
package com.icsc.ap.dei;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import org.json.JSONArray;
import org.json.JSONObject;


import com.icsc.ap.config.apjcConfig;
import com.icsc.ap.dao.apjcBillDAO;
import com.icsc.ap.dao.apjcCommonDAO;
import com.icsc.ap.dao.apjcPayApplyDAO;
import com.icsc.dpms.de.dejc303;
import com.icsc.dpms.de.dejc308;
import com.icsc.dpms.de.dejc314;
import com.icsc.dpms.ds.dsjccom;
import com.icsc.ge.gejcRateHome;
import com.icsc.ge.gejcRateType;

/**
 *
 */
public class apjcAjAxUtil {

    private dsjccom dscom;
    public apjcAjAxUtil(dsjccom dscom) {
        this.dscom = dscom;
    }


    public String queryGpCodeList(String gpValue,String pageNum) {

        int iPageNum = Integer.parseInt(pageNum);
        int pageSize = 10;
        int totalSize = 0;
        apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);

        String ajaxString = "";
        try {
            StringBuffer sb = new StringBuffer();
            sb.append("select count(*) as TOTALSIZE ");
            sb.append(" from (select id,chnname from db.TBGP10 where isInvalid <> '1' union select a.USERNO as id,trim(a.CNAME)||'-'||trim(b.DEPNAME)  as chnname from db.TBDU01 a left join db.TBDU05 b on a.DEPNO=b.DEPNO where a.COMPNO='" + this.dscom.companyId + "')  where 1=1 ");
            sb.append(gpValue.equals("") ? "" : " and ID like '%" + gpValue + "%'");
            sb.append(gpValue.equals("") ? "" : " or CHNNAME like '%" + gpValue + "%'");

            Map map = apComDAO.queryVO(sb.toString());
            int totoalSize = map==null||map.get("TOTALSIZE")==null ? 0 : Integer.parseInt((String)map.get("TOTALSIZE"));
            
            totalSize=totoalSize;
            
            int rowS = (iPageNum-1)*pageSize +1;
            int rowE = iPageNum*pageSize;
            
            sb = new StringBuffer("SELECT ID,CHNNAME FROM ");
            sb.append(" (select a.*, ");
            sb.append(" rownumber() over (ORDER BY a.id ) as rowNum");
            sb.append(" from (select id,chnname from db.TBGP10 where isInvalid <> '1' union  select c.USERNO as id,trim(c.CNAME)||'-'||trim(d.DEPNAME)  as chnname from db.TBDU01 c left join db.TBDU05 d on c.DEPNO=d.DEPNO ) a where 1=1 ");
            sb.append(gpValue.equals("") ? "" : " and ID like '%" + gpValue + "%'");
            sb.append(gpValue.equals("") ? "" : " or CHNNAME like '%" + gpValue + "%'");
            sb.append(")");
            sb.append(" as b");
            sb.append(" where b.rowNum BETWEEN " + rowS + " AND " + rowE);
            sb.append(" order by b.id");
            
            Map[] maps = apComDAO.queryVOs(sb.toString());
            JSONArray jsonArr = new JSONArray();
            for(int i = 0; maps!=null && i < maps.length; i++){
                jsonArr.put(new JSONObject(maps[i]));
            }
            

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("pageSize", pageSize);
            jsonObj.put("pageNum", pageNum);
            jsonObj.put("totalSize", totalSize);
            jsonObj.put("totoalPages", totalSize % pageSize == 0 ? totalSize / pageSize : (totalSize / pageSize + 1));
            jsonObj.put("gpCode", jsonArr);
            
            System.out.println(jsonObj.toString());
            ajaxString = jsonObj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ajaxString;
    }
    

    public String queryGpCodeListA(String gpValue,String pageNum) {

        int iPageNum = Integer.parseInt(pageNum);
        int pageSize = 10;
        int totalSize = 0;
        apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);

        String ajaxString = "";
        try {
            StringBuffer sb = new StringBuffer();
            sb.append(" select count(*) as TOTALSIZE  from  (select distinct PAYEEID from db.tbapPayApply where compid='" + this.dscom.companyId  + "' and PAYNO='') as a ");
            sb.append(" inner join (select id,chnname from db.TBGP10 where isInvalid <> '1' union select USERNO as id,CNAME as chnname from db.tbdu01) as b");
            sb.append(" on a.payeeId=b.id ");
            sb.append(" where  1=1 ");
            sb.append(gpValue.equals("") ? "" : " and a.payeeId like '%" + gpValue + "%'");
            sb.append(gpValue.equals("") ? "" : " or b.CHNNAME like '%" + gpValue + "%'");

            Map map = apComDAO.queryVO(sb.toString());
            int totoalSize = map==null||map.get("TOTALSIZE")==null ? 0 : Integer.parseInt((String)map.get("TOTALSIZE"));
            
            totalSize=totoalSize;
            
            int rowS = (iPageNum-1)*pageSize +1;
            int rowE = iPageNum*pageSize;
            
            sb = new StringBuffer("SELECT ID,CHNNAME FROM ");
            sb.append(" (");
            sb.append(" select id,CHNNAME,rownumber() over (ORDER BY id ) as rowNum from ");
            sb.append(" (");
            sb.append(" select a.PAYEEID as id,b.chnname from  ");
            sb.append(" (select distinct PAYEEID from db.tbapPayApply where compid='" + this.dscom.companyId + "' and PAYNO='') as a ");
            sb.append(" inner join (select id,chnname from db.TBGP10 where isInvalid <> '1' union select USERNO as id,CNAME as chnname from db.tbdu01) as b");
            sb.append(" on a.payeeId=b.id");
            sb.append(" where 1=1 ");
            sb.append(gpValue.equals("") ? "" : " and ID like '%" + gpValue + "%'");
            sb.append(gpValue.equals("") ? "" : " or CHNNAME like '%" + gpValue + "%'");
            sb.append(" ) ");
            sb.append(" ) as c");
            sb.append(" where rowNum BETWEEN " + rowS + " AND " + rowE);
            sb.append(" order by id");
            
            Map[] maps = apComDAO.queryVOs(sb.toString());
            JSONArray jsonArr = new JSONArray();
            for(int i = 0; maps!=null && i < maps.length; i++){
                jsonArr.put(new JSONObject(maps[i]));
            }
            

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("pageSize", pageSize);
            jsonObj.put("pageNum", pageNum);
            jsonObj.put("totalSize", totalSize);
            jsonObj.put("totoalPages", totalSize % pageSize == 0 ? totalSize / pageSize : (totalSize / pageSize + 1));
            jsonObj.put("gpCode", jsonArr);
            
            System.out.println(jsonObj.toString());
            ajaxString = jsonObj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ajaxString;
    }
    
    public String  queryGpCode(String gpValue) {
        apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);

        String ajaxString = "";
        try {
            StringBuffer sb = new StringBuffer("SELECT ID,CHNNAME from (select id,chnname from db.TBGP10 where isInvalid <> '1' union select USERNO as id,CNAME as chnname from db.tbdu01)  where 1=1  ");
            sb.append(gpValue.equals("") ? "" : " and ID like '%" + gpValue + "%'");
            sb.append(gpValue.equals("") ? "" : " or CHNNAME like '%" + gpValue + "%'");
            sb.append(" order by id fetch first 1 rows only");
            
            Map map = apComDAO.queryVO(sb.toString());

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("gpValue", gpValue);
            
            if(map==null){
                jsonObj.put("gpCode", "");
                jsonObj.put("gpName", "");
            }else{
                jsonObj.put("gpCode", (String)map.get("ID"));
                jsonObj.put("gpName", (String)map.get("CHNNAME"));               
            }

            System.out.println(jsonObj.toString());
            
            ajaxString =  jsonObj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return ajaxString;

    }
    

    public String queryProjectCodeList(String gpValue,String pageNum) {

        int iPageNum = Integer.parseInt(pageNum);
        int pageSize = 10;
        int totalSize = 0;
        apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);

        String ajaxString = "";
        try {
            StringBuffer sb = new StringBuffer();
            
            sb.append("select count(*) as TOTALSIZE  from (select a.REFNO as id ,a.REFDESC as CHNNAME  from db.tbaa26 as a ");
            sb.append(" inner join db.tbaa24 as b on a.compid=b.compid and a.REFTYPESTUS=b.REFTYPESTUS ");
            sb.append(" where a.compid='" + this.dscom.companyId + "' and b.REFTYPEDESC='DB' ") ;
            sb.append(gpValue.equals("") ? "" : " and (a.REFNO like '%" + gpValue + "%' or a.REFDESC like '%" + gpValue + "%')");
            sb.append(")");

            Map map = apComDAO.queryVO(sb.toString());
            int totoalSize = map==null||map.get("TOTALSIZE")==null ? 0 : Integer.parseInt((String)map.get("TOTALSIZE"));
            
            totalSize=totoalSize;
            
            int rowS = (iPageNum-1)*pageSize +1;
            int rowE = iPageNum*pageSize;
            
            sb = new StringBuffer("SELECT ID,CHNNAME FROM ");
            sb.append(" (");
            sb.append(" select id,CHNNAME,rownumber() over (ORDER BY id ) as rowNum from ");
            sb.append(" (");
            sb.append("select a.REFNO as id ,a.REFDESC as CHNNAME  from db.tbaa26 as a ");
            sb.append(" inner join db.tbaa24 as b on a.compid=b.compid and a.REFTYPESTUS=b.REFTYPESTUS ");
            sb.append(" where a.compid='" + this.dscom.companyId + "' and b.REFTYPEDESC='DB' ") ;
            sb.append(gpValue.equals("") ? "" : " and (a.REFNO like '%" + gpValue + "%' or a.REFDESC like '%" + gpValue + "%')");
            sb.append(" ) ");
            sb.append(" ) as c");
            sb.append(" where rowNum BETWEEN " + rowS + " AND " + rowE);
            sb.append(" order by id");
            
            Map[] maps = apComDAO.queryVOs(sb.toString());
            JSONArray jsonArr = new JSONArray();
            for(int i = 0; maps!=null && i < maps.length; i++){
                jsonArr.put(new JSONObject(maps[i]));
            }
            

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("pageSize", pageSize);
            jsonObj.put("pageNum", pageNum);
            jsonObj.put("totalSize", totalSize);
            jsonObj.put("totoalPages", totalSize % pageSize == 0 ? totalSize / pageSize : (totalSize / pageSize + 1));
            jsonObj.put("gpCode", jsonArr);
            
            System.out.println(jsonObj.toString());
            ajaxString = jsonObj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ajaxString;
    }
    
    public String  queryProjectName(String gpValue) {
        apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);

        String ajaxString = "";
        try {
            StringBuffer sb = new StringBuffer("select ID,CHNNAME  from (select a.REFNO as id ,a.REFDESC as CHNNAME  from db.tbaa26 as a ");
            sb.append(" inner join db.tbaa24 as b on a.compid=b.compid and a.REFTYPESTUS=b.REFTYPESTUS ");
            sb.append(" where a.compid='" + this.dscom.companyId + "' and b.REFTYPEDESC='DB' ") ;
            sb.append(gpValue.equals("") ? "" : " and (a.REFNO like '%" + gpValue + "%' or a.REFDESC like '%" + gpValue + "%')");
            sb.append(")");

            sb.append(" order by id fetch first 1 rows only");
            
            Map map = apComDAO.queryVO(sb.toString());

            JSONObject jsonObj = new JSONObject();
            jsonObj.put("gpValue", gpValue);
            
            if(map==null){
                jsonObj.put("gpCode", "");
                jsonObj.put("gpName", "");
            }else{
                jsonObj.put("gpCode", (String)map.get("ID"));
                jsonObj.put("gpName", (String)map.get("CHNNAME"));               
            }

            System.out.println(jsonObj.toString());
            
            ajaxString =  jsonObj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return ajaxString;

    }
    
    
    public List  queryBillVOList(String compId,String payeeId,String crcyUnit) {
        List apBillVOList = new Vector();
        try {
            apjcBillDAO apBillDAO = new apjcBillDAO(dscom);
                
            apBillVOList = apBillDAO.findByPayeeId(compId,payeeId,crcyUnit);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return apBillVOList;

    }
    
    public Map[]  queryBillPaymentMaps(String compId,String payeeId,String crcyUnit,int paymentType) {
        Map[] maps = null;
        try {
            apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);
            StringBuffer sb = new StringBuffer("select a.compId,a.CfmDate,a.ActuralDesc,a.PayeeId,a.projectNo,a.BillType,b.paymentSrlNo,a.billNo,a.ProcessDeptNo,a.projectNo,a.fieldA,a.fieldB,a.fieldC,a.fieldD,b.contractNo, ");
            sb.append(" a.ProcessEmpNo,b.FrnBillAmt,b.FrnApplyAmt,b.FrnPayAmt from db.tbapBill as a ");
            sb.append(" inner join db.TBAPBillPayment as b  on a.compid=b.compid and a.billno=b.billno");
            sb.append(" where a.compId='" + compId + "' and a.payeeId='" + payeeId + "' and a.crcyUnit='" + crcyUnit + "' and b.acctType=" + paymentType);
            sb.append(" and b.frnBillAmt!=b.frnApplyAmt and a.statuscode='Y'");
            maps = apComDAO.queryVOs(sb.toString());

        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return maps;
    }
    
    public Map[]  queryBillPaymentMaps(String compId,String payeeId,String crcyUnit,String projectNo,String  billType,int paymentType) {
        Map[] maps = null;
        try {
            apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);
            StringBuffer sb = new StringBuffer("select a.compId,a.CfmDate,a.ActuralDesc,a.PayeeId,a.projectNo,a.BillType,b.paymentSrlNo,a.billNo,a.ProcessDeptNo,a.projectNo,a.fieldA,a.fieldB,a.fieldC,a.fieldD,b.contractNo, ");
            sb.append(" a.ProcessEmpNo,b.FrnBillAmt,b.FrnApplyAmt,b.FrnPayAmt from db.tbapBill as a ");
            sb.append(" inner join db.TBAPBillPayment as b  on a.compid=b.compid and a.billno=b.billno");
            sb.append(" where a.compId='" + compId + "' and a.payeeId='" + payeeId + "' and a.crcyUnit='" + crcyUnit + "' and b.acctType=" + paymentType);
            if(!projectNo.equals("")){
                sb.append(" and a.projectNo='" + projectNo + "' ");
            }
            if(!billType.equals("")){
                sb.append(" and a.billType='" + billType + "' ");
            }
            sb.append(" and b.frnBillAmt!=b.frnApplyAmt and a.statuscode='Y' and a.isPayable='Y' ");
            maps = apComDAO.queryVOs(sb.toString());

        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return maps;
    }
    
    public Map[]  queryBillPaymentMaps(String compId,String payeeId,String crcyUnit) {
        Map[] maps = null;
        try {
            apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);
            StringBuffer sb = new StringBuffer("select a.CfmDate,a.ActuralDesc,a.PayeeId,a.BillType,b.paymentSrlNo,b.ACCTTYPE,a.billNo,a.ProcessDeptNo,");
            sb.append(" a.ProcessEmpNo,b.FrnBillAmt,b.FrnApplyAmt,b.FrnPayAmt from db.tbapBill as a ");
            sb.append(" inner join db.TBAPBillPayment as b  on a.compid=b.compid and a.billno=b.billno");
            sb.append(" where a.compId='" + compId + "' and a.payeeId='" + payeeId + "' and a.crcyUnit='" + crcyUnit + "' ");
            sb.append(" and b.frnBillAmt!=b.frnApplyAmt and a.statuscode='Y'");
            maps = apComDAO.queryVOs(sb.toString());

        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return maps;
    }
 
    public Map[]  queryBillPaymentMaps(String compId,String payeeId,String crcyUnit,String projectNo,String  billType,String processDeptNo,int paymentType) {
        Map[] maps = null;
        try {
            apjcCommonDAO apComDAO = new apjcCommonDAO(dscom);
            StringBuffer sb = new StringBuffer("select a.compId,a.CfmDate,a.ActuralDesc,a.PayeeId,a.projectNo,a.BillType,b.paymentSrlNo,a.billNo,a.ProcessDeptNo,a.projectNo,a.fieldA,a.fieldB,a.fieldC,a.fieldD,b.contractNo, ");
            sb.append(" a.ProcessEmpNo,b.FrnBillAmt,b.FrnApplyAmt,b.FrnPayAmt from db.tbapBill as a ");
            sb.append(" inner join db.TBAPBillPayment as b  on a.compid=b.compid and a.billno=b.billno");
            sb.append(" where a.compId='" + compId + "' and a.payeeId='" + payeeId + "' and a.crcyUnit='" + crcyUnit + "' and b.acctType=" + paymentType);
            if(!projectNo.equals("")){
                sb.append(" and a.projectNo='" + projectNo + "' ");
            }
            if(!billType.equals("")){
                sb.append(" and a.billType='" + billType + "' ");
            }
            if(!processDeptNo.equals("")){
                sb.append(" and a.processDeptNo like '" + processDeptNo + "%' ");
            }
            sb.append(" and b.frnBillAmt!=b.frnApplyAmt and a.statuscode='Y' and a.isPayable='Y' ");
            maps = apComDAO.queryVOs(sb.toString());

        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return maps;
    }
    
    public List queryPayApplyVOList(String payeeId,String crcyUnit){
        List apPayApplyVOList = new Vector();
        try {
            apjcPayApplyDAO apPayApplyDAO = new apjcPayApplyDAO(dscom);
                
            apPayApplyVOList = apPayApplyDAO.findByPayeeId(dscom.companyId,payeeId,crcyUnit);
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return apPayApplyVOList;
    }
    
    
    public String queryExRate(String crcyUnit){
        return new apjcConfig(this.dscom).queryExRate(crcyUnit).toString();
    }
    
    /**
     * @param args
     */
    public static void main(String[] args) {
        // TODO Auto-generated method stub
        dejc314 de314 = new dejc314();
        de314.computeTime("20131225", "090000", 10);
        System.out.println(de314.getTimeFmt());
        
    }

}
