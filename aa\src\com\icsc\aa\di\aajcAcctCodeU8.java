/**
 * 
 */
package com.icsc.aa.di;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import com.icsc.aa.dao.aajc01DAO;
import com.icsc.aa.dao.aajc01VO;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dao.dejcNotFoundException;
import com.icsc.dpms.di.diji234;
import com.icsc.dpms.ds.dsjccom;

/**
 * 
 */
public class aajcAcctCodeU8 implements diji234 {
    private aajc01DAO aa01DAOU8;

    /*
     * (non-Javadoc)
     * 
     * @see com.icsc.dpms.di.diji234#run(com.icsc.dpms.ds.dsjccom)
     */
    public void run(dsjccom dscom) {
        dejc301 de301U8 = new dejc301();
        try {
            Connection conU8 = de301U8.getConnection(dscom, "u8", "AAJCU8");
            aa01DAOU8 = new aajc01DAO(dscom, conU8);

            postAcctCodeToU8(dscom, "yf");
            postAcctCodeToU8(dscom, "yt");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            de301U8.close();
            de301U8 = null;
        }
    }

    /**
     * @param dscom
     * @param string
     * @throws Exception
     * @throws SQLException
     * @throws dejcNotFoundException
     */
    private void postAcctCodeToU8(dsjccom dscom, String compId) throws Exception {
        aajc01DAO aa01DAO = new aajc01DAO(dscom);
        List aa01VOList = aa01DAO.findByMasterKey(compId);

        aa01DAOU8.deleteU8(compId);
        for (int i = 0; aa01VOList != null && i < aa01VOList.size(); i++) {
            aajc01VO aa01VO = (aajc01VO) aa01VOList.get(i);

            aa01DAOU8.createU8(aa01VO);
        }
    }

}
