<?xml version="1.0" encoding="BIG5"?>
<pages>
<version>2.0</version> 
      <page pageID="apjjUpload" path="apjjSetUp.jsp">
	    <controller>com.icsc.ap.func.apjcUpload</controller>
	    <action flag="U" method="upload" forward="apjjSetUp.jsp"/>	
	    <action flag="B" method="upload" forward="apjjFeeApplyUpload.jsp"/>   
	        <converter>

				</converter>
    </page>  

      <page pageID="apjjRf01" path="apjjRf01.jsp">
	    <controller>com.icsc.ap.func.apjcRfFunc</controller>
	    <action flag="I" method="query" forward="apjjRf01.jsp"/>	   
	    <action flag="N" method="save"  forward="apjjRf01.jsp"/>
	        <converter>
	        <valueObject objectID="v1"
	                class="com.icsc.ap.dao.apjcrfVO"
	                type="unique"/>
				</converter>
    </page>  
    <page pageID="apjjBillTypeList" path="apjjBillTypeList.jsp">
	    <controller>com.icsc.ap.func.apjcBillTypeFunc</controller>
	    <action flag="I" method="queryAll" forward="apjjBillTypeList.jsp"/>	   
	        <converter>
	        <valueObject objectID="v"
	                class="com.icsc.ap.dao.apjcBillTypeVO"
	                type="unique"/>
				</converter>
    </page>  
    <page pageID="apjjBillType01" path="apjjBillType01.jsp">
	    <controller>com.icsc.ap.func.apjcBillTypeFunc</controller>
	    <action flag="Q" method="queryOne" forward="apjjBillType01.jsp"/>   
	    <action flag="N" method="save" forward="apjjBillType01.jsp"/>   
	        <converter>
	        <valueObject objectID="v"
	                class="com.icsc.ap.dao.apjcBillTypeVO"
	                type="unique"/>
				</converter>
    </page>  
    <page pageID="apjjPayItemType01" path="apjjPayItemType01.jsp">
	    <controller>com.icsc.ap.func.apjcPayItemTypeFunc</controller>
	    <action flag="I" method="query" forward="apjjPayItemType01.jsp"/>
	    <action flag="N" method="insert" forward="apjjPayItemType01.jsp"/>
	    <action flag="R" method="update" forward="apjjPayItemType01.jsp"/>
	    <action flag="D" method="delete" forward="apjjPayItemType01.jsp"/> 
	        <converter>
	        <valueObject objectID="v"
	                class="com.icsc.ap.dao.apjcPayItemTypeVO"
	                type="unique"/>
				</converter>
    </page>   
	<page pageID="apjjBk01" path="apjjBk01.jsp">
	    <controller>com.icsc.ap.func.apjcBkFunc</controller>
	    <action flag="I" method="query" forward="apjjBk01.jsp"/>
	    <action flag="N" method="create" forward="apjjBk01.jsp"/>
	    <action flag="R" method="update" forward="apjjBk01.jsp"/>
	    <action flag="D" method="delete" forward="apjjBk01.jsp"/>
	    <converter>
	        <valueObject objectID="apBkVO"
	            class="com.icsc.ap.dao.apjcBkVO"
	            type="unique"/>
			</converter>
	</page>  
<page pageID="apjjCodeRuleSet01" path="apjjCodeRuleSet01.jsp">
		<controller>com.icsc.ap.func.apjcCodeRuleSetFunc</controller>
			<action flag="I" method="query" forward="apjjCodeRuleSet01.jsp"/>
			<action flag="S" method="save" forward="apjjCodeRuleSet01.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcCodeRuleVO" type="sequence"/>
		</converter>
	</page>
	
	<page pageID="apjjPayApplyType01" path="apjjPayApplyType01.jsp">
		<controller>com.icsc.ap.func.apjcPayApplyTypeFunc</controller>
			<action flag="I" method="query" forward="apjjPayApplyType01.jsp"/>
			<action flag="S" method="save" forward="apjjPayApplyType01.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcPayApplyTypeVO" type="unique"/>
		</converter>
	</page>

    <page pageID="apjjPayType01" path="apjjPayType01.jsp">
        <controller>com.icsc.ap.func.apjcPayTypeFunc</controller>
            <action flag="I" method="query" forward="apjjPayType01.jsp"/>
            <action flag="S" method="save" forward="apjjPayType01.jsp"/>
        <converter>
            <valueObject objectID="v1" class="com.icsc.ap.dao.apjcPayTypeVO" type="unique"/>
        </converter>
    </page>
    	
	<page pageID="apjjInvoiceType01" path="apjjInvoiceType01.jsp">
		<controller>com.icsc.ap.func.apjcInvoiceTypeFunc</controller>
			<action flag="I" method="query" forward="apjjInvoiceType01.jsp"/>
			<action flag="N" method="create" forward="apjjInvoiceType01.jsp"/>
			<action flag="R" method="update" forward="apjjInvoiceType01.jsp"/>
			<action flag="D" method="delete" forward="apjjInvoiceType01.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcInvoiceTypeVO" type="unique"/>
		</converter>
	</page>
	
	<page pageID="apjjVendor01" path="apjjVendor01.jsp">
        <controller>com.icsc.ap.func.apjcVendorFunc</controller>
        <action flag="I" method="query" forward="apjjVendor01.jsp"/>
        <action flag="R" method="update" forward="apjjVendor01.jsp"/>
        <action flag="D" method="delete" forward="apjjVendor01.jsp"/>
        <converter>
            <valueObject objectID="gp10"
                class="com.icsc.gp.codegen.entity.gpjcBase"
                type="unique"/>
            <valueObject objectID="gp22"
                class="com.icsc.gp.codegen.entity.gpjcFC"
                type="unique"/>
            <valueObject objectID="apgp"
                class="com.icsc.ap.dao.apjcGpVO"
                type="unique"/>    
        </converter>
    </page>
 
    <page pageID="apjjBillApply" path="apjjBillApplyEdit.jsp">
		<controller>com.icsc.ap.func.apjcBillApplyFunc</controller>
			<action flag="I" method="query" forward="apjjBillApplyEdit.jsp"/>
			<action flag="N" method="create" forward="apjjBillApplyEdit.jsp"/>
			<action flag="R" method="update" forward="apjjBillApplyEdit.jsp"/>
			<action flag="D" method="delete" forward="apjjBillApplyEdit.jsp"/>
			<action flag="B" method="doBill" forward="apjjBillApplyEdit.jsp"/>
			<action flag="C" method="cancelBill" forward="apjjBillApplyEdit.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcBillApplyVO" type="unique"/>
			<valueObject objectID="v2" class="com.icsc.ap.dao.apjcBillApplyDetailVO" type="sequence"/>
		</converter>
	</page>
   
    <page pageID="apjjBillEdit" path="apjjBillEdit.jsp">
		<controller>com.icsc.ap.func.apjcBillFunc</controller>
			<action flag="I" method="query" forward="apjjBillEdit.jsp"/>
			<action flag="C" method="cancelConfirm" forward="apjjBillEdit.jsp"/>
			<action flag="B" method="back" forward="apjjBillEdit.jsp"/>
			<action flag="E" method="print" forward="apjjBillEdit.jsp"/>
			<action flag="U" method="preUpdate" forward="apjjBillPayItem.jsp"/>
			<action flag="R" method="update" forward="apjjBillPayItem.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcBillVO" type="unique"/>
			<valueObject objectID="v3" class="com.icsc.ap.dao.apjcBillPaymentVO" type="sequence"/>
			<valueObject objectID="v4" class="com.icsc.ap.dao.apjcBillDrItemVO" type="sequence"/>
		</converter>
	</page>
	
	<page pageID="apjjBillConfirm" path="apjjBillConfirm.jsp">
		<controller>com.icsc.ap.func.apjcBillFunc</controller>
			<action flag="P" method="preConfirm" forward="apjjBillConfirm.jsp"/>
			<action flag="C" method="confirm" forward="apjjBillConfirm.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcBillVO" type="unique"/>
			<valueObject objectID="v2" class="com.icsc.ap.dao.apjcCx01PayApplyVO" type="sequence"/>
			<valueObject objectID="v3" class="com.icsc.ap.dao.apjcPayDetailVO" type="sequence"/>
			<valueObject objectID="v4" class="com.icsc.ap.dao.apjcCx06DetailVO" type="sequence"/>
			<valueObject objectID="v5" class="com.icsc.ap.dao.apjcCx06VO" type="unique"/>
			<valueObject objectID="v6" class="com.icsc.ap.dao.apjcCx01BillVO" type="sequence"/>
			<valueObject objectID="v7" class="com.icsc.ap.dao.apjcVchrLineVO" type="sequence"/>
		</converter>
	</page>

	<page pageID="apjjPayApplyList" path="apjjPayApplyList.jsp">
        <controller>com.icsc.ap.func.apjcPayApplyFunc</controller>
        <action flag="D" method="delete" forward="apjjPayApplyList.jsp"/>
         <action flag="PN" method="sentNC" forward="apjjPayApplyList.jsp"/>
        <action flag="PC" method="sentCombineNC" forward="apjjPayApplyList.jsp"/>
        <action flag="CN" method="cancelSentNC" forward="apjjPayApplyList.jsp"/>
        <converter>
            <valueObject objectID="v"
                class="com.icsc.ap.dao.apjcPayApplyVO"
                type="unique"/>  
        </converter>
    </page>  

	<page pageID="apjjPayApplyEditYf" path="apjjPayApplyEditYf.jsp">
        <controller>com.icsc.ap.func.apjcPayApplyYfFunc</controller>
        <action flag="I" method="query" forward="apjjPayApplyEditYf.jsp"/>
        <action flag="N" method="insert" forward="apjjPayApplyEditYf.jsp"/>
        <action flag="R" method="update" forward="apjjPayApplyEditYf.jsp"/>
        <action flag="D" method="delete" forward="apjjPayApplyEditYf.jsp"/>
         <action flag="PN" method="sentNC" forward="apjjPayApplyEditYf.jsp"/>
        <action flag="CN" method="cancelSentNC" forward="apjjPayApplyEditYf.jsp"/>
        <converter>
            <valueObject objectID="v"
                class="com.icsc.ap.dao.apjcPayApplyVO"
                type="unique"/>  
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcPayApplyDetailVO"
                type="sequence"/>  
        </converter>
    </page> 
	
	<page pageID="apjjPayList" path="apjjPayList.jsp">
		<controller>com.icsc.ap.func.apjcPayFunc</controller>
			<action flag="N" method="create" forward="apjjPayList.jsp"/>
			<action flag="D" method="remove" forward="apjjPayList.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcPayVO" type="sequence"/>
		</converter>
	</page>
	
	<page pageID="apjjPayEdit" path="apjjPayEdit.jsp">
		<controller>com.icsc.ap.func.apjcPayEditFunc</controller>
			<action flag="I" method="query" forward="apjjPayEdit.jsp"/>
			<action flag="N" method="create" forward="apjjPayEdit.jsp"/>
			<action flag="R" method="update" forward="apjjPayEdit.jsp"/>
			<action flag="D" method="remove" forward="apjjPayEdit.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.ap.dao.apjcPayVO" type="unique"/>
			<valueObject objectID="v2" class="com.icsc.ap.dao.apjcPayApplyVO" type="sequence"/>
			<valueObject objectID="v3" class="com.icsc.ap.dao.apjcPayDetailVO" type="sequence"/>
		</converter>
	</page>

    <page pageID="apjjCx01Edit" path="apjjCx01Edit.jsp" uiCtrl="true">
        <controller>com.icsc.ap.func.apjcCx01Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx01Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcCx01VO"
                type="unique"/>  
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcCx01PayApplyVO"
                type="sequence"/>  
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcCx01BillVO"
                type="sequence"/>  
        </converter>
  </page>

    <page pageID="apjjCx02Edit" path="apjjCx02Edit.jsp" >
        <controller>com.icsc.ap.func.apjcCx02Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
				<action flag="R" method="update" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx02Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcCx02VO"
                type="unique"/>
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcCx02BillVO"
                type="sequence"/>  
        </converter>
  </page>

    <page pageID="apjjCx03Edit" path="apjjCx03Edit.jsp" >
        <controller>com.icsc.ap.func.apjcCx03Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx03Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcCx03VO"
                type="unique"/>  
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcCx03ReceiveVO"
                type="sequence"/>  
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcCx03BillVO"
                type="sequence"/>  
        </converter>
  </page>

    <page pageID="apjjCx04Edit" path="apjjCx04Edit.jsp" >
        <controller>com.icsc.ap.func.apjcCx04Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx04Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcCx04VO"
                type="unique"/>  
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcCx04BillVO"
                type="sequence"/>  
        </converter>
  </page>

    <page pageID="apjjCx06Edit" path="apjjCx06Edit.jsp" >
        <controller>com.icsc.ap.func.apjcCx06Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
				<action flag="R" method="update" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx06Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcCx06VO"
                type="unique"/>  
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcCx06DetailVO"
                type="sequence"/>
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcCx06DetailVO"
                type="sequence"/>    
            <valueObject objectID="v4"
                class="com.icsc.ap.dao.apjcVchrLineVO"
                type="sequence"/>  
        </converter>
  </page>
 
     <page pageID="apjjCx08Edit" path="apjjCx08Edit.jsp" >
        <controller>com.icsc.ap.func.apjcCx08Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx08Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v"
                class="com.icsc.ap.dao.apjcCx08VO"
                type="unique"/>  
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcCx08PayApplyVO"
                type="sequence"/>  
        </converter>
  </page>
 
     <page pageID="apjjCx09Edit" path="apjjCx09Edit.jsp" >
        <controller>com.icsc.ap.func.apjcCx09Func</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
				<action flag="R" method="update" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjCx09Confirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcCx09VO"
                type="unique"/>
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcCx09AcctVO"
                type="sequence"/>  
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcCx09BillVO"
                type="sequence"/>  
        </converter>
  </page>
   
	<page pageID="apjjPayPostAA" path="apjjPayPostAA.jsp">
        <controller>com.icsc.ap.func.apjcPayConfirmFunc</controller>
        <action flag="I" method="preConfirm" forward="apjjPayPostAA.jsp"/>
        <action flag="P" method="confirm" forward="apjjPayPostAA.jsp"/>
				<action flag="C" method="cancelConfirm" forward="apjjPayPostAA.jsp"/>
        <converter>
            <valueObject objectID="v"
                class="com.icsc.ap.dao.apjcVchrLineVO"
                type="sequence"/> 
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcPayVO"
                type="sequence"/>  
        </converter>
    </page>  

    <page pageID="apjjExGLEdit" path="apjjExGLEdit.jsp" >
        <controller>com.icsc.ap.func.apjcExGLFunc</controller>
        <action flag="I" method="query" />
        <action flag="N" method="create" />
        <action flag="D" method="delete" />
        <action flag="PC" method="preConfirm" forward="apjjExGLConfirm.jsp"/>
        <action flag="CC" method="cancelConfirm" />
        <action flag="C" method="confirm" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcExGLVO"
                type="unique"/>  
            <valueObject objectID="v2"
                class="com.icsc.ap.dao.apjcExGL01VO"
                type="sequence"/>  
            <valueObject objectID="v3"
                class="com.icsc.ap.dao.apjcVchrLineVO"
                type="sequence"/>  
        </converter>
  </page>

    <page pageID="apjjPostVchrList" path="apjjPostVchrList.jsp" >
        <controller>com.icsc.ap.func.apjcPostVchrFunc</controller>
        <action flag="V" method="prePostAA" forward="apjjPostVchrExe.jsp"/>
        <action flag="P" method="postAA" />
        <action flag="CP" method="cancelPostAA" />
        <converter>
            <valueObject objectID="v1"
                class="com.icsc.ap.dao.apjcAAVO"
                type="sequence"/> 
            <valueObject objectID="v4"
                class="com.icsc.ap.dao.apjcVchrLineVO"
                type="sequence"/>  
        </converter>
    </page>
    <page pageID="apjjCloseEdit" path="apjjCloseEdit.jsp" >
        <controller>com.icsc.ap.func.apjcCloseFunc</controller>
        <action flag="I" method="query" />
        <action flag="C" method="close" />
        <action flag="CC" method="cancelClose" />
        <converter>
        </converter>
    </page>
    
     <page pageID="apjjreport" path="apjjreportview.jsp">
            <controller>com.icsc.ap.print.apjcPrtProxy</controller>
            <action flag="PDF" method="printPDF" forward="apjjreportviewPDF.jsp" />
            <action flag="XLS" method="printXLS" forward="apjjreportview.jsp" />
            <action flag="PDFAJAX" method="printPDFAjax" forward="apjjAjaxEcho.jsp" />
            <action flag="XLSAJAX" method="printXLSAjax" forward="apjjAjaxEcho.jsp" />
            <action flag="JSON" method="getJsonData" forward="apjjAjaxEcho.jsp" />
            <converter>

            </converter>
        </page>
        
    <page pageID="apjjDeptTypeSet" path="apjjDeptTypeSet01.jsp">
        <controller>com.icsc.ap.func.apjcDeptTypeSetFunc</controller>
        <action flag="I" method="query"/>
        <action flag="R" method="modify" />
        <converter>
            <valueObject objectID="v1"
                         class="com.icsc.ap.dao.apjcDeptFeeTypeVO"
                         type="sequence"/>
        </converter>
    </page>
	<page pageID="apjjtrans" path="apjjtrans01List.jsp">
	    <controller>com.icsc.ap.func.apjcTransFunc</controller>
	    <action flag="N" method="create" forward="apjjtrans01List.jsp"/>   
	    <action flag="R" method="update" forward="apjjtrans01List.jsp"/>   
	    <action flag="D" method="delete" forward="apjjtrans01List.jsp"/>   
	    <action flag="P" method="confirm" forward="apjjtrans01List.jsp"/>   
	    <action flag="C" method="cancelConfirm" forward="apjjtrans01List.jsp"/>   
	        <converter>
	        <valueObject objectID="g1"
	                class="com.icsc.ap.dao.apjcTransVO"
	                type="sequence"/>
				</converter>
    </page>
</pages>
