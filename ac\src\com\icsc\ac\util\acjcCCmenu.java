/*
 * Created on 2006/3/27
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package com.icsc.ac.util;

import java.util.*;
import com.icsc.facc.zaf.zafcFactorValue;
import com.icsc.facc.zaf.zafcKey;
import com.icsc.facc.zaf.zafiFactor;
import com.icsc.facc.zaf.dao.zafcrdDAO;
import com.icsc.facc.zaf.dao.zafcrdVO;

/**
 * <AUTHOR>
 * 
 */
public class acjcCCmenu implements zafiFactor {

	public zafcFactorValue[] getData(zafcKey zafKey) {
		String leafId = zafKey.getLeafId();
		String compid = zafKey.getCompId();
		List alst = new LinkedList();
		try {
			Vector vt = new zafcrdDAO(zafKey.getDsCom()).queryByLeafId(compid, leafId);
			Iterator it = vt.iterator();
			while (it.hasNext()) {
				zafcrdVO rdvo = (zafcrdVO) it.next();
				if (!rdvo.getStatus().equals("Y"))
					continue;

				zafcFactorValue vo = new zafcFactorValue();
				vo.setCode(rdvo.getItemA());
				vo.setCompId(compid);
				vo.setDescription(rdvo.getDataA());
				alst.add(vo);
			}

			zafcFactorValue[] rslt = new zafcFactorValue[alst.size()];
			if (alst != null)
				for (int i = 0; i < alst.size(); i++) {
					zafcFactorValue vo = (zafcFactorValue) alst.get(i);
					rslt[i] = vo;
				}
			return rslt;
		} catch (Exception e) {
			return null;
		}
	}
}
