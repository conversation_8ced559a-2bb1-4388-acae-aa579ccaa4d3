package com.icsc.ac.func;

import java.sql.Connection;
import java.util.List;

import com.icsc.ac.dao.acjcv2TJDAO;
import com.icsc.ac.dao.acjcv2TJVO;
import com.icsc.dpms.de.dejc301;
import com.icsc.dpms.de.dejc308;
import com.icsc.dpms.de.structs.dejcFunctionalController;
import com.icsc.facc.zaf.bp.zafcmsg;

public class acjc0806AFunc extends dejcFunctionalController {
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2024/05/28 03:18:32 $";

	private String compId;
	
	public acjc0806AFunc() {
		this.AppId = "ACJC0806AFUNC";
	}

	public void init() throws Exception {
		compId = infoIn.getQryField("compId");
	}

	public void end() {
		infoOut.setParameter("compId", compId);
	}
	
	public void doQuery() throws Exception {
		try {
			acjcv2TJDAO acDAO = new acjcv2TJDAO(dsCom);
			List list = acDAO.findByComp(compId);
			infoOut.addResultVO("v", list);
			if (list.size() > 0) {
				infoOut.setMessage(zafcmsg.INQ_OK);
			} else {
				infoOut.setMessage(zafcmsg.ERR_NOT_FOUND);
			}
		} catch (Exception e) {
			this.handleExp(e);
		}
	}

	public void doInsert() throws Exception {
		dejc301 de301 = new dejc301();
		dejc308 de308 = new dejc308();
		String today = de308.getCrntDateLFmt1();
		try {
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			
			acjcv2TJDAO acDAO = new acjcv2TJDAO(dsCom, con);
			List list = infoIn.getSequenceVOs("v", "box");
			for (int i = 0; i < list.size(); i++) {
				acjcv2TJVO acVO = (acjcv2TJVO) list.get(i);
				acVO.setCreateId(dsCom.user.ID);
				acVO.setUpdateId(dsCom.user.ID);
				acVO.setUpdateDate(today);
			}
			acDAO.createList(list);
			de301.commit();
			this.doQuery();
			infoOut.setMessage(zafcmsg.IST_OK);
		} catch (Exception e) {
			this.handleExp(e);
		} finally {
			de301.close();
		}
	}

	public void doUpdate() throws Exception {
		dejc301 de301 = new dejc301();
		dejc308 de308 = new dejc308();
		String today = de308.getCrntDateLFmt1();
		try {
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			
			acjcv2TJDAO acDAO = new acjcv2TJDAO(dsCom, con);
			List list = infoIn.getSequenceVOs("v", "box");
			for (int i = 0; i < list.size(); i++) {
				acjcv2TJVO acVO = (acjcv2TJVO) list.get(i);				
				acVO.setUpdateId(dsCom.user.ID);
				acVO.setUpdateDate(today);
			}
			acDAO.updateCreateList(list);
			de301.commit();
			this.doQuery();
			infoOut.setMessage(zafcmsg.UPD_OK);
		} catch (Exception e) {
			this.handleExp(e);
		} finally {
			de301.close();
		}
	}

	public void doDelete() throws Exception {
		dejc301 de301 = new dejc301();
		
		try {
			Connection con = de301.getConnection(this.dsCom, this.AppId);
			de301.setAutoCommit(false);
			
			acjcv2TJDAO acDAO = new acjcv2TJDAO(dsCom, con);
			List list = infoIn.getSequenceVOs("v", "box");
			acDAO.removeList(list);
			de301.commit();
			this.doQuery();
			infoOut.setMessage(zafcmsg.DEL_OK);
		} catch (Exception e) {
			this.handleExp(e);
		} finally {
			de301.close();
		}
	}
}
