package com.icsc.ac.api;

import com.icsc.am.dao.amjcbaDAO;
import com.icsc.am.dao.amjcbaVO;
import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;
import java.util.*;

public class acjcAAChkActivity implements dejiKey, dejiCode {
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2021/02/20 06:27:04 $";
	
	HashMap mp = new HashMap();

	public void put(int index, Object value) throws dejcEditException {
		mp.put(String.valueOf(index), (String) value);
	}

	public java.lang.Object get(int index) throws dejcNotFoundException {
		return mp.get(String.valueOf(index));
	}

	public boolean isExist(dsjccom dsCom, dejiKey dekey) throws dejcDAOException {
		try {
			amjcbaVO vo = new amjcbaDAO(dsCom).findByActivityCodeWithoutCC((String) dekey.get(1), (String) dekey.get(3), (String) dekey.get(2));
			return !(vo == null);
		} catch (Exception e) {
			throw new dejcDAOException(e.toString());
		}
	}

	public java.lang.String getName(dsjccom dsCom, dejiKey dekey) throws dejcNotFoundException, dejcDAOException {
		try {
			amjcbaVO vo = new amjcbaDAO(dsCom).findByActivityCodeWithoutCC((String) dekey.get(1), (String) dekey.get(3), (String) dekey.get(2));
			if (vo == null)
				return "";
			else
				return vo.getActivityDesc();
		} catch (Exception e) {
			throw new dejcDAOException(e.toString());
		}
	}
} // end class
