/*
 * Default Layout Theme
 *
 * Created for jquery.layout 
 *
 * Copyright (c) 2008 
 *   Fabrizio Balliano (http://www.fabrizioballiano.net)
 *   <PERSON> (http://allpro.net)
 *
 * Dual licensed under the GPL (http://www.gnu.org/licenses/gpl.html)
 * and MIT (http://www.opensource.org/licenses/mit-license.php) licenses.
 *
 * Last Updated: 2009-03-04
 * NOTE: For best code readability, view this with a fixed-space font and tabs equal to 4-chars
 */

/*
*	BASIC PAGE COSMETICS
*/
body {

}

p {
	margin: 1em 0;
}


/*
*	PANES
*/
.ui-layout-pane { /* all 'panes' */
	background:	#FFF; 
	border:		1px solid #BBB;
	padding:	10px; 
	overflow:	auto;
}


/*
*	RESIZER-BARS
*/
.ui-layout-resizer	{ /* all 'resizer-bars' */
	background:		#DDD;
	border:			1px solid #BBB;
	border-width:	0;
	}
	.ui-layout-resizer-drag {		/* REAL resizer while resize in progress */
	}
	.ui-layout-resizer-open:hover ,	/* hover-color to 'resize' */
	.ui-layout-resizer-dragging {	/* resizer beging 'dragging' */
		background: #C4E1A4;
	}
	.ui-layout-resizer-dragging {	/* CLONED resizer being dragged */
		border:  	1px solid #BBB;
	}
	.ui-layout-resizer-dragging-limit {	/* CLONED resizer at min or max size-limit */
		background: #E18D8D;
	}

	.ui-layout-resizer-closed:hover	{ /* hover-color to 'slide open' */
		background: #EBD5AA;
	}
	.ui-layout-resizer-sliding {	/* resizer when pane was 'slid open' */
		opacity: 0.1; /* show only a slight shadow */
		filter: alpha(opacity=10);
		}
		.ui-layout-resizer-sliding:hover {	/* sliding resizer - hover */
			opacity: 1; /* on:hover, show the resizer-bar normally */
			filter: alpha(opacity=100);
		}
		/* sliding resizer - add 'outside-border' to resizer on:hover */
		.ui-layout-resizer-north-sliding:hover	{ border-bottom-width:	1px; }
		.ui-layout-resizer-south-sliding:hover	{ border-top-width:		1px; }
		.ui-layout-resizer-west-sliding:hover	{ border-right-width:	1px; }
		.ui-layout-resizer-east-sliding:hover	{ border-left-width:	1px; }


/*
*	TOGGLER-BUTTONS
*/
.ui-layout-toggler {
	color: #666;
	border: 1px solid #BBB; /* match pane-border */
	background-color: #999;
	}
	.ui-layout-resizer:hover .ui-layout-toggler {
		filter:		alpha(opacity=60);
		opacity:	.60;
	}
	.ui-layout-resizer:hover .ui-layout-toggler:hover { /* specificity */
		background-color: #FC6;
		filter:		alpha(opacity=100);
		opacity:	1;
	}
	.ui-layout-toggler-north ,
	.ui-layout-toggler-south {
		border-width: 0 1px;
	}
	.ui-layout-toggler-west ,
	.ui-layout-toggler-east {
		border-width: 1px 0;
	}
	/* hide the toggler-button when the pane is 'slid open' */
	.ui-layout-resizer-sliding .ui-layout-toggler {
		display: none;
	}
	/*
	*	style the text we put INSIDE the east/west togglers
	*/
	.ui-layout-toggler .content {
		font-size:		12px;
		font-weight:	bold;
		color:			#666;
		padding-bottom: 0.35ex; /* to 'vertically center' text inside text-span */
	}



