package com.icsc.aa.tag;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.icsc.aa.dao.aajcFactoryDAO;
import com.icsc.aa.dao.aajcFactoryVO;
import com.icsc.dpms.de.dejc318;
import com.icsc.dpms.de.dejcUtility;
import com.icsc.dpms.de.tag.dejcTagRemoteField;
import com.icsc.dpms.de.tag.dejcTagRemoteOption;
import com.icsc.dpms.de.tag.dejiWebDataFinder;
import com.icsc.dpms.ds.dsjccom;

public class aajcRemoteSelectFactoryId implements dejiWebDataFinder {

	public List findFieldValue(dsjccom dsCom, String key, Map params, String rmRtnFlds) {
		return null;
	}
	
	public List findOptions(dsjccom dsCom, String key, String selected,Map params) {
		
		aajcFactoryDAO aaFactoryDAO = new aajcFactoryDAO(dsCom);
		aajcFactoryVO aaFactoryVO = new aajcFactoryVO();
		List OptionList = new ArrayList() ;
		
		try{
			List list = aaFactoryDAO.findByMasterKey(key);
			
			dejcTagRemoteOption optionNull = new dejcTagRemoteOption("","");
			optionNull.setHideValue(true);
			OptionList.add(optionNull);
			
			for(int i = 0;i < list.size();i++){
				aaFactoryVO = (aajcFactoryVO)list.get(i);
				dejcTagRemoteOption option = new dejcTagRemoteOption(aaFactoryVO.getFactoryId(),aaFactoryVO.getFactoryName());
				if(aaFactoryVO.getFactoryId().equals(selected)){
					option.setSelected(true);
				}
				option.setHideValue(true);
				OptionList.add(option);
			}
		}
		catch(Exception e){
			e.printStackTrace();
		}
		
		return OptionList;
	}

}
