/*----------------------------------------------------------------------------*/
/* aajccpLoginMDAO		DAOTool Ver 10.0112 (INPUT FILE VERSION:2.0)
/*----------------------------------------------------------------------------*/
/* author : InfoChamp
/* system : 普通會計管理系統(AA)
/* target : 用戶授權帳套別及廠別維護作業基本資料文件
/* create : 104/08/25
/* update : 104/08/25
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao;

import java.sql.*;
import java.text.*;
import java.util.*;
import java.math.* ;
import com.icsc.dpms.de.*;
import com.icsc.dpms.de.sql.*;
import com.icsc.dpms.de.dao.*;
import com.icsc.dpms.ds.*;

/**
 * 用戶授權帳套別及廠別維護作業基本資料文件 DAO.
 * <pre>
 * Table Name        : DB.TBAACPLOGINM
 * Table Description : 用戶授權帳套別及廠別維護作業基本資料文件
 * Value Object Name : aajccpLoginMVO
 * </pre>
 * @version $Id: aajccpLoginMDAO_1.java,v 1.1 2015/09/30 05:13:30 I27368 Exp $
 * @since aajccpLoginMVO - 104/08/25
 * <AUTHOR>
 */
public class aajccpLoginMDAO extends dejcCommonDAO {
	public final static String AppId = "AAJCCPLOGINMDAO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:30 $";

/*----------------------------------------------------------------------------*/
/* Creates new aajccpLoginMDAO
/*----------------------------------------------------------------------------*/

	/**
	 * 膘凳赽
	 * @param dsCom - 僕蚚啋璃
	 * @since 104/08/25
	 */
	public aajccpLoginMDAO(dsjccom dsCom) {
		super(dsCom, AppId);
	}
	/**
	 * 膘凳赽
	 * @param dsCom - 僕蚚啋璃
	 * @param con - 蝠眢蟀諉唚
	 * @since 104/08/25
	 */
	public aajccpLoginMDAO(dsjccom dsCom, Connection con) {
		super(dsCom, con) ;
		super.appId = this.AppId;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/
	/**
	 * 郪磁眕翋瑩脤戙腔 sql
	 * @since 104/08/25
	 */
	private String getFindByPKSql(String userId) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("SELECT * FROM DB.TBAACPLOGINM");
		sqlStr.append(" WHERE  userId='"+userId+"' ");
		return sqlStr.toString();
	}
	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @return aacpLoginMVO - 等捩訧蹋
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPK(String userId)
			throws SQLException, Exception {
		this.sql = getFindByPKSql(userId) ;
		return (aajccpLoginMVO) this.eQuery(this.sql) ;
	}

	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @return aacpLoginMVO - 等捩訧蹋
	 * @exception dejcNotFoundException - �聹斢輓論岏珅�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPKExp(String userId)
			throws dejcNotFoundException, SQLException, Exception {
		aajccpLoginMVO aacpLoginMVO = findByPK(userId) ;
		if (aacpLoginMVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginMVO ;
	}

	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @param aacpLoginMVO - 蚕妏蚚氪垀枑鼎腔 object, DAO 祥鍚俴 new
	 * @return aacpLoginMVO - 等捩訧蹋
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPK(aajccpLoginMVO aacpLoginMVO, String userId)
			throws SQLException,Exception {
		this.sql = getFindByPKSql(userId) ;
		return (aajccpLoginMVO) this.eQuery(aacpLoginMVO, this.sql) ;
	}

	/**
	 * 眕翋瑩懂脤戙訧蹋
	 * <p>
	 * @param aacpLoginMVO - 蚕妏蚚氪垀枑鼎腔 object, DAO 祥鍚俴 new
	 * @return aacpLoginMVO - 等捩訧蹋
	 * @exception dejcNotFoundException - �聹斢輓論岏珅�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public aajccpLoginMVO findByPKExp(aajccpLoginMVO aacpLoginMVO, String userId)
			throws dejcNotFoundException, SQLException, Exception {
		aacpLoginMVO = findByPK(aacpLoginMVO, userId) ;
		if (aacpLoginMVO == null) {
			throw new dejcNotFoundException(this.sql) ;
		}
		return aacpLoginMVO ;
	}
	/**
	 * 陔崝珨捩訧蹋 throws
	 * <p>
	 * @param aacpLoginMVO - Value Object
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿謹�
	 * @exception dejcDupException - 笭葩瑩硉
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int create(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {

		if (aacpLoginMVO.verify() == false) {
			throw new dejcEditException(aacpLoginMVO.getMessage()) ;
		}
		if (aacpLoginMVO.isKeyOk() == false) {
			throw new dejcEditException("Value of key["+AppId+"].["+aacpLoginMVO.getMessage()+"] is null or empty!") ;
		}

		this.sql = getCreateSql(aacpLoginMVO) ;
		try {
			int rslt = this.executeUpdate(this.sql) ;
			if (rslt == 0) {
				throw new dejcNoUpdateException(this.sql) ;
			}
			return rslt;
		} catch (SQLException sqle) {
            handleDupException(sqle);
            return -1;
		}
	}
	
	public String getCreateSql(aajccpLoginMVO aacpLoginMVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGINM (");
		sqlStr.append("userId,lastCompId,lastFactoryId");
		sqlStr.append(") VALUES (");
		sqlStr.append("'").append(aacpLoginMVO.getUserIdS()).append("','").append(aacpLoginMVO.getLastCompIdS()).append("','").append(aacpLoginMVO.getLastFactoryIdS()).append("'");
		sqlStr.append(")");	
		this.sql = sqlStr.toString() ;
		return this.sql;
	}

	/**
	 * 陔崝嗣捩訧蹋 throws
	 * <p>
	 * @param aacpLoginMVOList - Value Object
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿謹�
	 * @exception dejcDupException - 笭葩瑩硉
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int createList(List aacpLoginMVOList)
			throws dejcEditException, dejcNoUpdateException, dejcDupException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			count = count+this.create(aacpLoginMVO) ;
		}
		return count ;
	}

  	/**
	 * 刉壺訧蹋
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int remove(String userId)
			throws SQLException, Exception {		
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGINM");
		sqlStr.append(" WHERE  userId='"+userId+"' " );
		this.sql = sqlStr.toString();						
		return this.executeUpdate(this.sql) ;
	}
	

	/**
	 * 刉壺訧蹋 throws
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int removeExp(String userId)
			throws dejcNoUpdateException, SQLException, Exception {
		int rslt = remove(userId) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(this.sql) ;
		}
		return rslt ;
	}

	/**
	 * 刉壺訧蹋 throws
	 * <p>
	 * @param aacpLoginMVO 郗刉壺腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int removeExp(aajccpLoginMVO aacpLoginMVO)
			throws dejcNoUpdateException, SQLException, Exception {
		return removeExp(aacpLoginMVO.getUserId()) ;
	}

	/**
	 * 刉壺嗣捩訧蹋 throws
	 * <p>
	 * @param aacpLoginMVOList 郗刉壺腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int removeList(List aacpLoginMVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			count = count+this.remove(aacpLoginMVO.getUserId()) ;
		}
		return count ;
	}

	/**
	 * 刉壺訧蹋 throws
	 * <p>
	 * @param aacpLoginMVO 郗刉壺腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱銅噫�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int remove(aajccpLoginMVO aacpLoginMVO)
			throws SQLException, Exception {
		return remove(aacpLoginMVO.getUserId()) ;
	}

	/**
	 * 党蜊訧蹋
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int update(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginMVO.verify() == false) {
			throw new dejcEditException(aacpLoginMVO.getMessage()) ;
		}
		if(aacpLoginMVO.hasEditFields()) {
			return updateEditFields(aacpLoginMVO);
		} else {
			return this.executeUpdate(getUpdateSql(aacpLoginMVO) ) ;
		}
	}

	public String getUpdateSql(aajccpLoginMVO aacpLoginMVO) {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGINM");
		sqlStr.append(" SET lastCompId='").append(aacpLoginMVO.getLastCompIdS()).append("',lastFactoryId='").append(aacpLoginMVO.getLastFactoryIdS()).append("' ");
		sqlStr.append(" WHERE  userId='"+aacpLoginMVO.getUserId()+"' ");
		this.sql = sqlStr.toString();
		return this.sql ;	
	}
	
	/**
	 * �躆岏珃郺升髲結銓派炬籤肱瑏譫藝裕�
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int updateFldsNotDef(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, SQLException, Exception {
		if (aacpLoginMVO.verify() == false) {
			throw new dejcEditException(aacpLoginMVO.getMessage()) ;
		}
		if (aacpLoginMVO.isKeyOk() == false) {
			throw new dejcEditException("primary key["+aacpLoginMVO.getMessage()+"] of ["+AppId+"] is empty!") ;
		}		
		StringBuffer updateFlds = new StringBuffer();
		if (aacpLoginMVO.getLastCompId() !=null  && !aacpLoginMVO.getLastCompId().equals("") ) {
			updateFlds.append("lastCompId='").append( aacpLoginMVO.getLastCompIdS() ).append("'").append(',') ;
		} 
		if (aacpLoginMVO.getLastFactoryId() !=null  && !aacpLoginMVO.getLastFactoryId().equals("") ) {
			updateFlds.append("lastFactoryId='").append( aacpLoginMVO.getLastFactoryIdS() ).append("'").append(',') ;
		} 
		if(updateFlds.length()==0) {
			return 0;
		}	
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGINM");
		sqlStr.append(" SET ").append( updateFlds.substring(0, updateFlds.length()-1) );
		sqlStr.append(" WHERE  userId='"+aacpLoginMVO.getUserId()+"' ");
		this.sql = sqlStr.toString();
		return this.executeUpdate(this.sql) ;
	}


	/**
	 * 党蜊訧蹋 throws
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿瑏�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int updateExp(aajccpLoginMVO aacpLoginMVO)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int rslt = update(aacpLoginMVO) ;
		if (rslt == 0) {
			throw new dejcNoUpdateException(sql) ;
		}
		return rslt ;
	}

	/**
	 * 党蜊-陔崝嗣捩訧蹋, �蝜�党蜊 0 捩腔趕ㄛ撈陔崝蜆捩訧蹋
	 * <p>
	 * @return int - 蝠眢捩杅
	 * @exception dejcEditException - Value Object 訧蹋衄昫
	 * @exception dejcNoUpdateException - 拸�庥拵岏玿瑏�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int updateCreateList(List aacpLoginMVOList)
			throws dejcEditException, dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			int updateCount = update(aacpLoginMVO) ;
			if ( updateCount==0 ) {
				count += create(aacpLoginMVO) ;
			} else {
				count += updateCount ;
			}
		}
		return count ;
	}

	/**
	 * 党蜊嗣捩訧蹋 throws
	 * <p>
	 * @param aacpLoginMVOList 郗党蜊腔訧蹋昜璃
	 * @return int - 蝠眢捩杅
	 * @exception dejcNoUpdateException - 拸�庥拵岏炱鉼瑏�
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	public int updateList(List aacpLoginMVOList)
			throws dejcNoUpdateException, SQLException, Exception {
		int count = 0 ;
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			count = count+this.updateExp(aacpLoginMVO) ;
		}
		return count ;
	}

	/**
	 * 硒俴 addCreateBatch(Object obj) 奀剒猁蚚善腔 sql
	 * 陔崝訧蹋腔 prepareStatement sql<br>
	 * 森源楊岆葡迡 commonDAO 腔源楊
	 */
    protected String getCreatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("INSERT INTO DB.TBAACPLOGINM  (");
		sqlStr.append("userId,lastCompId,lastFactoryId");
		sqlStr.append(") VALUES (");
		sqlStr.append("?,?,?");
		sqlStr.append(")");		    
		return sqlStr.toString();
    }

	/**
	 * 硒俴 addupdateBatch(Object obj) 奀剒猁蚚善腔 sql
	 * 党蜊訧蹋腔 prepareStatement sql<br>
	 * 森源楊岆葡迡 commonDAO 腔源楊
	 */
    protected String getUpdatePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("UPDATE DB.TBAACPLOGINM");
		sqlStr.append(" SET lastCompId=?,lastFactoryId=? ");
		sqlStr.append(" WHERE  userId=? ");
		return sqlStr.toString();
    }

	/**
	 * 硒俴 addDeleteBatch(Object obj) 奀剒猁蚚善腔 sql
	 * 刉壺訧蹋腔 prepareStatement sql<br>
	 * 森源楊岆葡迡 commonDAO 腔源楊
	 */
    protected String getDeletePreSql() throws SQLException {
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("DELETE FROM DB.TBAACPLOGINM");
		sqlStr.append(" WHERE  userId=? " );
		return sqlStr.toString();
    }

	/**
	 * 硒俴 addCreateBatch(Object obj) 奀剒猁網請腔源楊
	 */
    protected void prepareCreate(Object obj) throws SQLException {
    	aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
		pstmt.setString(1, aacpLoginMVO.getUserId()) ;
		pstmt.setString(2, aacpLoginMVO.getLastCompId()) ;
		pstmt.setString(3, aacpLoginMVO.getLastFactoryId()) ;
    }

	/**
	 * 硒俴 addUpdateBatch(Object obj) 奀剒猁網請腔源楊
	 */
    protected void prepareUpdate(Object obj) throws SQLException {
    	aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
		pstmt.setString(1, aacpLoginMVO.getLastCompId()) ;
		pstmt.setString(2, aacpLoginMVO.getLastFactoryId()) ;
		pstmt.setString(3, aacpLoginMVO.getUserId()) ;
    }

	/**
	 * 硒俴 addDeleteBatch(Object obj) 奀剒猁網請腔源楊
	 */
    protected void prepareDelete(Object obj) throws SQLException {
    	aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
		pstmt.setString(1, aacpLoginMVO.getUserId()) ;
    }

	/**
	 * 妗釬虜濬梗
	 * <p>
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	protected Object getObjFromRS(ResultSet rs) throws SQLException,Exception {
		return aajccpLoginMVO.getInstanceByName(rs) ;
	}

	/**
	 * 妗釬虜濬梗, 妏蚚 user 換輛懂腔 object, 祥鍚俴 new , 眕誹吽硒俴奀潔摯暮砪极<br>
	 * 巠磁還奀俶腔訧蹋堍呾(籵都婓堍呾湮講訧蹋奀ㄛ珨虳戲弇剒猁婃湔婓 value object)<br>
	 * 森源楊硐頗婓硒俴 findByPK(Object obj,String userId) 奀符掩網請﹝
	 * <p>
	 * @exception SQLException - 訧蹋踱渣昫
	 * @since 104/08/25
	 */
	protected Object getObjFromRS(Object obj, ResultSet rs) throws SQLException,Exception {
		return aajccpLoginMVO.getInstanceByName(obj, rs) ;
	}
	
	/**
	  * 掘爺源楊 2009.03.26 陔崝
	  */
	public int backup(String tableName, aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		this.sql = getCreateSql(aacpLoginMVO).replaceFirst(" DB.TBAACPLOGINM "," "+tableName+" "); 
		return this.executeUpdate(this.sql) ;
	} 
	public int backup(String tableName, ResultSet rs) throws SQLException, Exception {
		return backup(tableName, aajccpLoginMVO.getInstanceByName(rs) ) ;
	} 

	public String getUpdateEditFieldsPartialSql(aajccpLoginMVO aacpLoginMVO)  {
		if( aacpLoginMVO.getEditFields()==null ) {
			return "";
		}
		return dejcSqlUtils.genUpdateFields(  aacpLoginMVO.getEditFields() );		
	}
	/**
	  * 党蜊衄雄徹腔戲弇ㄛ森源楊猁衄虴③�楛�衄珂硒俴  aajccpLoginMVO.monitor() 
	  */
	public String getUpdateEditFieldsSql(aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		String updSql =getUpdateEditFieldsPartialSql(aacpLoginMVO) ;
		if(updSql.equals("")){
			return "";
		}
		return "update DB.TBAACPLOGINM set "+updSql+" where   userId='"+aacpLoginMVO.getUserId()+"' ";
	}
	/**
	  * 党蜊衄雄徹腔戲弇ㄛ森源楊猁衄虴③�楛�衄珂硒俴  aajccpLoginMVO.monitor() 
	  */
	public int updateEditFields(aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		this.sql=getUpdateEditFieldsSql(aacpLoginMVO);
		if(this.sql.equals("")){
			// 2010.03.02 植0蜊峈-1ㄛ眕妎梗岆瘁衄硒俴
			return -1;
		}
		return this.executeUpdate(getUpdateEditFieldsSql(aacpLoginMVO)) ;
	}
	/**
	 * 樑update腔sqlㄛ梑郔綴珨跺戲弇懂樑update
	 */	
	private String getLockSqlPrefix() {
		return "update DB.TBAACPLOGINM set lastFactoryId=lastFactoryId ";
	}

	/**
	 * 脤戙﹜坶隅﹜潼諷訧蹋
	 */	
	public aajccpLoginMVO loadByPK4Update(String userId) throws SQLException, Exception {
		List aList = loadList4Update("WHERE   userId='"+userId+"' ");
		if(aList.size()>0) {
			return (aajccpLoginMVO)aList.get(0);
		}else {
			throw new dejcNotFoundException(this.sql) ;	
		}	
	}
	
	private String getSelectSqlPrefix() {
		return "select * from DB.TBAACPLOGINM ";
	}
	
	private List loadList4Update(String conditionSql) throws SQLException, Exception {
		this.executeUpdate(getLockSqlPrefix()+conditionSql) ;
		this.sql=getSelectSqlPrefix()+conditionSql ;
		return monitor(this.eQueryAll( this.sql ));
	}
	/**
	  *潼諷訧蹋
	  */
	private List monitor(List aacpLoginMVOList){
		for (int i=0; i<aacpLoginMVOList.size(); i++) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)aacpLoginMVOList.get(i) ;
			aacpLoginMVO.monitor();
		}	
		return aacpLoginMVOList;
	}
	/**
	 * 眕 VO 檜堤 DB 訧蹋ㄛ檜訧蹋肮奀頗坶隅
	 */
	public aajccpLoginMVO loadByVO(aajccpLoginMVO aacpLoginMVO) throws SQLException, Exception {
		return loadByPK4Update(aacpLoginMVO.getUserId());
	}
	/**
	 * 眕 VO List 檜堤 DB 訧蹋ㄛ檜訧蹋肮奀頗坶隅
	 */
	public List loadByList(List aacpLoginMVOList) throws SQLException, Exception {
		List rsltList= new ArrayList(aacpLoginMVOList.size());
		for (Iterator iterator = aacpLoginMVOList.iterator(); iterator.hasNext();) {
			aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO) iterator.next();
			rsltList.add( loadByVO(aacpLoginMVO) ) ;
		}
		return rsltList;
	}
	/**
	 * 眕雄怓脤戙沭璃脤戙
	 */
	public Vector findByCriteria(dejcCriteria criteria)throws dejcNotFoundException, SQLException, Exception{
		return findByCriteria(criteria,"");
	}
	/**
	 * 眕雄怓脤戙沭璃脤戙
	 */
	public Vector findByCriteria(dejcCriteria criteria,String orderby)throws dejcNotFoundException, SQLException, Exception{
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append(getSelectSqlPrefix());
		if(!dejcUtility.isNull(orderby)) {
			orderby="order by "+orderby;
		}
		sqlStr.append(criteria.toSqlWithWhereIfExists()).append(orderby);  
		this.sql = sqlStr.toString();
		return this.eQueryAll(this.sql) ;
	}
	
//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

    public List findAll() throws SQLException, Exception{
    	this.sql = "select * from DB.TBAACPLOGINM ";
    	return this.eQueryAll(this.sql);
    }

} // end of Class aajccpLoginMDAO