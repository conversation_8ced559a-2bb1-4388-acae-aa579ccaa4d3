<?xml version="1.0" encoding="utf-8"?>
<pages>
	<version>2.0</version>
	<!--原料基本资料维护-->
	<page pageID="mrjj001AEdit" path="mrjj001AEdit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="s" class="com.icsc.mf.dao.mfjctbV002VO" type="unique"/>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbV003VO" type="unique"/>
			<valueObject objectID="s2" class="com.icsc.mf.dao.mfjctbV032VO" type="unique"/>
			<valueObject objectID="s3" class="com.icsc.mf.dao.mfjctbV032VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj001BEdit" path="mrjj001BEdit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbV003VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj001CList" path="mrjj001CList.jsp" uiCtrl="true" >
		<action flag="I" method="doQuery" validate="doQuery_validate" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<!-- <action flag="T" method="doConfirm" /> -->
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbV004VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj001DList" path="mrjj001DList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbC323VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj001EList" path="mrjj001EList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete" />
		<action flag="O" method="doOpen"/>
		<action flag="C" method="doClose"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR008VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj001FList" path="mrjj001FList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete" />
		<action flag="U" method="doUpLoad" />
		<converter>
			<valueObject objectID="t" class="com.icsc.mf.dao.mfjctbV009VO" type="sequence"/>
		</converter>
	</page>
	<!--储位资料维护  -->
	<page pageID="mrjj002Edit" path="mrjj002Edit.jsp"   uiCtrl="true">
		<controller>com.icsc.mr.func.mrjc002EditFunc</controller>
		<action flag="W" method="wel"  />
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate"  />
		<action flag="D" method="doDelete"  />
		<converter>
			<valueObject objectID="m" class="com.icsc.mf.dao.mfjctbV015VO" type="sequence"/>
		</converter>
	</page>
	<!--种类代码维护  -->
	<page pageID="mrjj003Edit" path="mrjj003Edit.jsp"  uiCtrl="true" >
		<action flag="W" method="wel"  />
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate"  />
		<action flag="D" method="doRemove"  />
		<converter>
			<valueObject objectID="m" class="com.icsc.mf.dao.mfjctbV001VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj004List" path="mrjj004List.jsp"  uiCtrl="true" >
		<action flag="W" method="wel"  />
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate"  />
		<action flag="D" method="doDelete"  />
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbV004VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj008List" path="mrjj008List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete" />
		<action flag="UC" method="doUpLoad"/>
		<action flag="UM" method="doUpLoad"/>
		<action flag="UL" method="doUpLoad"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbC323VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj013List" path="mrjj013List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="N" method="doCreate" validate="create_validate"/>
		<action flag="R" method="doUpdate" validate="update_validate"/>
		<action flag="D" method="doDelete" validate="delete_validate"/>
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mf.dao.mfjctbV046VO"
						 type="sequence"/>
		</converter>
	</page>

	<!--储位损维�?  -->
	<page pageID="mrjj020List" path="mrjj020List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb015VO" type="sequence"/>
		</converter>
	</page>
	<!--运杂费资料维�?  -->
	<page pageID="mrjj021Edit" path="mrjj021Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb002VO" type="sequence"/>
		</converter>
	</page>
	<!--运杂费资料维�?  -->
	<page pageID="mrjj021List" path="mrjj021List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="s1" class="com.icsc.mr.dao.mrjctb004VO" type="sequence"/>
		</converter>
	</page>
	<!--变转料设定维�? -->
	<page pageID="mrjj023List" path="mrjj023List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb009VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj027Edit" path="mrjj027Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mf.dao.mfjctbC030VO"
						 type="unique"/>
		</converter>
	</page>

	<!--外销副产品回收设定作�?  -->
	<page pageID="mrjj028List" path="mrjj028List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="s1" class="com.icsc.mr.dao.mrjctb012VO" type="sequence"/>
		</converter>
	</page>

	<!--混匀料分摊比例设�?  -->
	<page pageID="mrjj035List" path="mrjj035List.jsp"   uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate"  />
		<action flag="D" method="doDelete"  />
		<converter>
			<valueObject objectID="g1" class="com.icsc.mr.dao.mrjctb016VO" type="sequence"/>
		</converter>
	</page>

	<!-- 厂内倒运计划申请作业  -->
	<page pageID="mrjj050List" path="mrjj050List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete" />
		<action flag="O" method="doOpen"/>
		<action flag="C" method="doClose"/>
		<action flag="SO" method="doSendOut"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="PE" method="doPrintExcel" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR008VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj050AList" path="mrjj050AList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mf.dao.mfjctbR008VO" type="unique"/>
			<valueObject objectID="v2" 	class="com.icsc.mf.dao.mfjctbC009VO" 	 type="sequence"/>

		</converter>
	</page>
	<page pageID="mrjj050BList" path="mrjj050BList.jsp" >
		<action flag="I" method="doQuery" />
		<converter>

		</converter>
	</page>
	<!-- 铁运内过磅申请作 -->
	<page pageID="mrjj051List" path="mrjj051List.jsp" uiCtrl="true" >
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate"  validate="doCreate_validate" />
		<action flag="R" method="doUpdate"  validate="doUpdate_validate"  />
		<action flag="D" method="doDelete"  validate="doDelete_validate"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb008VO" type="sequence"/>
		</converter>
	</page>
	<!--厂内倒运过磅实绩查询  -->
	<page pageID="mrjj052List" path="mrjj052BList.jsp" >
		<action flag="I" method="doQuery" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR016VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj055List" path="mrjj055List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR016VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj053List" path="mrjj053List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm"/>
		<action flag="N" method="doCreate"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctbL59VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj056List" path="mrjj056List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR016VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj057List" path="mrjj057List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR016VO" type="sequence"/>
		</converter>
	</page>
	<!-- 水运预报作业  -->
	<page pageID="mrjj060List" path="mrjj060List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="A" method="doClose"/>
		<action flag="C" method="doOpen"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR031VO" type="sequence"/>
		</converter>
	</page>
	<!-- 铁运预报作业  -->
	<page pageID="mrjj061List" path="mrjj061List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="CL" method="doClose"/>
		<action flag="C" method="doOpen"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="T" method="doConfirm"/>
		<action flag="P" method="doPrint"/>
		<action flag="DL" method="doDownLoad"/>
		<action flag="S" method="doSend"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR030VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj062List" path="mrjj062List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="CL" method="doClose"/>
		<action flag="C" method="doOpen"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="T" method="doConfirm"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR030VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj063List" path="mrjj063List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="CL" method="doWithdraw"/>
		<action flag="C" method="doOpen"/>
		<action flag="S" method="doSend"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="T" method="doConfirm"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR030VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj064List" path="mrjj064List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="CL" method="doClose"/>
		<action flag="C" method="doOpen"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="T" method="doConfirm"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR030VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj065List" path="mrjj065List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR032VO" type="sequence"/>
		</converter>
		<converter>
			<valueObject objectID="p" class="com.icsc.mf.dao.mfjctbR032VO" type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj065DetailList" path="mrjj065DetailList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR030VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj066List" path="mrjj066List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="SE" method="doSet"/>
		<action flag="X" method="doXctl"/>
		<action flag="R2" method="doUpdate2"/>
		<action flag="R3" method="doAccess"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb025VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj067List" path="mrjj067List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="R2" method="doUpdate2"/>
		<action flag="R3" method="doUpdate3"/>
		<action flag="D" method="doDelete"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="T" method="doConfirm"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb025VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj068List" path="mrjj068List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR020VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj069List" path="mrjj069List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR018VO" type="sequence"/>
		</converter>
	</page>

	<!--船运资料作业  -->
	<page pageID="mrjj070Edit" path="mrjj070Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D" method="doDelete" validate="doDelete_validate"/>
		<action flag="R2" method="doUpdate2" validate="doUpdate2_validate"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb042VO" type="unique"/>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctbL20VO" type="unique"/>
			<valueObject objectID="s1" class="com.icsc.mf.dao.mfjctbR051VO" type="sequence"/>
			<valueObject objectID="s2" class="com.icsc.mr.dao.mrjctb003VO" type="sequence" />
			<valueObject objectID="s3" class="com.icsc.mf.dao.mfjctbC323VO" type="sequence" />
		</converter>
	</page>

	<!--移存船运资料作业  -->
	<page pageID="mrjj071Edit" path="mrjj071Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb042VO" type="unique"/>
		</converter>
	</page>

	<!--船只到港作业  -->
	<page pageID="mrjj073Edit" path="mrjj073Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="R" method="doUpdate" />
		<action flag="R2" method="doUpdate2" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="ov" class="com.icsc.mr.dao.mrjctb042VO" type="sequence"/>
			<valueObject objectID="av" class="com.icsc.mr.dao.mrjctb042VO" type="sequence"/>
		</converter>
	</page>

	<!--船只到港重量验收作业  -->
	<page pageID="mrjj074Edit" path="mrjj074Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="SE" method="doSet" />
		<action flag="AN" method="doAbrogate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="M" method="doMassPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="RG" method="doUpdateRG" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb042VO" type="unique"/>
			<valueObject objectID="s" class="com.icsc.mr.dao.mrjctb042VO" type="sequence"/>
		</converter>
	</page>

	<!-- 借料作业 -->
	<page pageID="mrjj075Edit" path="mrjj075Edit.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="T" method="doConfirm" />
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
			<valueObject objectID="v3" class="com.icsc.mr.dao.mrjctb031VO" type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj075List" path="mrjj075List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doUpdate"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mf.dao.mfjctbR051VO" type="sequence"/>
		</converter>
	</page>

	<!-- 还料作业 -->
	<page pageID="mrjj076Edit" path="mrjj076Edit.jsp"   uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="T" method="doConfirm" />
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
			<valueObject objectID="v3" class="com.icsc.mr.dao.mrjctb031VO" type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj076List" path="mrjj076List.jsp"   uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mf.dao.mfjctbR051VO" type="sequence"/>
		</converter>
	</page>

	<!--  还料明细 -->
	<page pageID="mrjj076AList" path="mrjj076AList.jsp"   uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="D" method="doDelete"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="sequence"/>
		</converter>
	</page>

	<!--铁运归类作业  -->
	<page pageID="mrjj080Edit" path="mrjj080Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate" validate="doCreate_validate"/>
		<action flag="R" method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D" method="doDelete" validate="doDelete_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="P" method="doPrint" forward="mrjjDownLoad.jsp" />
		<action flag="SP" method="doSplit" validate="doSplit_validate"/>
		<action flag="X" method="addRow"/>
		<action flag="E" method="setCarNo" validate="setCarNo_validate" forward="mrjjSetCarNo.jsp"/>
		<action flag="O" method="setHandChkNo" forward="mrjjSetHandChk.jsp"/>
		<action flag="U" method="doUpLoad" validate="doUpLoad_validate"/>
		<action flag="SO" method="doSendOut"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--铁运化验资料维护作业  -->
	<page pageID="mrjj080List" path="mrjj080List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate" validate="doCreate_validate"/>
		<action flag="R" method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D" method="doDelete" validate="doDelete_validate"/>
		<action flag="SP" method="doSplit" validate="doSplit_validate"/>
		<action flag="U" method="doUpLoad" validate="doUpLoad_validate"/>
		<action flag="P" method="doPrint" forward="mrjjDownLoad.jsp" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--铁运组批�? -->
	<page pageID="mrjj081Edit" path="mrjj081Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="SE" method="doSet" validate="doSet_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="P" method="doPrint"  forward="mrjjDownLoad.jsp"/>
		<action flag="O" method="setHandChkNo" forward="mrjjSetHandChk.jsp"/>
		<action flag="M" method="doMerger" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj081List" path="mrjj081List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="SE" method="doSet"    validate="doSet_validate"/>
		<action flag="C"  method="doCancel" validate="doCancel_validate"/>
		<action flag="M"  method="doMerge" />
		<action flag="P"  method="doPrint"  forward="mrjjDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--铁运入储作业  -->
	<page pageID="mrjj082Edit" path="mrjj082Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="O" method="doUpdate" />
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="P2" method="doPrint2"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj083List" path="mrjj083List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="CF" method="doConfirm" />
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj084List" path="mrjj084List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="U" method="doUpLoad" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj085List" path="mrjj085List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="U" method="doUpLoad" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj086List" path="mrjj086List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="CF" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C"  method="doCancel"  validate="doCancel_validate"/>
		<action flag="U"  method="doUpLoad" />
		<action flag="P"  method="doPrint"   forward="mrjjRptDownLoad.jsp"/>
		<action flag="P2" method="doPrint2"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj087List" path="mrjj087List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="U" method="doUpLoad" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj088List" path="mrjj088List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="U" method="doUpLoad" />
		<action flag="S"  method="doSubmit" />
		<action flag="W"  method="doWithdraw" />
		<action flag="P"  method="doPrint" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj089List" path="mrjj089List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="U" method="doUpLoad" />
		<action flag="CF" method="doConfirm" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--汽运归类作业  -->
	<page pageID="mrjj090Edit" path="mrjj090Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate" validate="doCreate_validate"/>
		<action flag="R" method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D" method="doDelete" validate="doDelete_validate"/>
		<action flag="P" method="doPrint"  forward="mrjjDownLoad.jsp"/>
		<action flag="SE" method="doSet" validate="doSet_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="X" method="addRow"/>
		<action flag="E" method="setCarNo" validate="setCarNo_validate" forward="mrjjSetCarNo.jsp"/>
		<action flag="O" method="setHandChkNo" forward="mrjjSetHandChk.jsp"/>
		<action flag="U" method="doUpLoad" validate="doUpLoad_validate"/>
		<action flag="SO" method="doSendOut"/>
		<action flag="SW" method="doSendWGT"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--汽运�?(�?)批作�?  -->
	<page pageID="mrjj091Edit" path="mrjj091Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="SE" method="doSet" validate="doSet_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="P" method="doPrint"  forward="mrjjDownLoad.jsp"/>
		<action flag="O" method="setHandChkNo" forward="mrjjSetHandChk.jsp"/>
		<action flag="M" method="doMerger" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--汽运入储作业  -->
	<page pageID="mrjj092Edit" path="mrjj092Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="O" method="doUpdate" />
		<action flag="R" method="doUpdate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="P2" method="doPrint2"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj096List" path="mrjj096List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="R2"  method="doUpdate" />
		<action flag="R3"  method="doUpdate" />
		<action flag="CF" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C"  method="doCancel"  validate="doCancel_validate"/>
		<action flag="P"  method="doPrint"   forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj096AList" path="mrjj096AList.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="R"  method="doUpdate" />
		<action flag="SP"  method="doSplit" />
		<action flag="C"  method="doCancel" />
		<action flag="P"  method="doPrint"   forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj097List" path="mrjj097List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="SE" method="doSet" validate="doSet_validate"/>
		<action flag="C" method="doCancel" validate="doCancel_validate"/>
		<action flag="M" method="doMerger" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="PA" method="doPrintA"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj099List" path="mrjj099List.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" />
		<action flag="CF" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C"  method="doCancel"  validate="doCancel_validate"/>
		<action flag="P"  method="doPrint"   forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>
	<!--品质验收作业  -->
	<page pageID="mrjj100Edit" path="mrjj100Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="R"  method="doUpdate"     validate="doUpdate_validate"/>
		<action flag="T"  method="doConfirm"    validate="doConfirm_validate"/>
		<action flag="C"  method="doCancel"     validate="doCancel_validate"/>
		<action flag="R1"  method="doUpdate2"   validate="doUpdate2_validate"/>
		<action flag="R3"  method="doUpdate3"    />
		<action flag="P"  method="doPrint"   forward="mrjjRptDownLoad.jsp"  />
		<action flag="N"  method="doCreate"   validate="doCreate_validate"/>
		<action flag="R2"  method="doUpdate051"   validate="doUpdate051_validate"/>
		<action flag="D"  method="doDelete"   validate="doDelete_validate"/>
		<converter>
			<valueObject objectID="u"
						 class="com.icsc.mf.dao.mfjctbR050VO"
						 type="unique"/>
			<valueObject objectID="s1"
						 class="com.icsc.mf.dao.mfjctbR051VO"
						 type="sequence"/>
		</converter>
	</page>
	<!--生铁入库作业  -->
	<page pageID="mrjj101AEdit" path="mrjj101AEdit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" />
		<action flag="R"  method="doUpdate" />
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm"/>
		<action flag="C"  method="doCancel"/>
		<action flag="SO" method="doSendOut"/>
		<action flag="CP" method="doCancelPut"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb044VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj101BList" path="mrjj101BList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="Q" method="doQueryF" forward="/erp/mr/jsp/mrjj102BEditList.jsp" />
		<action flag="O" method="sure" />
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb032VO"
						 type="sequence"/>
			<valueObject objectID="v1"
						 class="com.icsc.mf.dao.mfjctbR016VO"
						 type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj101CList" path="mrjj101CList.jsp">
		<action flag="I" method="doQuery" />
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mf.dao.mfjctbR051VO"
						 type="sequence"/>
		</converter>
	</page>

	<!--铁合金入库作  -->
	<page pageID="mrjj102AEdit" path="mrjj102AEdit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" />
		<action flag="R"  method="doUpdate" />
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm"/>
		<action flag="C"  method="doCancel"/>
		<action flag="SO"  method="doSendOut"/>
		<action flag="CP" method="doCancelPut"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb044VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj102BEdit" path="mrjj102BEdit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="Q" method="doQueryF" forward="/erp/mr/jsp/mrjj102BEditList.jsp" />
		<action flag="O" method="sure" />
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb032VO"
						 type="sequence"/>
			<valueObject objectID="v1"
						 class="com.icsc.mf.dao.mfjctbR016VO"
						 type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj102CEdit" path="mrjj102CEdit.jsp" uiCtrl="false">
		<action flag="I" method="doQuery" />
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mf.dao.mfjctbR051VO"
						 type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj103Edit" path="mrjj103Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" />
		<action flag="R"  method="doUpdate" />
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm"/>
		<action flag="C"  method="doCancel"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj103AEdit" path="mrjj103AEdit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" />
		<action flag="R"  method="doUpdate" />
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm"/>
		<action flag="C"  method="doCancel"/>
		<action flag="P"  method="put"/>
		<action flag="CP" method="cancelPut"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj103BEdit" path="mrjj103BEdit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="Q" method="queryF" forward="/erp/mr/jsp/mrjj102BEditList.jsp" />
		<action flag="O" method="sure" />
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb031VO"
						 type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj104AEdit" path="mrjj104AEdit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" />
		<action flag="R"  method="doUpdate" />
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<action flag="P"  method="put"/>
		<action flag="CP" method="cancelPut"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj104BEdit" path="mrjj104BEdit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="Q" method="queryF" forward="/erp/mr/jsp/mrjj102BEditList.jsp" />
		<action flag="O" method="sure" />
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mr.dao.mrjctb031VO"
						 type="sequence"/>
		</converter>
	</page>

	<!--委外加工成品入库作业  -->
	<page pageID="mrjj105Edit" path="mrjj105Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate"  />
		<action flag="R" method="doUpdate"  />
		<action flag="D" method="doDelete"  />
		<action flag="T" method="doConfirm"  />
		<action flag="C" method="doCancel"  />
		<action flag="P" method="doPrint"   />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>
	<!--委外加工成品直接领用作业  -->
	<page pageID="mrjj106Edit" path="mrjj106Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate"  />
		<action flag="R" method="doUpdate"  />
		<action flag="D" method="doDelete"  />
		<action flag="T" method="doConfirm"  />
		<action flag="C" method="doCancel"  />
		<action flag="P" method="doPrint"   />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--通用录帐作业  -->
	<page pageID="mrjj107Edit" path="mrjj107Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!-- 其他入库运杂费明�? -->
	<page pageID="mrjj107List" path="mrjj107List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb031VO" type="sequence"/>
		</converter>
	</page>

	<!-- 铁合金入�? -->
	<page pageID="mrjj108AEdit" path="mrjj108AEdit.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb044VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj108BList" path="mrjj108BList.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb044VO" type="unique"/>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb032VO" type="unique"/>
			<valueObject objectID="q" class="com.icsc.mr.dao.mrjctb032VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj112Edit" path="mrjj112Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--废钢入库作业  -->
	<page pageID="mrjj120AEdit" path="mrjj120AEdit.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="R2" method="doUpdate2" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<action flag="P" method="doPrint" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj120BList" path="mrjj120BList.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb032VO" type="unique"/>
			<valueObject objectID="q" class="com.icsc.mr.dao.mrjctb032VO" type="sequence"/>
		</converter>
	</page>

	<!--公司/自产废钢验收作业  -->
	<page pageID="mrjj121Edit" path="mrjj121Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="R2" method="doUpdate2" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<action flag="P" method="doPrint"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb031VO" type="unique"/>
		</converter>
	</page>

	<!--废钢回收作业  -->
	<page pageID="mrjj123AEdit" path="mrjj123AEdit.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="R2" method="doUpdate2" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj123BList" path="mrjj123BList.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb032VO" type="unique"/>
			<valueObject objectID="q" class="com.icsc.mr.dao.mrjctb032VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj125AEdit" path="mrjj125AEdit.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<action flag="R2" method="doUpdate2" />
		<action flag="T" method="doConfirm"/>
		<action flag="C" method="doCancel"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj125BList" path="mrjj125BList.jsp"  uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb045VO" type="unique"/>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb032VO" type="unique"/>
			<valueObject objectID="q" class="com.icsc.mr.dao.mrjctb032VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj130Edit" path="mrjj130Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!-- 铁合金出库作  -->
	<page pageID="mrjj131Edit" path="mrjj131Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--钢坯出库  -->
	<page pageID="mrjj132Edit" path="mrjj132Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1" />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<!--领料作业  -->
	<page pageID="mrjj133Edit" path="mrjj133Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>

	<!--逄料作页  -->
	<page pageID="mrjj134Edit" path="mrjj134Edit.jsp" uiCtrl="true" >
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<!--自产入库作业  -->
	<page pageID="mrjj135Edit" path="mrjj135Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C"  method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>
	<!--回收入库作业  -->
	<page pageID="mrjj136Edit" path="mrjj136Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>


	<page pageID="mrjj137Edit" path="mrjj137Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="R2"  method="doUpdate2" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="sequence"/>
		</converter>
	</page>

	<!--锄售出库作页  -->
	<page pageID="mrjj138Edit" path="mrjj138Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj139Edit" path="mrjj139Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>

	<!--变料作业  -->
	<page pageID="mrjj140Edit" path="mrjj140Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>
	<!--移存作业  -->
	<page pageID="mrjj141Edit" path="mrjj141Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--国内锄售作页  -->
	<page pageID="mrjj143Edit" path="mrjj143Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--验收调整作业  -->
	<page pageID="mrjj144Edit" path="mrjj144Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj145Edit" path="mrjj145Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<!--盘盈亏调整作�?  -->
	<page pageID="mrjj146Edit" path="mrjj146Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--通用录帐作业  -->
	<page pageID="mrjj150Edit" path="mrjj150Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--通用录帐作业  -->
	<page pageID="mrjj151Edit" path="mrjj151Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--通用录帐作业  -->
	<page pageID="mrjj151List" path="mrjj151List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="C" method="doConfirm" validate="doConfirm_validate" />
		<action flag="U" method="doUpLoad" validate="doUpLoad_validate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="t" class="com.icsc.mr.dao.mrjctb030VO" type="sequence"/>
		</converter>
	</page>

	<!--通用录帐作业  -->
	<page pageID="mrjj152Edit" path="mrjj152Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj153Edit" path="mrjj153Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="R2"  method="doUpdate2" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="sequence"/>
		</converter>
	</page>

	<!-- 料号替换作业 -->
	<page pageID="mrjj165Edit" path="mrjj165Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj170List" path="mrjj170List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doUpdate"  />
		<converter>
			<valueObject objectID="t" class="com.icsc.mr.dao.mrjctb031VO" type="sequence"/>
		</converter>
	</page>
	<!-- 各类问题统计与追踪查询报�? -->
	<page pageID="mrjj171List" path="mrjj171List.jsp" uiCtrl="false">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="F" method="doConfirm" validate="doConfirm_validate"/>
		<action flag="P"  method="doPrint" />
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctbL60VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj171AEdit" path="mrjj171AEdit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" validate="create_validate"/>
		<action flag="R" method="doUpdate" validate="update_validate" />
		<action flag="D" method="doDelete" validate="delete_validate" />
		<action flag="B" method="doDistribute" />
		<action flag="C" method="doReceive" />
		<action flag="E" method="doHandle" />
		<action flag="CF" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctbL60VO" type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj171BEdit" path="mrjj171BEdit.jsp" uiCtrl="false">
		<action flag="I" method="doQuery" />
		<converter>
			<valueObject objectID="v2" class="com.icsc.mr.dao.mrjctbL60VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj180Edit" path="mrjj180Edit.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>

	<page pageID="mrjj190List" path="mrjj190List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="t" class="com.icsc.mf.dao.mfjctbVC26VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj191List" path="mrjj191List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="t"  class="com.icsc.mr.dao.mrjctb041VO"  type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj192List" path="mrjj192List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="t"  class="com.icsc.mr.dao.mrjctb041VO"  type="sequence"/>
		</converter>
	</page>

	<!-- 铁运入储合同修改作业 -->
	<page pageID="mrjj193List" path="mrjj193List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="t"  class="com.icsc.mr.dao.mrjctb041VO"  type="sequence"/>
		</converter>
	</page>

	<!-- 汽运入储合同修改作业 -->
	<page pageID="mrjj194List" path="mrjj194List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="t"  class="com.icsc.mr.dao.mrjctb041VO"  type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj195List" path="mrjj195List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<converter>
			<valueObject objectID="t" class="com.icsc.mf.dao.mfjctbVC26VO" type="sequence"/>
		</converter>
	</page>

	<!-- 港口收发存台账作�?	-->
	<page pageID="mrjj200List" path="mrjj200List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<!-- 船运公司承运明细�? -->
	<page pageID="mrjj211List" path="mrjj211List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<!-- 料号数量收发�? (区分收料暂估) -->
	<page pageID="mrjj240List" path="mrjj240List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>
	<!-- 料号数量收发�? (区分成本中心) -->
	<page pageID="mrjj241List" path="mrjj241List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj242List" path="mrjj242List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj243List" path="mrjj243List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>
	<!-- 库存资料查询 -->
	<page pageID="mrjj250List" path="mrjj250List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj250TList" path="mrjj250TList.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj252List" path="mrjj252List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj253List" path="mrjj253List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj254List" path="mrjj254List.jsp" uiCtrl="false">
		<action flag="W" method="welcome"/>
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint" />
		<action flag="H" method="doPrint" />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj255List" path="mrjj255List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj256List" path="mrjj256List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj257List" path="mrjj257List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj258List" path="mrjj258List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj259List" path="mrjj259List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>

	<!-- 料号库存档查�? -->
	<page pageID="mrjj260List" path="mrjj260List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>

	<!-- 料号品级库存档查�? -->
	<page pageID="mrjj261List" path="mrjj261List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"  />
		<converter>
		</converter>
	</page>

	<page pageID="mrjj263List" path="mrjj263List.jsp" uiCtrl="false">
		<action flag="W" method="welcome"/>
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint" />
		<action flag="H" method="doPrint" />
		<converter>
		</converter>
	</page>
	<page pageID="mrjj270List" path="mrjj270List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<!-- 进料日报�? -->
	<page pageID="mrjj275List" path="mrjj275List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 原料分厂领用物质报表 -->
	<page pageID="mrjj276List" path="mrjj276List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 大宗原燃料干基日报表 -->
	<page pageID="mrjj277List" path="mrjj277List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 废钢生铁日报�? -->
	<page pageID="mrjj278List" path="mrjj278List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 交易明细查询作业 -->
	<page pageID="mrjj280List" path="mrjj280List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<action flag="H" method="doPrint"/>
		<converter>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>


	<!-- 交易清单查询作业 -->
	<page pageID="mrjj281List" path="mrjj281List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<action flag="H" method="doPrint"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>

	<!-- 交易明细汇��查询作�? -->
	<page pageID="mrjj282List" path="mrjj282List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 交易明细汇��查询作�? -->
	<page pageID="mrjj283List" path="mrjj283List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<!-- 原料关帐棄栄 -->
	<page pageID="mrjj290List" path="mrjj290List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<!-- 计量过磅资料查询作业 -->
	<page pageID="mrjj293List" path="mrjj293List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 废钢入库报表-->
	<page pageID="mrjj300List" path="mrjj300List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 外购废钢月进料明�? -->
	<page pageID="mrjj301List" path="mrjj301List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjc301ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mf.dao.mfjctbV030VO"
						 type="sequence"/>
		</converter>
	</page>


	<!-- 自产废钢月进明细�? -->
	<page pageID="mrjj302List" path="mrjj302List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 废钢库存明细-汇整 -->
	<page pageID="mrjj303List" path="mrjj303List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<!--	生铁月进料明细作�? -->
	<page pageID="mrjj306List" path="mrjj306List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<!-- 回收废钢日报�? -->
	<page pageID="mrjj312List" path="mrjj312List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<!-- 回收废钢日报�? -->
	<page pageID="mrjj321List" path="mrjj321List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 新��互供批次确认作�? -->
	<page pageID="mrjj323Edit" path="mrjj323Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="CF"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<action flag="R2"  method="doUpdate2" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="sequence"/>
		</converter>
	</page>


	<!-- 废钢生产经营综合日报 -->
	<page pageID="mrjj330List" path="mrjj330List.jsp" uiCtrl="false">
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj331List" path="mrjj331List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<!-- 原燃料进货统计表 -->
	<page pageID="mrjj350List" path="mrjj350List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjc350ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="v"
						 class="com.icsc.mf.dao.mfjctbV030VO"
						 type="sequence"/>
		</converter>
	</page>


	<page pageID="mrjj351List" path="mrjj351List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
			<valueObject objectID="p" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!-- 原燃料调进表作业	-->
	<page pageID="mrjj352List" path="mrjj352List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<!-- 外购原燃料质量统计表 -->
	<page pageID="mrjj354List" path="mrjj354List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 外购原燃料质量明细表 -->
	<page pageID="mrjj355List" path="mrjj355List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 入库汇��表 -->
	<page pageID="mrjj360List" path="mrjj360List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj361List" path="mrjj361List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj362List" path="mrjj362List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj363List" path="mrjj363List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj365List" path="mrjj365List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="PA" method="doPrintA"/>
		<action flag="PB" method="doPrintB"/>
		<action flag="PC" method="doPrintC"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj366List" path="mrjj366List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj367List" path="mrjj367List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj368List" path="mrjj368List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj369List" path="mrjj369List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj370List" path="mrjj370List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj371List" path="mrjj371List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj372List" path="mrjj372List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj375List" path="mrjj375List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj380List" path="mrjj380List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj381List" path="mrjj381List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj410List" path="mrjj410List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj411List" path="mrjj411List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjj411ListCar" path="mrjj411ListCar.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="S"  method="doSubmit" />
		<action flag="W"  method="doWithdraw" />
		<action flag="P" method="doPrint"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mr.dao.mrjctb041VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjj501List" path="mrjj501List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 暂估明细表查询作�? -->
	<page pageID="mrjj550List" path="mrjj550List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 铁合金去向表 -->
	<page pageID="mrjj582List" path="mrjj582List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>


	<!-- 交易追踪查询报表 -->
	<page pageID="mrjj590List" path="mrjj590List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!-- 铁汽运追踪报�? -->
	<page pageID="mrjj591List" path="mrjj591List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<!-- 质量验收追踪报表 -->
	<page pageID="mrjj592List" path="mrjj592List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj600List" path="mrjj600List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<!--  MFV052 状��? X �? 抛VCAA�?-->
	<page pageID="mrjj830List" path="mrjj830List.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="CF" method="doConfirm" validate="doConfirm_validate"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbV052VO" type="sequence"/>
		</converter>
	</page>

	<page pageID="mrjjSG001List" path="mrjjSG001List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG002List" path="mrjjSG002List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG003List" path="mrjjSG003List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV018VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG004List" path="mrjjSG004List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbVI12VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG005List" path="mrjjSG005List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbVC32VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG006List" path="mrjjSG006List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbVC26VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG007List" path="mrjjSG007List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mr.dao.mrjctb030VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG008List" path="mrjjSG008List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG009List" path="mrjjSG009List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mr.dao.mrjctbL40VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG010List" path="mrjjSG010List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery" />
		<converter>
		</converter>
	</page>
	<page pageID="mrjjSG011List" path="mrjjSG011List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery" />
		<converter>
		</converter>
	</page>
	<page pageID="mrjjSG012List" path="mrjjSG012List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doReThrow"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbR020VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG013List" path="mrjjSG013List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbVC34VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG014List" path="/erp/mr/jsp/mrjjSG014List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG014ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG015List" path="/erp/mr/jsp/mrjjSG015List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG015ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG016List" path="/erp/mr/jsp/mrjjSG016List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG016ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG017List" path="/erp/mr/jsp/mrjjSG017List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG017ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG018List" path="/erp/mr/jsp/mrjjSG018List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG018ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<action flag="T" method="doConfirm" forward="mrjjSG018Search.jsp"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG020List" path="/erp/mr/jsp/mrjjSG020List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG020ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG021List" path="/erp/mr/jsp/mrjjSG021List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG021ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG022List" path="/erp/mr/jsp/mrjjSG022List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG022ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG023List" path="/erp/mr/jsp/mrjjSG023List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG024List" path="/erp/mr/jsp/mrjjSG024List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSG024ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<action flag="P" method="print"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG026List" path="/erp/mr/jsp/mrjjSG026List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG027List" path="/erp/mr/jsp/mrjjSG027List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG030List" path="/erp/mr/jsp/mrjjSG030List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG031List" path="/erp/mr/jsp/mrjjSG031List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSG055List" path="mrjjSG055List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbVC32VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG066List" path="mrjjSG066List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbVC26VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSG122List" path="mrjjSG122List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doReThrow" forward="mrjjSG122Search.jsp"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjjSGDI55List" path="mrjjSGDI55List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doReThrow" forward="mrjjSGDI55Search.jsp"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjjSGDI68List" path="mrjjSGDI68List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doReThrow" forward="mrjjSGDI68Search.jsp"/>
		<converter>
		</converter>
	</page>
	<page pageID="mrjjSGOIList" path="mrjjSGOIList.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSGOJList" path="mrjjSGOJList.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSGSSList" path="mrjjSGSSList.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSGJY10List" path="mrjjSGJY10List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doReThrow"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbR022VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSGJY13List" path="mrjjSGJY13List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="R" method="doReThrow"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbR023VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjj063BList" path="mrjj063BList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"/>
		<action flag="FI" method="doQuery"/>
		<action flag="PR" method="doQuery"/>
		<action flag="NE" method="doQuery"/>
		<action flag="LA" method="doQuery"/>
		<action flag="SK" method="doQuery"/>
		<action flag="N" method="doCreate"/>
		<action flag="R" method="doUpdate"/>
		<action flag="D" method="doDelete"/>
		<action flag="CL" method="doClose"/>
		<action flag="C" method="doOpen"/>
		<action flag="P" method="doPrint"  forward="mrjjRptDownLoad.jsp"/>
		<action flag="U" method="doUpLoad"/>
		<action flag="T" method="doConfirm"/>
		<converter>
			<valueObject objectID="v" class="com.icsc.mf.dao.mfjctbR030VO" type="sequence"/>
		</converter>
	</page>
	<page pageID="mrjj098List" path="mrjj098List.jsp" uiCtrl="false">
		<action flag="U" method="doDownload"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjj284List" path="mrjj284List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<action flag="P" method="doPrint"/>
		<converter>
		</converter>
	</page>

	<page pageID="mrjjSG067List" path="mrjjSG067List.jsp" uiCtrl="false">
		<action flag="I" method="doQuery"/>
		<converter>
			<valueObject objectID="p"
						 class="com.icsc.mf.dao.mfjctbV052VO"
						 type="unique"/>
		</converter>
	</page>
	<page pageID="mrjjSGL20List" path="/erp/mr/jsp/mrjjSGL20List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSGL20ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>
	<page pageID="mrjjSGL21List" path="/erp/mr/jsp/mrjjSGL21List.jsp" uiCtrl="false">
		<controller>com.icsc.mr.func.mrjcSGL21ListFunc</controller>
		<action flag="I" method="doQuery"/>
		<converter></converter>
	</page>

	<!--互供调整作业  -->
	<page pageID="mrjj147Edit" path="mrjj147Edit.jsp" uiCtrl="true">
		<action flag="I"  method="doQuery" />
		<action flag="N"  method="doCreate" validate="doCreate_validate"/>
		<action flag="R"  method="doUpdate" validate="doUpdate_validate"/>
		<action flag="D"  method="doDelete" />
		<action flag="T"  method="doConfirm" validate="doConfirm_validate"/>
		<action flag="C" method="doCancel" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.mrjctb030VO" type="unique"/>
		</converter>
	</page>

	<!--lyg测试  -->
	<page pageID="mrjjlygstuList" path="mrjjlygstuList.jsp" uiCtrl="true">
		<action flag="I" method="doQuery"  />
		<action flag="I1" method="doQuery1"  />
		<action flag="N" method="doCreate" validate="doCreate_validate" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" />
		<action flag="D" method="doDelete" validate="doDelete_validate" />
		<action flag="T" method="doConfirm" validate="doConfirm_validate" />
		<action flag="C" method="doCancel" validate="doCancel_validate" />
		<action flag="P" method="doPrint" />
		<converter>
			<valueObject objectID="v1"
						 class="com.icsc.mr.dao.LygStudentVO"
						 type="unique"/>
		</converter>
	</page>

	<!-- 学生信息列表页面 -->
	<page pageID="mrjjlygstuList" path="mrjjlygstuList.jsp" uiCtrl="true">
		<controller>com.icsc.mr.func.mrjclygstuListFunc</controller>
		<action flag="I" method="doQuery" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="studentList" class="com.icsc.mr.dao.LygStudentVO" type="sequence"/>
		</converter>
	</page>

	<!-- 学生信息编辑页面 -->
	<page pageID="mrjjlygstuEdit" path="mrjjlygstuEdit.jsp" uiCtrl="true">
		<controller>com.icsc.mr.func.mrjclygstuEditFunc</controller>
		<action flag="I" method="doQuery" />
		<action flag="N" method="doCreate" />
		<action flag="R" method="doUpdate" />
		<action flag="D" method="doDelete" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.mr.dao.LygStudentVO" type="unique"/>
		</converter>
	</page>


</pages>
