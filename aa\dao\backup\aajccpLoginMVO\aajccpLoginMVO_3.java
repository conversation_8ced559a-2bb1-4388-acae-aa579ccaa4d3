/*----------------------------------------------------------------------------*/
/* aajccpLoginMVO		DAOTool Ver 10.0112 (INPUT FILE VERSION:2.0)
/*----------------------------------------------------------------------------*/
/* author : InfoChamp
/* system : 普通會計管理系統(AA)
/* target : 用戶授權帳套別及廠別維護作業基本資料文件
/* create : 104/08/25
/* update : 104/08/25
/*----------------------------------------------------------------------------*/
package com.icsc.aa.dao ;

import java.io.Serializable ;
import java.math.*;
import java.sql.*;
import java.text.DecimalFormat ;
import java.util.*;
import java.util.regex.*;
import javax.servlet.http.HttpServletRequest ;

import com.icsc.dpms.de.*;


/**
 * 用戶授權帳套別及廠別維護作業基本資料文件 Value Object.
 * <pre>
 * Table Name        : DB.TBAACPLOGINM
 * Table Description : 用戶授權帳套別及廠別維護作業基本資料文件
 * Data Access Object: aajccpLoginMDAO
 * </pre>
 * @version 普通會計管理系統(AA)
 * @since aajccpLoginMVO - 104/08/25
 * <AUTHOR>
 */
public class aajccpLoginMVO extends dejcCommonVO implements Serializable {
	public final static String AppId = "AAJCCPLOGINMVO" ;
	public final static String CLASS_VERSION = "$Revision: 1.1 $ $Date: 2015/09/30 05:13:36 $";
	public final static String ATTRIB_DESC="userId=使用者代號,lastCompId=上次使用公司帳套別," ; 

	/**
	 * 使用者代號 輸入欄位長度
	 * @since 104/08/25
	 */
	public final static int USERID_LEN = 10 ;
	/**
	 * 上次使用公司帳套別 輸入欄位長度
	 * @since 104/08/25
	 */
	public final static int LASTCOMPID_LEN = 10 ;
	


/*----------------------------------------------------------------------------*/
/* DB.TBAACPLOGINM column Name
/*----------------------------------------------------------------------------*/

	/**
	 * 使用者代號
	 * @since 104/08/25
	 */
	private String userId;
	/**
	 * 上次使用公司帳套別
	 * @since 104/08/25
	 */
	private String lastCompId;
	/**
	 * 執行訊息
	 * @since 104/08/25
	 */
	private String message ;

/*----------------------------------------------------------------------------*/
/* Creates new aajccpLoginMVO
/*----------------------------------------------------------------------------*/

	/**
	 * 建構子
	 * @since 104/08/25
	 */
	public aajccpLoginMVO() {
		clear() ;
		super.setChild(this) ;
	}
	/**
	 * 主要鍵建構子
	 * @since 104/08/25
	 */
	public aajccpLoginMVO(String userId) {
		this() ;
		this.userId = userId ;	// 使用者代號
	}
	
	/**
	 * 將所有的欄位 reset 成預設值.
	 * <pre>
	 * 當物件資料不需再被使用時(例如物件被 delete 後仍需放一個空物件到畫面上)
	 * 可以使用此方法，好處是可以重覆使用此物件，不需再 new 一個新的
	 *</pre>
	 */
	public void clear() {
		this.userId = "" ;	// 使用者代號
		this.lastCompId = "" ;	// 上次使用公司帳套別
		this.message = "";
	}
	/**
	 * 將自己複制一份出來
	 */
	public aajccpLoginMVO myClone() {
		aajccpLoginMVO aacpLoginMVO = new aajccpLoginMVO() ;
		aacpLoginMVO.setUserId(this.userId);
		aacpLoginMVO.setLastCompId(this.lastCompId);
		return aacpLoginMVO ;
	}

/**
  * toJSON 方法
  * <p>
  * @return JSON String
  * @since 104/08/25
  */
 public String toJSON() {
  StringBuffer sb = new StringBuffer() ;
  sb.append("userId :\""+dejcUtility.parseToJSON(getUserId()+"")+"\"") ;
  sb.append(",");
  sb.append("lastCompId :\""+dejcUtility.parseToJSON(getLastCompId()+"")+"\"") ;
  return "{"+sb.toString()+"}";
 }



/*----------------------------------------------------------------------------*/
/* function methods
/*----------------------------------------------------------------------------*/

	/**
	 * 以 ResultSet 來作物件
	 * <p>
	 * @param rs - 單一 ResultSet
	 * @return aajccpLoginMVO - 產生 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/08/25
	 */
	public static aajccpLoginMVO getInstance(ResultSet rs) throws SQLException,Exception {
		aajccpLoginMVO aacpLoginMVO = new aajccpLoginMVO() ;

  		aacpLoginMVO.setUserId(rs.getString(1));
  		aacpLoginMVO.setLastCompId(rs.getString(2));
  		return aacpLoginMVO ;
	}

	/**
	 * 以 ResultSet 來作物件
	 * <p>
	 * @param rs - 單一 ResultSet
	 * @return aajccpLoginMVO - 產生 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/08/25
	 */
	public static aajccpLoginMVO getInstanceByName(ResultSet rs) throws SQLException,Exception {
		return getInstanceByName(new aajccpLoginMVO(), rs) ;
	}

	/**
	 * 以 ResultSet 來作物件, 使用由 user 傳進來的物件
	 * <p>
	 * @param rs - 單一 ResultSet
	 * @return aajccpLoginMVO - 產生 Value Object
	 * @exception SQLException - DB SQLException
	 * @since 104/08/25
	 */
	public static aajccpLoginMVO getInstanceByName(Object obj, ResultSet rs) throws SQLException {
		aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO)obj ;
  		aacpLoginMVO.setUserId(rs.getString("userId"));
  		aacpLoginMVO.setLastCompId(rs.getString("lastCompId"));
  		return aacpLoginMVO ;
	}

	/**
	 * 以 Request 來作物件
	 * <p>
	 * @param request - 網頁要求物件
	 * @return aajccpLoginMVO - 產生 Value Object
	 * @since 104/08/25
	 */
	public static aajccpLoginMVO getInstance(HttpServletRequest request) {
		return getFromReq(request, -1) ;
	}
	/**
	 * 以 Request 來作物件 (遇到多筆資料時)
	 * <p>
	 * @param request - 網頁要求物件
	 * @param index - 參數流水序號
	 * @return aajccpLoginMVO - 產生 Value Object
	 * @since 104/08/25
	 */
	public static aajccpLoginMVO getInstance(HttpServletRequest request, int index) {
		return getFromReq(request, index) ;
	}
	/**
	 * 以 Request 來作物件
	 * <p>
	 * @param req - 網頁要求物件
	 * @param index - 參數流水序號
	 * @return aajccpLoginMVO - 產生 Value Object
	 * @since 104/08/25
	 */
	private static aajccpLoginMVO getFromReq(HttpServletRequest req, int index) {
		String seq = (index >= 0) ? String.valueOf(index) : "" ;
		aajccpLoginMVO aacpLoginMVO = new aajccpLoginMVO() ;

		aacpLoginMVO.setUserId(req.getParameter("userId"+seq)) ;
		aacpLoginMVO.setLastCompId(req.getParameter("lastCompId"+seq)) ;

		return aacpLoginMVO ;
	}

/*----------------------------------------------------------------------------*/
/* public methods
/*----------------------------------------------------------------------------*/

	/**
	 * 覆寫 toString , 以利 Debug
	 * <p>
	 * @return 物件內容值
	 * @since 104/08/25
	 */
	public String toString() {
		StringBuffer sb = new StringBuffer() ;

		sb.append("<aajccpLoginMVO>");
		sb.append(super.toString());
		sb.append("\r\n\tuserId = " + getUserId()) ;
		sb.append("\r\n\tlastCompId = " + getLastCompId()) ;
		sb.append("\r\n</aajccpLoginMVO>");

		return sb.toString();
	}
	/**
	 * 比較兩個物件的 Key 屬性
	 * <pre>
	 * 只比較 Key 部分，有一項 Key 不同，則 return false
	 * 並非全部的屬性都比較
	 * </pre>
	 * @param object - 欲比較之物件
	 * @return true - the same; false - diff
	 * @since 104/08/25
	 */
	public boolean keyEquals(Object object) {
		if (object instanceof aajccpLoginMVO == false) {
			return false ;
		}

		aajccpLoginMVO aacpLoginMVO = (aajccpLoginMVO) object ;
		if (this.userId.equals(aacpLoginMVO.getUserId()) == false) {
			return false ;
		}
		return true ;
	}
	/**
	 * 檢查 key 值是否不等於 null 或 空字串
	 * @return true - ok; false - hasErr
	 * @since 104/08/25
	 */
	public boolean isKeyOk() {
		if (dejcUtility.isNull(this.userId)) {
			return hasErr("userId") ;
		}
		return true ;
	}
	/**
	 * 檢查資料的正確性 (包括長度，型態)
	 * @return true - ok; false - hasErr
	 * @since 104/08/25
	 */
	public boolean verify() {
		if (userId!=null) { 
			if (userId.getBytes().length>USERID_LEN) {
				return hasErr("使用者代號的資料長度["+userId.getBytes().length+"]不得超過["+USERID_LEN+"]") ; 
			}
		}
		if (lastCompId!=null) { 
			if (lastCompId.getBytes().length>LASTCOMPID_LEN) {
				return hasErr("上次使用公司帳套別的資料長度["+lastCompId.getBytes().length+"]不得超過["+LASTCOMPID_LEN+"]") ; 
			}
		}
		return true ;
	}	

/*----------------------------------------------------------------------------*/
/* private methods
/*----------------------------------------------------------------------------*/

	/**
	 * 設定執行訊息
	 * @param msg - 訊息
	 * @return false - always false
	 */
	private boolean hasErr(String msg) {
		this.message = msg;
		return false ;
	}

	/**
	 * 以欄位名稱取出資料(像 Map 一樣，
	 * @throws RuntimeException thrown when the field not exists
	 */
	public Object get(String field) {
	    return super.get(field) ;
	}


/*----------------------------------------------------------------------------*/
/* get and set methods for the instance variables
/*----------------------------------------------------------------------------*/

	/**
	 * 回傳執行訊息
	 * @return message - 取得執行訊息
	 * @since 104/08/25
     */
	public String getMessage() {
		return this.message ;
	}
	/**
	  *側錄flag，為true時才開始側錄
	  */
	private boolean monitoring ;
	/**
	 * 編輯欄位名稱
	 */
	private Map editFields;
	/**
	 * 欄位舊資料名稱
	 */
	private Map oldFieldValues;

	/**
	  * 取得修改欄位資
	  * @return null if no editFields
	  */
	public Map getEditFields() {
		return this.editFields;
	}
	/**
	  *開始側錄 set進來的 fieldName,fieldValue
	  */
	public void monitor(){
		this.monitoring=true;
		this.editFields=new HashMap();
		this.oldFieldValues=new HashMap();
	}
	/**
	  *取消側錄
	  */
	public void stopMonitor(){
		this.monitoring=false;
	}
	/**
	  * 每個 set method 被呼叫時都會呼叫此方法
	  */
	public void onSetField(String field,Object value){
		if(this.monitoring){
			oldFieldValues.put(field, get(field));
			editFields.put(field,value);
		}
	}
	
	/** 
	 * 設定使用者代號
	 */ 
	public void setUserId(String userId)  { 
		onSetField("userId",userId); 
		this.userId = (userId==null)?"":userId; 
	}

	/** 
	 * 取得使用者代號
	 */ 
	public String getUserId()  { 
		return this.userId ; 
	}
	/** 
	 * 取得使用者代號 ( 處理單引號的問題 )
	 */ 
	public String getUserIdS()  { 
		return dejcUtility.replaceS(this.userId) ; 
	}

	/** 
	 * 設定上次使用公司帳套別
	 */ 
	public void setLastCompId(String lastCompId)  { 
		onSetField("lastCompId",lastCompId); 
		this.lastCompId = (lastCompId==null)?"":lastCompId; 
	}

	/** 
	 * 取得上次使用公司帳套別
	 */ 
	public String getLastCompId()  { 
		return this.lastCompId ; 
	}
	/** 
	 * 取得上次使用公司帳套別 ( 處理單引號的問題 )
	 */ 
	public String getLastCompIdS()  { 
		return dejcUtility.replaceS(this.lastCompId) ; 
	}

	/**
	  *以欄位英文名稱取出中文名稱
	  */	
	public String getFieldDesc(String fieldName){
		Matcher matcher = Pattern.compile(fieldName+"=[^,]+").matcher(ATTRIB_DESC);
		if(matcher.find()){
			return matcher.group().replaceAll(fieldName+"=", "");
		}
		return null;
	}
	public String[] getAllFieldNames(){
		return ATTRIB_DESC.split("=[^,]+,");
	}
	/**
	 *是否有編輯過的欄位
	 */
	public boolean hasEditFields() {
		return getEditFields()!=null && !getEditFields().isEmpty();
	}
  	/**
	 * 列出所有被修改過的欄位名稱
	 * @return
	 */
	public String[] listEditFieldNames() {
		if(!hasEditFields()) {
			return new String[0];
		}
		Set keySet = getEditFields().keySet();
		String[] fieldNames=new String[keySet.size()];
		keySet.toArray(fieldNames);
		return fieldNames;
	}
	/**
	 * 取出修改前的資料
	 * @param field
	 * @return
	 */
	public Object getOldFieldValue(String field) {
		if(oldFieldValues==null) {
			return null ;
		}
		return oldFieldValues.get(field);
	}
	/**
	 * 取出所有 KEY 欄位
	 * @param field
	 * @return
	 */	
	public String[] getKeys() {
	    String key = "userId," ;
	    if(!key.equals("")){
	    	return key.substring(0,key.length()-1).split(",");
	    }else{
	    	return new String[0];
	    }
    	}
 

/*----------------------------------------------------------------------------*/
/* Customize methods
/*----------------------------------------------------------------------------*/

//==^_^== ======= Don't Extend Your Code above , or All changes will lose after next Generating Code ===========  ==^_^== //

	/** 
	 * validate every attribute (ex. empty, date format...)
	 * method need to be implemented..
	 */ 
	public dejcVoValidater validate() { 
		dejcVoValidater v = null ; 
		 // v = new dejcVoValidater(this); 
		 // validating logic here...
		return v ; 
	}
	private String cname;
	public String getCname() {
		return cname;
	}
	public void setCname(String cname) {
		this.cname = cname;
	}


} // end of Class aajccpLoginMDAO