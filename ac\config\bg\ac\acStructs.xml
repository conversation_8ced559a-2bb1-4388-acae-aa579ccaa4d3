<?xml version="1.0" encoding="BIG5"?>

<pages>
	<page pageID="acjj0201AList" path="acjj0201AList.jsp">
		<controller>com.icsc.ac.func.acjc0201AFunc</controller>
			<action flag="I" method="query" forward="acjj0201AList.jsp" />
			<action flag="D" method="doDelete" forward="acjj0201AList.jsp" />
		<converter>
		</converter>
	</page>
	<page pageID="acjjCCDetailCost" path="acjjCCDetailCost.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjCCDetailCost.jsp" />
		<converter></converter>
	</page>
	<page pageID="acjjImport" path="acjjImport.jsp">
		<controller>com.icsc.ac.acjcImportFunc</controller>
		<action flag="E" method="doExport" forward="acjjImport.jsp" />
		<action flag="I" method="doImport" forward="acjjImport.jsp" />
		<converter>

		</converter>
	</page>



	<page pageID="acjj0101CostCenter" path="acjj0101CostCenter.jsp">

		<controller>com.icsc.ac.func.acjc0101Func</controller>

		<action flag="I" method="doInquire"
			forward="acjj0101CostCenter.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0101CostCenter.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0101CostCenter.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0101CostCenter.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcb1VO"
				type="unique" />

		</converter>

	</page>

	<page pageID="acjj0101Dept" path="acjj0101Dept.jsp">

		<controller>com.icsc.ac.func.acjc0101Func</controller>

		<action flag="QD" method="queryDept" forward="acjj0101Dept.jsp" />

		<converter>

			<valueObject objectID="v2" class="" type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0101Activity" path="acjj0101Activity.jsp">

		<controller>com.icsc.ac.func.acjc0101Func</controller>

		<action flag="QA" method="queryActi"
			forward="acjj0101Activity.jsp" />

		<converter>

			<valueObject objectID="v3" class="com.icsc.am.dao.amjcbaVO"
				type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0101Attri" path="acjj0101Attri.jsp">

		<controller>com.icsc.ac.func.acjc0101AttriFunc</controller>

		<action flag="I" method="doQuery" forward="acjj0101Attri.jsp" />

		<action flag="R" method="doUpdate" forward="acjj0101Attri.jsp" />

		<action flag="D" method="doDelete" forward="acjj0101Attri.jsp" />

		<converter>

			<valueObject objectID="v4"
				class="com.icsc.ac.dao.acjcccAttriVO" type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0102WCEMenu" path="acjj0102WCEMenu.jsp">

		<controller>com.icsc.ac.func.acjc01022Func</controller>

		<action flag="I" method="doQueryLayer"
			forward="acjj0102WCEMenu.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0102WCEMenu.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0102WCEMenu.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0102WCEMenu.jsp" />

		<converter>

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjcb3VO"
				type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0102WCEList" path="acjj0102WCEList.jsp">

		<controller>com.icsc.ac.func.acjc01023Func</controller>

		<action flag="I" method="doInquire"
			forward="acjj0102WCEList.jsp" />

		<converter>

			<valueObject objectID="v3" class="com.icsc.ac.dao.acjcb2VO"
				type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0102WCEInput" path="acjj0102WCEInput.jsp">

		<controller>com.icsc.ac.func.acjc0102Func</controller>

		<action flag="I" method="doInquire"
			forward="acjj0102WCEInput.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0102WCEInput.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0102WCEInput.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0102WCEInput.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcb2VO"
				type="unique" />

		</converter>

	</page>

	<page pageID="acjj0102WCEAttri" path="acjj0102WCEAttri.jsp">

		<controller>com.icsc.ac.func.acjc01024Func</controller>

		<action flag="I" method="doQuery"
			forward="acjj0102WCEAttri.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0102WCEAttri.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0102WCEAttri.jsp" />

		<converter>

			<valueObject objectID="v2"
				class="com.icsc.ac.dao.acjcwceAttriVO" type="sequence" />



		</converter>

	</page>



	<page pageID="acjj0113A" path="acjj0113A.jsp">

		<controller>com.icsc.ac.func.acjc0113Func</controller>

		<action flag="I" method="doInquire" forward="acjj0113A.jsp" />

		<action flag="N" method="doInsert" validate="do_Validate"
			forward="acjj0113A.jsp" />

		<action flag="R" method="doUpdate" validate="do_Validate"
			forward="acjj0113A.jsp" />

		<action flag="D" method="doDelete" forward="acjj0113A.jsp" />

		<converter>

			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcshareBaseVO" type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0201A" path="acjj0201A.jsp">

		<controller>com.icsc.ac.func.acjc0201Func</controller>

		<action flag="I" method="doInquire"
			validate="doInquire_validate" forward="acjj0201A.jsp" />
		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0201A.jsp" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0201A.jsp" />
		<action flag="C" method="confirm" validate="doUpdate_validate"
			forward="acjj0201A.jsp" />
		<action flag="D" method="doScrap" forward="acjj0201A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjci1VO"
				type="unique" />

		</converter>

	</page>



	<page pageID="acjj0202A" path="acjj0202A.jsp">

		<controller>com.icsc.ac.func.acjc0202Func</controller>

		<action flag="I" method="doInquire" forward="acjj0202A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="unique" />

		</converter>

	</page>

	<page pageID="acjj0202AList" path="acjj0202AList.jsp">
		<controller>com.icsc.ac.func.acjc0202Func</controller>
		<action flag="B" method="doBack" forward="acjj0202AList.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="sequence" />
		</converter>
	</page>



	<page pageID="acjj0301A" path="acjj0301A.jsp">

		<controller>com.icsc.ac.func.acjc0301Func</controller>

		<action flag="I" method="doInquire" forward="acjj0301A.jsp" />

		<action flag="N" method="doInsert" validate="insert_validate"
			forward="acjj0301A.jsp" />

		<action flag="D" method="doDelete" validate="delete_validate"
			forward="acjj0301A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcbcVO"
				type="sequence" />

		</converter>

	</page>







	<page pageID="acjj0304A" path="acjj0304A.jsp">

		<controller>com.icsc.ac.func.acjc0304Func</controller>

		<action flag="I" method="doInquire"
			validate="doInquire_validate" forward="acjj0304A.jsp" />

		<action flag="N" method="doInsert" validate="oprate_validate"
			forward="acjj0304A.jsp" />

		<action flag="R" method="doUpdate" validate="oprate_validate"
			forward="acjj0304A.jsp" />

		<action flag="D" method="doDelete" validate="oprate_validate"
			forward="acjj0304A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct2VO"
				type="sequence" />

		</converter>

	</page>


	<page pageID="acjj0403A" path="acjj0403A.jsp">

		<controller>com.icsc.ac.func.acjc0403Func</controller>

		<action flag="I" method="doInquire" forward="acjj0403A.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0403A.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0403A.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0403A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct2VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0501A" path="acjj0501A.jsp">

		<controller>com.icsc.ac.func.acjc0501Func</controller>

		<action flag="I" method="doInquire" forward="acjj0501A.jsp" />

		<action flag="N" method="doInsert" validate="action_validate"
			forward="acjj0501A.jsp" />

		<action flag="R" method="doUpdate" validate="action_validate"
			forward="acjj0501A.jsp" />

		<action flag="D" method="doDelete" forward="acjj0501A.jsp" />

		<action flag="Prev" method="doQueryPrev"
			forward="acjj0501A.jsp" />

		<action flag="Next" method="doQueryNext"
			forward="acjj0501A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjctfVO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0701Print" path="acjj0701Print.jsp">

		<controller>com.icsc.ac.func.acjc0701Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0701Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcm3VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0702Print" path="acjj0702Print.jsp">

		<controller>com.icsc.ac.func.acjc0702Func</controller>

		<action flag="P" method="doPrintInquire"
			forward="acjj0702Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0704Print" path="acjj0704Print.jsp">

		<controller>com.icsc.ac.func.acjc0704Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0704Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcm1VO"
				type="sequence" />

		</converter>

	</page>


	<page pageID="acjj0706Print" path="acjj0706Print.jsp">

		<controller>com.icsc.ac.func.acjc0706Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0706Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="" type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0707" path="acjj0707Print.jsp">

		<controller>com.icsc.ac.func.acjc0707Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0707Print.jsp" />

		<converter>

		</converter>

	</page>



	<page pageID="acjj0709Print_csac" path="acjj0709Print_csac.jsp"
		uiCtrl="false">

		<controller>com.icsc.ac.func.acjc0709Func_csac</controller>

		<action flag="M" method="doPrintInquire" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcb1VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0710Print_csac" path="acjj0710Print_csac.jsp">

		<controller>com.icsc.ac.func.acjc0710Func_csac</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0710Print_csac.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcm1VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0711Print" path="acjj0711Print.jsp">

		<controller>com.icsc.ac.func.acjc0711Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0711Print.jsp" />

		<action flag="PF" method="doPrintFactory"
			forward="acjj0711Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcm1VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj071101Print" path="acjj071101Print.jsp">

		<controller>com.icsc.ac.func.acjc071101Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj071101Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcm1VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0712Print" path="acjj0712Print.jsp">

		<controller>com.icsc.ac.func.acjc0712Func</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0712Print.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcm2VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0719Print_csac" path="acjj0719Print_csac.jsp">

		<controller>com.icsc.ac.func.acjc0719Func_csac</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj0719Print_csac.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="sequence" />

		</converter>

		<converter>

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjcm1VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj071901Print_csac"
		path="acjj071901Print_csac.jsp">

		<controller>com.icsc.ac.func.acjc071901Func_csac</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj071901Print_csac.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="unique" />

		</converter>

	</page>

	<page pageID="acjj071902Print_csac"
		path="acjj071902Print_csac.jsp">

		<controller>com.icsc.ac.func.acjc071902Func_csac</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj071902Print_csac.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="unique" />

		</converter>

	</page>

	<page pageID="acjj071903Print_csac"
		path="acjj071903Print_csac.jsp">

		<controller>com.icsc.ac.func.acjc071903Func_csac</controller>

		<action flag="I" method="doPrintInquire"
			forward="acjj071903Print_csac.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="unique" />

		</converter>

	</page>

	<page pageID="acjj0801Main" path="acjj0801Main.jsp">

		<controller>com.icsc.ac.func.acjc0801Func</controller>

		<action flag="I" method="doInquire" validate="inquire_validate"
			forward="acjj0801Main.jsp" />

		<action flag="U" method="run" validate="run_validate"
			forward="acjj0801Main.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcb4VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0802A" path="acjj0802A.jsp">

		<controller>com.icsc.ac.func.acjc0802Func</controller>

		<action flag="I" method="doInquire" forward="acjj0802A.jsp" />

		<action flag="N" method="doInsert" forward="acjj0802A.jsp" />

		<action flag="R" method="doUpdate" forward="acjj0802A.jsp" />

		<action flag="D" method="doDelete" forward="acjj0802A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcb4VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0901" path="acjj0901.jsp">

		<controller>com.icsc.ac.func.acjc0901Func</controller>

		<action flag="I" method="doInquire" forward="acjj0901.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjc75VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0902A" path="acjj0902A.jsp">

		<controller>com.icsc.ac.func.acjc0902Func</controller>

		<action flag="I" method="doInquire" forward="acjj0902A.jsp" />

		<action flag="N" method="doInsert" validate="action_validate"
			forward="acjj0902A.jsp" />

		<action flag="R" method="doUpdate" validate="action_validate"
			forward="acjj0902A.jsp" />

		<action flag="D" method="doDelete" forward="acjj0902A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjc72VO"
				type="unique" />

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjc71VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0903A" path="acjj0903A.jsp">

		<controller>com.icsc.ac.func.acjc0903AFunc</controller>

		<action flag="I" method="doInquire" forward="acjj0903A.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0903A.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0903A.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0903A.jsp" />

		<action flag="Prev" method="doInquirePrev"
			forward="acjj0903A.jsp" />

		<action flag="Next" method="doInquireNext"
			forward="acjj0903A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjc73VO"
				type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0903B" path="acjj0903B.jsp">

		<controller>com.icsc.ac.func.acjc0903BFunc</controller>

		<action flag="I" method="doInquire" forward="acjj0903B.jsp" />

		<converter>

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjc74VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj0904Base" path="acjj0904Base.jsp">

		<controller>com.icsc.ac.func.acjc0904BaseFunc</controller>

		<action flag="I" method="doInquire" forward="acjj0904Base.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0904Base.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0904Base.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0904Base.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjc75VO"
				type="unique" />

		</converter>

	</page>



	<page pageID="acjj0904Row" path="acjj0904Row.jsp">

		<controller>com.icsc.ac.func.acjc0904RowFunc</controller>

		<action flag="I" method="doInquire" forward="acjj0904Row.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj0904Row.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj0904Row.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj0904Row.jsp" />

		<converter>

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjc76VO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj1702Menu" path="acjj1702Menu.jsp">

		<controller>com.icsc.ac.func.acjc1702Func</controller>

		<action flag="I" method="doInquire" forward="acjj1702Menu.jsp" />

		<converter>

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjctcVO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj1702Input" path="acjj1702Input.jsp">

		<controller>com.icsc.ac.func.acjc1702Func</controller>

		<action flag="II" method="doInputInquire"
			forward="acjj1702Input.jsp" />

		<action flag="R" method="doInputUpdate"
			validate="doUpdate_validate" forward="acjj1702Input.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjctdVO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj1702Print" path="acjj1702Print.jsp">

		<controller>com.icsc.ac.func.acjc1702Func</controller>

		<action flag="P" method="doPrintInquire"
			forward="acjj1702Print.jsp" />

		<converter>

			<valueObject objectID="v2" class="com.icsc.ac.dao.acjctdVO"
				type="sequence" />

		</converter>

	</page>



	<page pageID="acjj2001A" path="acjj2001A.jsp">

		<controller>com.icsc.ac.func.acjc2001Func</controller>

		<action flag="I" method="doInquire" forward="acjj2001A.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj2001A.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj2001A.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj2001A.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcb5VO"
				type="sequence" />

		</converter>

	</page>

	<page pageID="acjj5101" path="acjj5101CostTD.jsp">

		<controller>com.icsc.ac.func.acjc5101Func</controller>

		<action flag="I" method="doInquire"
			forward="acjj5101CostTD.jsp" />

		<action flag="N" method="doInsert" validate="doInsert_validate"
			forward="acjj5101CostTD.jsp" />

		<action flag="R" method="doUpdate" validate="doUpdate_validate"
			forward="acjj5101CostTD.jsp" />

		<action flag="D" method="doDelete" validate="doDelete_validate"
			forward="acjj5101CostTD.jsp" />

		<action flag="GD" method="getDateMenu" forward="acjj5101.jsp" />
		
		<converter>

			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcbudgetCostTDVO" type="sequence" />

		</converter>

	</page>
	
	<page pageID="acjj5101Upload" path="acjj5101Upload.jsp">

		<controller>com.icsc.ac.func.acjc5101Func</controller>

		<action flag="GD" method="getDateMenu" forward="acjj5101Upload.jsp" />
		
		<action flag="U" method="doUpload" validate="doUpload_validate"
			forward="acjj5101Upload.jsp" />

		<converter>
		
			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcbudgetCostTDVO" type="sequence" />

		</converter>

	</page>

	<page pageID="acjjruletable" path="acjjruletableM.jsp">

		<controller>com.icsc.ac.func.acjcruleFunc</controller>

		<action flag="I" method="queryShareBase"
			forward="acjjruletableL.jsp" />

		<action flag="Is" method="doInquire"
			forward="acjjruletableM.jsp" />

		<action flag="N" method="doInsert" forward="acjjruletableM.jsp" />

		<action flag="R" method="doUpdate" forward="acjjruletableM.jsp" />

		<action flag="D" method="doDelete" forward="acjjruletableM.jsp" />

		<converter>

			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcRuleTableVO" type="sequence" />

			<valueObject objectID="v2"
				class="com.icsc.ac.dao.acjcshareBaseVO" type="sequence" />

		</converter>

	</page>

	<page pageID="acjjruletableL" path="acjjruletableL.jsp">

		<controller>com.icsc.ac.func.acjcruleFunc</controller>

		<action flag="Is" method="doInquire"
			forward="acjjruletableM.jsp" />

		<converter>

			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcRuleTableVO" type="sequence" />

			<valueObject objectID="v2"
				class="com.icsc.ac.dao.acjcshareBaseVO" type="sequence" />

		</converter>

	</page>

	<page pageID="acjjCostItem" path="acjjCostItemM.jsp">

		<controller>com.icsc.ac.func.acjcCostItemFunc</controller>

		<action flag="I" method="query" forward="acjjCostItemM.jsp" />

		<action flag="N" method="create" forward="acjjCostItemM.jsp" />

		<action flag="D" method="delete" forward="acjjCostItemM.jsp" />

		<converter>

			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcFacCostItemVO" type="sequence" />

		</converter>

	</page>

	<page pageID="acjjCostItemSetting"
		path="acjjCostItemSettingM.jsp">

		<controller>
			com.icsc.ac.func.acjcCostItemSettingFunc
		</controller>

		<action flag="I" method="query"
			forward="acjjCostItemSettingM.jsp" />

		<action flag="N" method="create"
			forward="acjjCostItemSettingM.jsp" />

		<action flag="R" method="update"
			forward="acjjCostItemSettingM.jsp" />

		<action flag="D" method="delete"
			forward="acjjCostItemSettingM.jsp" />

		<converter>

			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcFacCostItemSettingVO" type="sequence" />

		</converter>

	</page>

	<page pageID="acjj0202" path="acjjAmVchrNoDetail.jsp">

		<controller>com.icsc.ac.func.acjc0202Func</controller>

		<action flag="P" method="print"
			forward="acjjAmVchrNoDetail.jsp" />

		<converter>

			<valueObject objectID="v1" class="com.icsc.ac.dao.acjct1VO"
				type="unique" />

		</converter>

	</page>



	<page pageID="acjjQuerySource" path="acjjQuerySourceM.jsp">

		<controller>com.icsc.ac.func.acjcQuerySource</controller>

		<action flag="I" method="query" forward="acjjQuerySourceM.jsp" />

		<action flag="I2" method="query2"
			forward="acjjQuerySourceM2.jsp" />

		<action flag="I3" method="query3"
			forward="acjjQuerySourceM3.jsp" />



		<converter>

		</converter>

	</page>

	<page pageID="acjjRptPrint" path="acjjRptPrint.jsp">

		<controller>com.icsc.ac.func.acjcRptPrintFunc</controller>

		<action flag="P" method="doPrint" forward="acjjRptPrint.jsp" />

		<converter>

		</converter>

	</page>

	<page pageID="acjjMaterialBalances" path="acjjMaterialBalances.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun"
			forward="acjjMaterialBalances.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="acjjBatMakeCostRpt" path="acjjBatMakeCostRpt.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun"
			forward="acjjBatMakeCostRpt.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.facc.zaf.dao.zafcrdVO" type="unique" />
		</converter>
	</page>

	<page pageID="acjjwceg01" path="acjjwceg01.jsp">
		<controller>com.icsc.ac.func.acjcwceg01Func</controller>
		<action flag="I" method="query" forward="acjjwceg01.jsp" />
		<action flag="N" method="create" forward="acjjwceg01.jsp" />
		<action flag="R" method="update" forward="acjjwceg01.jsp" />
		<action flag="D" method="delete" forward="acjjwceg01.jsp" />
		<action flag="C" method="copy" forward="acjjwceg01.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcwceg01VO" type="sequence" />
		</converter>
	</page>
	<page pageID="acjjwceg02" path="acjjwceg02.jsp">
		<controller>com.icsc.ac.func.acjcwceg02Func</controller>
		<action flag="I" method="query" forward="acjjwceg02.jsp" />
		<action flag="N" method="create" forward="acjjwceg02.jsp" />
		<action flag="D" method="delete" forward="acjjwceg02.jsp" />
		<converter>

			<valueObject objectID="v2"
				class="com.icsc.ac.dao.acjcwceg02VO" type="sequence" />
		</converter>
	</page>
	<page pageID="acjjdpdwceDiff" path="acjjdpdwceDiff.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjdpdwceDiff.jsp" />
		<converter></converter>
	</page>
	<page pageID="acjjdpdwcePlan" path="acjjdpdwcePlan.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjdpdwcePlan.jsp" />
		<converter></converter>
	</page>
	<page pageID="acjjdpdwceRpt1" path="acjjdpdwceRpt1.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjdpdwceRpt1.jsp" />
		<converter></converter>
	</page>
	<page pageID="acjjdpdwceRptEE" path="acjjdpdwceRptEE.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjdpdwceRptEE.jsp" />
		<converter></converter>
	</page>
	<page pageID="acjjdpdwceW2" path="acjjdpdwceW2.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjdpdwceW2.jsp" />
		<converter></converter>
	</page>

	<page pageID="acjjdpdwceRpt2" path="acjjdpdwceRpt2.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjdpdwceRpt2.jsp" />
		<converter></converter>
	</page>

	<page pageID="acjj0601A" path="acjj0601A.jsp">
		<controller>com.icsc.ac.func.acjc0601Func</controller>
		<action flag="I" method="doInquire" forward="acjj0601A.jsp" />
		<action flag="N" method="doInsert" forward="acjj0601A.jsp" />
		<action flag="R" method="doUpdate" forward="acjj0601A.jsp" />
		<action flag="D" method="doDelete" forward="acjj0601A.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ac.dao.acjcbhVO"
				type="sequence" />
		</converter>
	</page>

	<page pageID="acjjCCCost" path="acjjCCCost.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjCCCost.jsp" />
		<converter></converter>
	</page>

	<page pageID="acjjCCDetailCost" path="acjjCCDetailCost.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjCCDetailCost.jsp" />
		<converter></converter>
	</page>

	<page pageID="acjjSelectOutput" path="acjjSelectOutput.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjSelectOutput.jsp" />
		<converter></converter>
	</page>

	<page pageID="acjjt1ref" path="acjjt1refSearchList.jsp">
		<controller>com.icsc.ac.func.acjct1refFunc</controller>
		<action flag="R" method="update"
			forward="acjjt1refSearchList.jsp" />
		<action flag="C" method="copy"
			forward="acjjt1refSearchList.jsp" />
		<converter>
			<valueObject objectID="v"
				class="com.icsc.ac.dao.acjct1refVO" type="sequence" />
		</converter>
	</page>

	<page pageID="acjjVarChk" path="acjjVarChk.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjVarChk.jsp" />
		<converter></converter>
	</page>


	<page pageID="acjjOrderList" path="acjjOrderListm.jsp">
		<controller>com.icsc.ac.func.amjcOrderListFunc</controller>
		<action flag="I" method="query" forward="acjjOrderListm.jsp" />
		<action flag="N" method="create" forward="acjjOrderListm.jsp" />
		<action flag="R" method="update" forward="acjjOrderListm.jsp" />
		<action flag="D" method="remove" forward="acjjOrderListm.jsp" />
		<action flag="U" method="load" forward="acjjOrderImport.jsp" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjctransferVO" type="sequence" />
		</converter>
	</page>

	<page pageID="acjjOrderList1" path="acjjOrderList1.jsp">
		<controller>com.icsc.ac.func.acjcOrderList1Func</controller>
		<action flag="I" method="query" />
		<action flag="C" method="change" />
		<converter>
			<valueObject objectID="v"
				class="com.icsc.ac.dao.acjcorderVO" type="sequence" />
		</converter>
	</page>

	<page pageID="acjjOrderList2" path="acjjOrderList2.jsp">
		<controller>com.icsc.ac.func.acjcOrderList2Func</controller>
		<action flag="I" method="query" />
		<action flag="C" method="change" />
		<converter>
			<valueObject objectID="v1"
				class="com.icsc.ac.dao.acjcorderVO" type="sequence" />
			<valueObject objectID="v2"
				class="com.icsc.ac.dao.acjcorderVO" type="sequence" />
		</converter>
	</page>

	<page pageID="acjjOrderList3" path="acjjOrderList3.jsp">
		<controller>com.icsc.ac.func.acjcOrderList3Func</controller>
		<action flag="I" method="query" />
		<action flag="G" method="genOrder" />
		<action flag="R" method="update" />
		<action flag="D" method="delete" />
		<converter>
			<valueObject objectID="v"
				class="com.icsc.ac.dao.acjctransferVO" type="sequence" />
		</converter>
	</page>
	
	<page pageID="acjjCostIndicatorList" path="acjjCostIndicatorList.jsp">	
		<controller>com.icsc.ac.func.acjcCostIndicatorListFunc</controller>
		<action flag="I" method="doQuery" forward="acjjCostIndicatorList.jsp"/>	
		<action flag="N" method="doCreate" forward="acjjCostIndicatorList.jsp"/>
		<action flag="R" method="doUpdate" forward="acjjCostIndicatorList.jsp"/>
		<action flag="D" method="doRemove" forward="acjjCostIndicatorList.jsp"/>
		<converter>
			<valueObject objectID="v1" 
					class="com.icsc.ac.dao.acjcCostIndicatorVO"
				 	type="sequence"/>      
		</converter>
	</page>
	
	<page pageID="acjjKICSRpt" path="acjjKICSRpt.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjKICSRpt.jsp" />
		<converter></converter>
	</page>
	
	<page pageID="acjjPcsCtAnlstRpt" path="acjjPcsCtAnlstRpt.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjPcsCtAnlstRpt.jsp" />
		<converter></converter>
	</page>
	
		<page pageID="acjj0202DCAA" path="acjj0202DCAA.jsp">
		<controller>com.icsc.ac.dca.func.acjc0202DCAFunc</controller>
		<action flag="I" method="doInquire" forward="acjj0202DCAA.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ac.dca.dao.acjct1dcaVO"
				type="unique" />
		</converter>
	</page>

	<page pageID="acjj0202DCAAList" path="acjj0202DCAAList.jsp">
		<controller>com.icsc.ac.dca.func.acjc0202DCAFunc</controller>
		<action flag="B" method="doBack" forward="acjj0202DCAAList.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ac.dca.dao.acjct1dcaVO"
				type="sequence" />
		</converter>
	</page>
	
	<page pageID="acjj0201DCAAList" path="acjj0201DCAAList.jsp">
		<controller>com.icsc.ac.dca.func.acjc0201DCAAFunc</controller>
			<action flag="I" method="query" forward="acjj0201DCAAList.jsp" />
			<action flag="D" method="doDelete" forward="acjj0201DCAAList.jsp" />
		<converter>
		</converter>
	</page>
	
	<page pageID="acjj0201DCAA" path="acjj0201DCAA.jsp">
		<controller>com.icsc.ac.dca.func.acjc0201DCAFunc</controller>
		<action flag="I" method="doInquire" validate="doInquire_validate" forward="acjj0201DCAA.jsp" />
		<action flag="N" method="doInsert" validate="doInsert_validate" 	forward="acjj0201DCAA.jsp" />
		<action flag="R" method="doUpdate" validate="doUpdate_validate" forward="acjj0201DCAA.jsp" />
		<action flag="D" method="doScrap" forward="acjj0201DCAA.jsp" />
		<converter>
			<valueObject objectID="v1" class="com.icsc.ac.dca.dao.acjct1dcaVO" 	type="unique" />
		</converter>
	</page>
	
	<page pageID="acjjAAtoAC" path="acjjAAtoAC.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjAAtoAC.jsp" />
		<converter></converter>
	</page>
	
	<page pageID="acjjPdCostRpt1" path="acjjPdCostRpt1.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjPdCostRpt1.jsp" />
		<converter></converter>
	</page>
	
	<page pageID="acjjPdExpDetail" path="acjjPdExpDetail.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjPdExpDetail.jsp" />
		<converter></converter>
	</page>

	<page pageID="acjjUAORptPara1" path="acjjUAORptPara1.jsp">
		<controller>com.icsc.facc.zaf.web.zafcBat001Func</controller>
		<action flag="R" method="doRun" forward="acjjUAORptPara1.jsp" />
		<converter></converter>
	</page>
	
</pages>

